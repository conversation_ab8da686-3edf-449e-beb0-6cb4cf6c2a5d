#include "../include/isoloc.h"

int main(int argc, char *argv[]) {
    // Initialiser GTK
    gtk_init(&argc, &argv);
    
    // Créer l'application
    IsolocApp *app = isoloc_app_new();
    if (!app) {
        g_print("Erreur: Impossible de créer l'application\n");
        return 1;
    }
    
    // Initialiser l'application
    isoloc_app_init(app);
    
    // Afficher la fenêtre principale
    gtk_widget_show_all(app->window);
    
    // Démarrer la boucle principale GTK
    gtk_main();
    
    // Nettoyer et quitter
    isoloc_app_destroy(app);
    
    return 0;
}

IsolocApp* isoloc_app_new(void) {
    IsolocApp *app = g_malloc(sizeof(IsolocApp));
    if (!app) {
        return NULL;
    }
    
    // Initialiser les pointeurs à NULL
    app->window = NULL;
    app->header_bar = NULL;
    app->main_stack = NULL;
    app->sidebar = NULL;
    app->content_area = NULL;
    app->db = NULL;
    
    return app;
}

void isoloc_app_init(IsolocApp *app) {
    // Initialiser la base de données
    if (init_database(app) != 0) {
        g_print("Erreur: Impossible d'initialiser la base de données\n");
        return;
    }
    
    // Créer l'interface utilisateur
    create_main_window(app);
    create_header_bar(app);
    create_sidebar(app);
    setup_main_content(app);
}

void isoloc_app_destroy(IsolocApp *app) {
    if (app) {
        close_database(app);
        g_free(app);
    }
}
