@echo off
REM Lanceur pour Isoloc - Application de Comptabilité

echo ============================================================
echo     ISOLOC - Application de Comptabilite Professionnelle
echo ============================================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Python n'est pas installe ou n'est pas dans le PATH
    echo.
    echo Veuillez installer Python depuis https://python.org
    echo Assurez-vous de cocher "Add Python to PATH" lors de l'installation
    echo.
    pause
    exit /b 1
)

echo Python detecte avec succes!
echo.

REM Créer le répertoire data s'il n'existe pas
if not exist "data" mkdir data

echo Demarrage de l'application...
echo.

REM Lancer l'application
python run_app.py

if %errorlevel% neq 0 (
    echo.
    echo ERREUR: L'application a rencontre un probleme
    echo.
    pause
    exit /b 1
)

echo.
echo Application fermee normalement.
pause
