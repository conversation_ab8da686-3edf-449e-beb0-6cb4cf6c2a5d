# 📋 قواعد إدارة العملاء في Isoloc

## 🎯 نظام الفئات والحقول المشروطة

### 📊 الفئات المتاحة:
- **Public** (عام)
- **Privé** (خاص)

### 📝 قواعد الحقول حسب الفئة:

#### 🟢 عند اختيار "Public" (عام):
- ✅ **ICE**: مطلوب وقابل للكتابة
- ✅ **IF**: مطلوب وقابل للكتابة
- ✅ **Adresse**: مطلوبة وقابلة للكتابة

#### 🔴 عند اختيار "Privé" (خاص):
- ❌ **ICE**: ظاهر لكن معطل وغير مطلوب
- ❌ **IF**: ظاهر لكن معطل وغير مطلوب
- ❌ **Adresse**: ظاهرة لكن معطلة وغير مطلوبة

## 🔢 نظام الترقيم التلقائي

### ✨ الميزات:
- **أكواد تلقائية**: C1, C2, C3, C4, C5...
- **ترقيم متسلسل**: لا توجد فجوات في التسلسل
- **إعادة ترتيب ذكية**: عند حذف عميل، يتم إعادة ترقيم جميع العملاء

### 📋 مثال على إعادة الترتيب:
```
قبل الحذف: C1, C2, C3, C4, C5
حذف C3: C1, C2, C4, C5
بعد إعادة الترتيب: C1, C2, C3, C4
```

## 📊 الحقول المطلوبة

### 🔴 حقول إجبارية لجميع العملاء:
1. **Code** - تلقائي (للقراءة فقط)
2. **Nom/Entreprise** - اسم العميل أو الشركة

### 🟡 حقول اختيارية لجميع العملاء:
3. **Personne à contacter** - اسم الشخص المسؤول
4. **Contact** - رقم الهاتف الأساسي
5. **N° Fix** - رقم الهاتف الثابت
6. **N° Fax** - رقم الفاكس
7. **Adresse électronique** - البريد الإلكتروني

### 🔵 حقول مشروطة (حسب الفئة):
8. **ICE** - مطلوب للعملاء العامين فقط
9. **IF** - مطلوب للعملاء العامين فقط
10. **Adresse** - مطلوبة للعملاء العامين فقط

## ⚠️ رسائل التحقق

### 🚨 رسائل الخطأ المحتملة:
- "Le nom est obligatoire!" - عند ترك حقل الاسم فارغاً
- "ICE est obligatoire pour les clients publics!" - عند ترك ICE فارغاً للعملاء العامين
- "IF est obligatoire pour les clients publics!" - عند ترك IF فارغاً للعملاء العامين
- "Adresse est obligatoire pour les clients publics!" - عند ترك العنوان فارغاً للعملاء العامين

## 🎨 الواجهة التفاعلية

### 🔄 التغيير التلقائي:
- عند تغيير الفئة من "Public" إلى "Privé":
  - تُعطل حقول ICE, IF, Adresse (تبقى ظاهرة لكن غير قابلة للتعديل)
  - تُمحى محتويات هذه الحقول تلقائياً
  - تصبح هذه الحقول غير مطلوبة للحفظ

- عند تغيير الفئة من "Privé" إلى "Public":
  - تُفعل حقول ICE, IF, Adresse (تصبح قابلة للتعديل)
  - تصبح هذه الحقول مطلوبة للحفظ

### 🎯 مثال عملي:

#### إضافة عميل عام (Public):
1. اختر "Public" من قائمة الفئة
2. املأ الاسم (مطلوب)
3. املأ ICE (مطلوب)
4. املأ IF (مطلوب)
5. املأ العنوان (مطلوب)
6. املأ باقي الحقول (اختياري)
7. احفظ

#### إضافة عميل خاص (Privé):
1. اختر "Privé" من قائمة الفئة
2. املأ الاسم (مطلوب)
3. حقول ICE, IF, العنوان ستكون معطلة تلقائياً
4. املأ باقي الحقول (اختياري)
5. احفظ

## 📊 البيانات التجريبية

### العملاء المُدرجين مسبقاً:
- **C1**: Dupont Jean (Public) - مع ICE, IF, Adresse
- **C2**: Martin Marie (Public) - مع ICE, IF, Adresse  
- **C3**: Société ABC (Privé) - بدون ICE, IF, Adresse
- **C4**: Entreprise XYZ (Privé) - بدون ICE, IF, Adresse

## 🔧 استكشاف الأخطاء

### ❓ مشاكل شائعة:
1. **لا يمكن الكتابة في ICE/IF/Adresse**: تأكد من اختيار "Public"
2. **رسالة خطأ عند الحفظ**: تأكد من ملء جميع الحقول المطلوبة
3. **الكود لا يتغير**: الكود تلقائي ولا يمكن تعديله يدوياً

### ✅ نصائح للاستخدام:
- اختر الفئة أولاً قبل ملء باقي البيانات
- تأكد من ملء جميع الحقول المطلوبة قبل الحفظ
- استخدم البحث للعثور على العملاء بسرعة
- استخدم الكليك المزدوج للتعديل السريع

---

**Isoloc** - نظام محاسبة احترافي مع إدارة عملاء ذكية
