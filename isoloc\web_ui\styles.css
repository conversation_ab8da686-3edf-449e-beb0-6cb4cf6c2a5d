/* Variables CSS */
:root {
    --primary-color: #4F46E5;
    --primary-dark: #3730A3;
    --secondary-color: #6B7280;
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --danger-color: #EF4444;
    --background-color: #F9FAFB;
    --surface-color: #FFFFFF;
    --text-primary: #111827;
    --text-secondary: #6B7280;
    --border-color: #E5E7EB;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Header */
.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 32px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    font-size: 24px;
}

.header-nav {
    display: flex;
    gap: 4px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
}

.nav-btn:hover {
    background: #F3F4F6;
    color: var(--text-primary);
}

.nav-btn.active {
    background: var(--primary-color);
    color: white;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-btn {
    position: relative;
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.2s;
}

.header-btn:hover {
    background: #F3F4F6;
    color: var(--text-primary);
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--danger-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s;
}

.user-profile:hover {
    background: #F3F4F6;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
}

/* Contenu principal */
.main-content {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.page-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* Boutons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: white;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: #F9FAFB;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.2s;
}

.btn-icon:hover {
    background: #F3F4F6;
    color: var(--text-primary);
}

/* Cartes */
.card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.card-content {
    padding: 24px;
}

/* Grille de statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-card.revenue .stat-icon {
    background: var(--success-color);
}

.stat-card.invoices .stat-icon {
    background: var(--warning-color);
}

.stat-card.clients .stat-icon {
    background: var(--primary-color);
}

.stat-card.expenses .stat-icon {
    background: var(--danger-color);
}

.stat-content h3 {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change.warning {
    color: var(--warning-color);
}

/* Grille du tableau de bord */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

.chart-placeholder {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #F9FAFB;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
}

/* Liste des factures */
.invoice-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.invoice-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #F3F4F6;
}

.invoice-item:last-child {
    border-bottom: none;
}

.invoice-info {
    flex: 1;
}

.invoice-number {
    font-weight: 600;
    color: var(--text-primary);
}

.invoice-client {
    font-size: 14px;
    color: var(--text-secondary);
}

.invoice-amount {
    font-weight: 600;
    margin-right: 16px;
}

.invoice-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-paid {
    background: #D1FAE5;
    color: #065F46;
}

.status-pending {
    background: #FEF3C7;
    color: #92400E;
}

.status-draft {
    background: #E5E7EB;
    color: #374151;
}

/* Barre de recherche */
.search-bar {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-bar i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-bar input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Filtres */
.filters {
    display: flex;
    gap: 12px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    background: white;
}

/* Tableau */
.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    text-align: left;
    padding: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
}

.data-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #F3F4F6;
}

.client-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.client-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.client-name {
    font-weight: 600;
    color: var(--text-primary);
}

.client-company {
    font-size: 14px;
    color: var(--text-secondary);
}

.amount {
    font-weight: 600;
}

.amount.positive {
    color: var(--success-color);
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.link:hover {
    text-decoration: underline;
}

/* Responsive */
@media (max-width: 768px) {
    .header {
        padding: 0 16px;
    }
    
    .header-nav {
        display: none;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}
