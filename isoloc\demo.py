#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ISOLOC - Application de Comptabilité Professionnelle
Version Demo Python (pour test rapide)
"""

import os
import sys
from datetime import datetime

class Client:
    def __init__(self, id, nom, prenom="", email="", telephone="", solde=0.0):
        self.id = id
        self.nom = nom
        self.prenom = prenom
        self.email = email
        self.telephone = telephone
        self.solde = solde

class Facture:
    def __init__(self, id, numero, client_id, date, montant_ht, tva=20.0, statut="En attente"):
        self.id = id
        self.numero = numero
        self.client_id = client_id
        self.date = date
        self.montant_ht = montant_ht
        self.tva = tva
        self.montant_ttc = montant_ht * (1 + tva / 100)
        self.statut = statut

class IsolocApp:
    def __init__(self):
        self.clients = []
        self.factures = []
        self.initialiser_donnees_test()
    
    def initialiser_donnees_test(self):
        """Initialise l'application avec des données de test"""
        # Clients de test
        self.clients = [
            Client(1, "<PERSON><PERSON>", "<PERSON>", "<EMAIL>", "01 23 45 67 89", 1250.50),
            Client(2, "Martin", "Marie", "<EMAIL>", "01 98 76 54 32", 890.00),
            Client(3, "Société ABC", "", "<EMAIL>", "01 11 22 33 44", 2100.75),
            Client(4, "Entreprise XYZ", "", "<EMAIL>", "01 55 66 77 88", 0.00)
        ]
        
        # Factures de test
        self.factures = [
            Facture(1, "FAC-2024-001", 1, "2024-01-15", 1041.67, 20.0, "Payée"),
            Facture(2, "FAC-2024-002", 2, "2024-01-20", 741.67, 20.0, "En attente"),
            Facture(3, "FAC-2024-003", 3, "2024-01-25", 1750.00, 20.0, "En attente"),
            Facture(4, "FAC-2024-004", 1, "2024-02-01", 625.00, 20.0, "Brouillon")
        ]
    
    def clear_screen(self):
        """Nettoie l'écran"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def afficher_header(self):
        """Affiche l'en-tête de l'application"""
        print("=" * 60)
        print("    ISOLOC - Application de Comptabilité Professionnelle")
        print("                Version Demo Python")
        print("=" * 60)
        print()
    
    def afficher_menu_principal(self):
        """Affiche le menu principal"""
        print("=== MENU PRINCIPAL ===")
        print("1. 📊 Tableau de bord")
        print("2. 👥 Gestion des clients")
        print("3. 🧾 Gestion des factures")
        print("4. ➕ Ajouter un client")
        print("5. ➕ Ajouter une facture")
        print("6. 💰 Calculateur de TVA")
        print("0. ❌ Quitter")
        print("=" * 23)
    
    def afficher_tableau_bord(self):
        """Affiche le tableau de bord avec les statistiques"""
        print("\n📊 === TABLEAU DE BORD ===")
        print()
        
        # Statistiques générales
        nb_clients = len(self.clients)
        nb_factures = len(self.factures)
        
        # Calcul du chiffre d'affaires
        ca_total = sum(f.montant_ttc for f in self.factures if f.statut == "Payée")
        
        # Factures en attente
        factures_attente = len([f for f in self.factures if f.statut == "En attente"])
        
        # Factures en retard (simulation)
        factures_retard = len([f for f in self.factures if f.statut == "En retard"])
        
        print(f"📈 Chiffre d'affaires:     {ca_total:>10.2f} €")
        print(f"👥 Nombre de clients:      {nb_clients:>10}")
        print(f"🧾 Total des factures:     {nb_factures:>10}")
        print(f"⏳ Factures en attente:    {factures_attente:>10}")
        print(f"🚨 Factures en retard:     {factures_retard:>10}")
        print()
        
        # Dernières factures
        print("🕒 Dernières factures:")
        print("-" * 50)
        for facture in self.factures[-3:]:
            client = next((c for c in self.clients if c.id == facture.client_id), None)
            client_nom = client.nom if client else "Client inconnu"
            print(f"{facture.numero:<15} | {client_nom:<15} | {facture.montant_ttc:>8.2f} €")
        print("=" * 27)
    
    def afficher_clients(self):
        """Affiche la liste des clients"""
        print("\n👥 === LISTE DES CLIENTS ===")
        print()
        print(f"{'ID':<3} | {'Nom':<20} | {'Email':<25} | {'Solde':<10}")
        print("-" * 65)
        
        for client in self.clients:
            nom_complet = f"{client.nom} {client.prenom}".strip()
            print(f"{client.id:<3} | {nom_complet:<20} | {client.email:<25} | {client.solde:>8.2f} €")
        
        print("=" * 30)
    
    def afficher_factures(self):
        """Affiche la liste des factures"""
        print("\n🧾 === LISTE DES FACTURES ===")
        print()
        print(f"{'Numéro':<15} | {'Client':<15} | {'Date':<12} | {'Montant TTC':<12} | {'Statut':<12}")
        print("-" * 80)
        
        for facture in self.factures:
            client = next((c for c in self.clients if c.id == facture.client_id), None)
            client_nom = client.nom if client else "Inconnu"
            
            print(f"{facture.numero:<15} | {client_nom:<15} | {facture.date:<12} | {facture.montant_ttc:>10.2f} € | {facture.statut:<12}")
        
        print("=" * 33)
    
    def ajouter_client(self):
        """Ajoute un nouveau client"""
        print("\n➕ === AJOUTER UN CLIENT ===")
        print()
        
        try:
            nom = input("Nom: ").strip()
            if not nom:
                print("❌ Le nom est obligatoire!")
                return
            
            prenom = input("Prénom: ").strip()
            email = input("Email: ").strip()
            telephone = input("Téléphone: ").strip()
            
            # Générer un nouvel ID
            nouvel_id = max([c.id for c in self.clients], default=0) + 1
            
            # Créer le nouveau client
            nouveau_client = Client(nouvel_id, nom, prenom, email, telephone)
            self.clients.append(nouveau_client)
            
            print(f"✅ Client ajouté avec succès! (ID: {nouvel_id})")
            
        except KeyboardInterrupt:
            print("\n❌ Opération annulée.")
    
    def ajouter_facture(self):
        """Ajoute une nouvelle facture"""
        print("\n➕ === AJOUTER UNE FACTURE ===")
        print()
        
        try:
            # Afficher les clients disponibles
            print("Clients disponibles:")
            for client in self.clients:
                nom_complet = f"{client.nom} {client.prenom}".strip()
                print(f"  {client.id}. {nom_complet}")
            print()
            
            numero = input("Numéro de facture: ").strip()
            if not numero:
                print("❌ Le numéro de facture est obligatoire!")
                return
            
            client_id = int(input("ID du client: "))
            if not any(c.id == client_id for c in self.clients):
                print("❌ Client introuvable!")
                return
            
            date = input("Date (YYYY-MM-DD) [aujourd'hui]: ").strip()
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            montant_ht = float(input("Montant HT: "))
            if montant_ht <= 0:
                print("❌ Le montant doit être positif!")
                return
            
            # Générer un nouvel ID
            nouvel_id = max([f.id for f in self.factures], default=0) + 1
            
            # Créer la nouvelle facture
            nouvelle_facture = Facture(nouvel_id, numero, client_id, date, montant_ht)
            self.factures.append(nouvelle_facture)
            
            print(f"✅ Facture ajoutée avec succès!")
            print(f"   Montant HT: {montant_ht:.2f} €")
            print(f"   TVA (20%): {nouvelle_facture.montant_ttc - montant_ht:.2f} €")
            print(f"   Montant TTC: {nouvelle_facture.montant_ttc:.2f} €")
            
        except (ValueError, KeyboardInterrupt):
            print("\n❌ Erreur dans la saisie ou opération annulée.")
    
    def calculateur_tva(self):
        """Calculateur de TVA"""
        print("\n💰 === CALCULATEUR DE TVA ===")
        print()
        
        try:
            print("1. Calculer TTC à partir de HT")
            print("2. Calculer HT à partir de TTC")
            choix = input("Votre choix (1 ou 2): ").strip()
            
            if choix == "1":
                montant_ht = float(input("Montant HT: "))
                tva = float(input("Taux de TVA (%) [20]: ") or "20")
                montant_ttc = montant_ht * (1 + tva / 100)
                montant_tva = montant_ttc - montant_ht
                
                print(f"\n📊 Résultat:")
                print(f"   Montant HT:  {montant_ht:>10.2f} €")
                print(f"   TVA ({tva}%):   {montant_tva:>10.2f} €")
                print(f"   Montant TTC: {montant_ttc:>10.2f} €")
                
            elif choix == "2":
                montant_ttc = float(input("Montant TTC: "))
                tva = float(input("Taux de TVA (%) [20]: ") or "20")
                montant_ht = montant_ttc / (1 + tva / 100)
                montant_tva = montant_ttc - montant_ht
                
                print(f"\n📊 Résultat:")
                print(f"   Montant TTC: {montant_ttc:>10.2f} €")
                print(f"   TVA ({tva}%):   {montant_tva:>10.2f} €")
                print(f"   Montant HT:  {montant_ht:>10.2f} €")
            else:
                print("❌ Choix invalide!")
                
        except (ValueError, KeyboardInterrupt):
            print("\n❌ Erreur dans la saisie ou opération annulée.")
    
    def run(self):
        """Lance l'application"""
        while True:
            self.clear_screen()
            self.afficher_header()
            self.afficher_menu_principal()
            
            try:
                choix = input("\nVotre choix: ").strip()
                
                if choix == "1":
                    self.afficher_tableau_bord()
                elif choix == "2":
                    self.afficher_clients()
                elif choix == "3":
                    self.afficher_factures()
                elif choix == "4":
                    self.ajouter_client()
                elif choix == "5":
                    self.ajouter_facture()
                elif choix == "6":
                    self.calculateur_tva()
                elif choix == "0":
                    print("\n👋 Merci d'avoir utilisé Isoloc!")
                    print("💡 Note: Cette version demo ne sauvegarde pas les données.")
                    break
                else:
                    print("\n❌ Choix invalide! Veuillez réessayer.")
                
                if choix != "0":
                    input("\n⏎ Appuyez sur Entrée pour continuer...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Au revoir!")
                break

if __name__ == "__main__":
    app = IsolocApp()
    app.run()
