#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Serveur web simple pour tester l'interface Isoloc
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class IsolocHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Handler personnalisé pour servir les fichiers statiques"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent), **kwargs)
    
    def end_headers(self):
        # Ajouter les en-têtes CORS pour le développement
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        # Personnaliser les logs
        print(f"[{self.address_string()}] {format % args}")

def start_server(port=8080):
    """Démarre le serveur web"""
    
    # Changer vers le répertoire de l'interface web
    web_dir = Path(__file__).parent
    os.chdir(web_dir)
    
    print("=" * 60)
    print("    ISOLOC - Interface Web de Démonstration")
    print("=" * 60)
    print()
    print(f"📁 Répertoire: {web_dir}")
    print(f"🌐 Port: {port}")
    print(f"🔗 URL: http://localhost:{port}")
    print()
    
    try:
        # Créer le serveur
        with socketserver.TCPServer(("", port), IsolocHTTPRequestHandler) as httpd:
            print(f"✅ Serveur démarré sur le port {port}")
            print("📱 Ouverture automatique du navigateur...")
            print()
            print("Pour arrêter le serveur, appuyez sur Ctrl+C")
            print("-" * 60)
            
            # Ouvrir automatiquement le navigateur
            webbrowser.open(f'http://localhost:{port}')
            
            # Démarrer le serveur
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n")
        print("🛑 Arrêt du serveur...")
        print("👋 Merci d'avoir utilisé Isoloc!")
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Erreur: Le port {port} est déjà utilisé.")
            print(f"💡 Essayez avec un autre port: python server.py {port + 1}")
        else:
            print(f"❌ Erreur: {e}")
        sys.exit(1)

def main():
    """Point d'entrée principal"""
    
    # Vérifier les arguments de ligne de commande
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ Erreur: Le port doit être un nombre entier.")
            print("💡 Usage: python server.py [port]")
            sys.exit(1)
    
    # Vérifier que les fichiers nécessaires existent
    required_files = ['index.html', 'styles.css', 'script.js']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Erreur: Fichiers manquants:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 Assurez-vous d'être dans le bon répertoire.")
        sys.exit(1)
    
    # Démarrer le serveur
    start_server(port)

if __name__ == "__main__":
    main()
