# Makefile pour Isoloc - Application de Comptabilité

# Compilateur et flags
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
LIBS = `pkg-config --cflags --libs gtk+-3.0 sqlite3`

# Répertoires
SRCDIR = src
INCDIR = include
OBJDIR = obj
BINDIR = bin

# Nom de l'exécutable
TARGET = isoloc

# Fichiers sources
SOURCES = $(SRCDIR)/main.c \
          $(SRCDIR)/ui/main_window.c \
          $(SRCDIR)/ui/sidebar.c \
          $(SRCDIR)/ui/dashboard.c \
          $(SRCDIR)/ui/pages.c \
          $(SRCDIR)/db/database.c

# Fichiers objets
OBJECTS = $(SOURCES:$(SRCDIR)/%.c=$(OBJDIR)/%.o)

# Règle par défaut
all: directories $(BINDIR)/$(TARGET)

# Créer les répertoires nécessaires
directories:
	@mkdir -p $(OBJDIR)
	@mkdir -p $(OBJDIR)/ui
	@mkdir -p $(OBJDIR)/db
	@mkdir -p $(OBJDIR)/utils
	@mkdir -p $(BINDIR)
	@mkdir -p data

# Compilation de l'exécutable
$(BINDIR)/$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $@ $(LIBS)
	@echo "Compilation terminée: $(BINDIR)/$(TARGET)"

# Compilation des fichiers objets
$(OBJDIR)/%.o: $(SRCDIR)/%.c
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) -I$(INCDIR) -c $< -o $@ `pkg-config --cflags gtk+-3.0 sqlite3`

# Nettoyage
clean:
	rm -rf $(OBJDIR)
	rm -rf $(BINDIR)
	@echo "Nettoyage terminé"

# Installation (optionnel)
install: $(BINDIR)/$(TARGET)
	cp $(BINDIR)/$(TARGET) /usr/local/bin/
	@echo "Installation terminée"

# Désinstallation
uninstall:
	rm -f /usr/local/bin/$(TARGET)
	@echo "Désinstallation terminée"

# Exécution
run: $(BINDIR)/$(TARGET)
	./$(BINDIR)/$(TARGET)

# Vérification des dépendances
check-deps:
	@echo "Vérification des dépendances..."
	@pkg-config --exists gtk+-3.0 || (echo "GTK+ 3.0 non trouvé" && exit 1)
	@pkg-config --exists sqlite3 || (echo "SQLite3 non trouvé" && exit 1)
	@echo "Toutes les dépendances sont présentes"

# Aide
help:
	@echo "Commandes disponibles:"
	@echo "  make          - Compiler l'application"
	@echo "  make clean    - Nettoyer les fichiers compilés"
	@echo "  make run      - Compiler et exécuter l'application"
	@echo "  make install  - Installer l'application système"
	@echo "  make check-deps - Vérifier les dépendances"
	@echo "  make help     - Afficher cette aide"

.PHONY: all clean install uninstall run help directories check-deps
