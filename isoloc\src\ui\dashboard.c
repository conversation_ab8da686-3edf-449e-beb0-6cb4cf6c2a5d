#include "../../include/isoloc.h"

GtkWidget* create_dashboard_page(IsolocApp *app) {
    // Conteneur principal du tableau de bord
    GtkWidget *dashboard_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 20);
    gtk_widget_set_margin_start(dashboard_box, 20);
    gtk_widget_set_margin_end(dashboard_box, 20);
    gtk_widget_set_margin_top(dashboard_box, 20);
    gtk_widget_set_margin_bottom(dashboard_box, 20);
    
    // Titre du tableau de bord
    GtkWidget *title = gtk_label_new("Tableau de Bord");
    gtk_style_context_add_class(gtk_widget_get_style_context(title), "title-1");
    gtk_widget_set_halign(title, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(dashboard_box), title, FALSE, FALSE, 0);
    
    // Conteneur pour les cartes de statistiques
    GtkWidget *stats_grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(stats_grid), 15);
    gtk_grid_set_column_spacing(GTK_GRID(stats_grid), 15);
    gtk_box_pack_start(GTK_BOX(dashboard_box), stats_grid, FALSE, FALSE, 0);
    
    // Carte 1: Chiffre d'affaires
    GtkWidget *card1 = create_stat_card("Chiffre d'Affaires", "€ 45,230.50", "↗ +12.5%", "green");
    gtk_grid_attach(GTK_GRID(stats_grid), card1, 0, 0, 1, 1);
    
    // Carte 2: Factures en attente
    GtkWidget *card2 = create_stat_card("Factures en Attente", "8", "2 en retard", "orange");
    gtk_grid_attach(GTK_GRID(stats_grid), card2, 1, 0, 1, 1);
    
    // Carte 3: Clients actifs
    GtkWidget *card3 = create_stat_card("Clients Actifs", "24", "↗ +3 ce mois", "blue");
    gtk_grid_attach(GTK_GRID(stats_grid), card3, 2, 0, 1, 1);
    
    // Carte 4: Dépenses du mois
    GtkWidget *card4 = create_stat_card("Dépenses du Mois", "€ 8,450.00", "↘ -5.2%", "red");
    gtk_grid_attach(GTK_GRID(stats_grid), card4, 3, 0, 1, 1);
    
    // Section des graphiques et listes
    GtkWidget *content_grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(content_grid), 20);
    gtk_grid_set_column_spacing(GTK_GRID(content_grid), 20);
    gtk_box_pack_start(GTK_BOX(dashboard_box), content_grid, TRUE, TRUE, 0);
    
    // Graphique des revenus (placeholder)
    GtkWidget *chart_frame = gtk_frame_new("Évolution des Revenus");
    gtk_widget_set_size_request(chart_frame, 400, 300);
    GtkWidget *chart_placeholder = gtk_label_new("Graphique des revenus\n(À implémenter)");
    gtk_widget_set_halign(chart_placeholder, GTK_ALIGN_CENTER);
    gtk_widget_set_valign(chart_placeholder, GTK_ALIGN_CENTER);
    gtk_container_add(GTK_CONTAINER(chart_frame), chart_placeholder);
    gtk_grid_attach(GTK_GRID(content_grid), chart_frame, 0, 0, 1, 1);
    
    // Liste des dernières factures
    GtkWidget *recent_frame = gtk_frame_new("Dernières Factures");
    gtk_widget_set_size_request(recent_frame, 400, 300);
    GtkWidget *recent_list = create_recent_invoices_list(app);
    gtk_container_add(GTK_CONTAINER(recent_frame), recent_list);
    gtk_grid_attach(GTK_GRID(content_grid), recent_frame, 1, 0, 1, 1);
    
    return dashboard_box;
}

GtkWidget* create_stat_card(const char *title, const char *value, const char *change, const char *color) {
    // Créer le frame pour la carte
    GtkWidget *frame = gtk_frame_new(NULL);
    gtk_widget_set_size_request(frame, 200, 120);
    
    // Conteneur de la carte
    GtkWidget *card_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 5);
    gtk_widget_set_margin_start(card_box, 15);
    gtk_widget_set_margin_end(card_box, 15);
    gtk_widget_set_margin_top(card_box, 15);
    gtk_widget_set_margin_bottom(card_box, 15);
    
    // Titre de la carte
    GtkWidget *title_label = gtk_label_new(title);
    gtk_style_context_add_class(gtk_widget_get_style_context(title_label), "caption");
    gtk_widget_set_halign(title_label, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(card_box), title_label, FALSE, FALSE, 0);
    
    // Valeur principale
    GtkWidget *value_label = gtk_label_new(value);
    gtk_style_context_add_class(gtk_widget_get_style_context(value_label), "title-2");
    gtk_widget_set_halign(value_label, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(card_box), value_label, FALSE, FALSE, 0);
    
    // Changement/évolution
    GtkWidget *change_label = gtk_label_new(change);
    gtk_style_context_add_class(gtk_widget_get_style_context(change_label), "caption");
    gtk_widget_set_halign(change_label, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(card_box), change_label, FALSE, FALSE, 0);
    
    gtk_container_add(GTK_CONTAINER(frame), card_box);
    
    return frame;
}

GtkWidget* create_recent_invoices_list(IsolocApp *app) {
    // Créer une liste scrollable
    GtkWidget *scrolled = gtk_scrolled_window_new(NULL, NULL);
    gtk_scrolled_window_set_policy(GTK_SCROLLED_WINDOW(scrolled), 
                                  GTK_POLICY_AUTOMATIC, GTK_POLICY_AUTOMATIC);
    
    // Créer la liste
    GtkWidget *listbox = gtk_list_box_new();
    gtk_container_add(GTK_CONTAINER(scrolled), listbox);
    
    // Ajouter quelques exemples de factures
    const char *invoices[][3] = {
        {"FAC-2024-001", "Client ABC", "€ 1,250.00"},
        {"FAC-2024-002", "Société XYZ", "€ 890.50"},
        {"FAC-2024-003", "Entreprise 123", "€ 2,100.00"},
        {"FAC-2024-004", "Client DEF", "€ 750.25"},
        {NULL, NULL, NULL}
    };
    
    for (int i = 0; invoices[i][0] != NULL; i++) {
        GtkWidget *row = gtk_list_box_row_new();
        GtkWidget *box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 10);
        gtk_widget_set_margin_start(box, 10);
        gtk_widget_set_margin_end(box, 10);
        gtk_widget_set_margin_top(box, 5);
        gtk_widget_set_margin_bottom(box, 5);
        
        GtkWidget *num_label = gtk_label_new(invoices[i][0]);
        gtk_widget_set_size_request(num_label, 100, -1);
        gtk_widget_set_halign(num_label, GTK_ALIGN_START);
        gtk_box_pack_start(GTK_BOX(box), num_label, FALSE, FALSE, 0);
        
        GtkWidget *client_label = gtk_label_new(invoices[i][1]);
        gtk_widget_set_halign(client_label, GTK_ALIGN_START);
        gtk_box_pack_start(GTK_BOX(box), client_label, TRUE, TRUE, 0);
        
        GtkWidget *amount_label = gtk_label_new(invoices[i][2]);
        gtk_widget_set_halign(amount_label, GTK_ALIGN_END);
        gtk_style_context_add_class(gtk_widget_get_style_context(amount_label), "monospace");
        gtk_box_pack_start(GTK_BOX(box), amount_label, FALSE, FALSE, 0);
        
        gtk_container_add(GTK_CONTAINER(row), box);
        gtk_list_box_insert(GTK_LIST_BOX(listbox), row, -1);
    }
    
    return scrolled;
}
