# Isoloc - Application de Comptabilité

Application de comptabilité professionnelle avec interface graphique moderne développée en Python avec Tkinter.

## 🚀 Fonctionnalités

### ✅ Implémentées

#### 📊 Tableau de Bord
- **Statistiques en temps réel** : <PERSON><PERSON><PERSON> d'affaires, nombre de clients, factures, dépenses
- **Cartes colorées** avec indicateurs visuels
- **Liste des dernières factures** avec statuts
- **Actions rapides** pour navigation et export

#### 👥 Gestion des Clients
- **Système de codes automatique** : C1, C2, C3... (réorganisation automatique après suppression)
- **Formulaire complet** avec tous les champs requis
- **Catégories** : Public ou Privé avec champs conditionnels
- **Champs obligatoires** pour clients privés (ICE, IF, Adresse)
- **Recherche avancée** par code, nom, email ou contact
- **Actions complètes** : <PERSON><PERSON><PERSON>, Modi<PERSON>r, Voir détails, Supprimer
- **Interface intuitive** avec validation des données

#### 💾 Base de Données
- **SQLite intégrée** pour stockage local
- **Données d'exemple** automatiquement créées
- **Sauvegarde automatique** des modifications
- **Structure relationnelle** optimisée

### 🚧 En Développement

#### 📄 Gestion des Factures
- Création de nouvelles factures
- Modification et suppression
- Gestion des statuts et échéances
- Calcul automatique TVA

#### 💸 Gestion des Dépenses
- Enregistrement des dépenses
- Catégorisation
- Suivi par fournisseur
- Rapports de dépenses

#### 📈 Rapports Financiers
- Génération de rapports PDF
- Graphiques et statistiques
- Export vers Excel/CSV
- Bilan et compte de résultat

## 🛠️ Installation et Utilisation

### Prérequis
- **Python 3.7+** installé sur votre système
- **Tkinter** (inclus avec Python par défaut)
- **SQLite3** (inclus avec Python par défaut)

### Installation

1. **Téléchargez** les fichiers du projet
2. **Placez-les** dans un dossier de votre choix
3. **Ouvrez** un terminal dans ce dossier

### Lancement

#### Méthode 1: Script Python
```bash
python run_app.py
```

#### Méthode 2: Fichier Batch (Windows)
```bash
run_isoloc.bat
```

#### Méthode 3: Directement
```bash
cd app
python main.py
```

## 📱 Interface Utilisateur

### Design Moderne
- **Thème professionnel** avec couleurs harmonieuses
- **Onglets intuitifs** pour navigation
- **Icônes emoji** pour identification rapide
- **Interface responsive** qui s'adapte à la taille

### Navigation
- **📊 Tableau de Bord** : Vue d'ensemble et statistiques
- **👥 Clients** : Gestion complète de la clientèle
- **📄 Factures** : Création et suivi des factures
- **💸 Dépenses** : Enregistrement des dépenses
- **📈 Rapports** : Génération de rapports financiers

### Couleurs et Codes
- **🟢 Vert** : Revenus, soldes positifs, succès
- **🔵 Bleu** : Informations, clients, navigation
- **🟠 Orange** : Alertes, factures en attente
- **🔴 Rouge** : Dépenses, soldes négatifs, erreurs

## 💾 Structure de la Base de Données

### Tables Principales

#### `clients`
- `id` : Identifiant unique auto-incrémenté
- `code` : Code client automatique (C1, C2, C3...)
- `nom` : Nom du client/entreprise
- `categorie` : Public ou Privé
- `ice` : Numéro ICE (obligatoire pour Privé)
- `if_field` : Numéro IF (obligatoire pour Privé)
- `adresse` : Adresse complète (obligatoire pour Privé)
- `personne_contact` : Nom de la personne à contacter
- `contact` : Numéro de téléphone principal
- `n_fix` : Numéro de téléphone fixe
- `n_fax` : Numéro de fax
- `adresse_electronique` : Adresse email
- `solde` : Solde actuel du client
- `date_creation` : Date de création automatique

#### `factures`
- `id` : Identifiant unique
- `numero` : Numéro de facture
- `client_id` : Référence au client
- `date_facture` : Date d'émission
- `date_echeance` : Date d'échéance
- `montant_ht` : Montant hors taxes
- `tva` : Taux de TVA
- `montant_ttc` : Montant toutes taxes comprises
- `statut` : Statut (Brouillon, En attente, Payée, En retard)
- `notes` : Notes additionnelles

#### `depenses`
- `id` : Identifiant unique
- `description` : Description de la dépense
- `montant` : Montant de la dépense
- `categorie` : Catégorie de dépense
- `date_depense` : Date de la dépense
- `fournisseur` : Nom du fournisseur
- `notes` : Notes additionnelles

## 🎨 Personnalisation

### Modifier les Couleurs
Éditez les couleurs dans la méthode `setup_styles()` :

```python
# Couleurs personnalisées
style.configure('Title.TLabel', foreground='#votre-couleur')
```

### Ajouter des Fonctionnalités
1. Créez une nouvelle méthode dans la classe `IsolocApp`
2. Ajoutez un bouton ou menu pour l'appeler
3. Implémentez la logique métier

### Modifier la Base de Données
1. Ajoutez les nouvelles tables dans `create_tables()`
2. Mettez à jour les données d'exemple si nécessaire
3. Créez les méthodes de gestion correspondantes

## 🔧 Dépannage

### Erreurs Communes

#### "Python n'est pas reconnu"
- Installez Python depuis [python.org](https://python.org)
- Cochez "Add Python to PATH" lors de l'installation

#### "Tkinter non trouvé"
- Tkinter est inclus avec Python par défaut
- Sur Linux : `sudo apt-get install python3-tk`

#### "Base de données verrouillée"
- Fermez toutes les instances de l'application
- Supprimez le fichier `data/isoloc.db` pour recommencer

#### Interface qui ne s'affiche pas
- Vérifiez que votre système supporte les interfaces graphiques
- Essayez de lancer depuis un terminal pour voir les erreurs

## 📊 Données d'Exemple

L'application crée automatiquement des données d'exemple :

- **4 clients** avec informations complètes
- **4 factures** avec différents statuts
- **4 dépenses** dans diverses catégories

Ces données permettent de tester immédiatement toutes les fonctionnalités.

## 🎯 Fonctionnalités Avancées

### 🔢 Système de Codes Automatique
- **Génération automatique** : Les codes clients sont générés automatiquement (C1, C2, C3...)
- **Réorganisation intelligente** : Après suppression d'un client, tous les codes sont réorganisés
- **Pas de doublons** : Le système garantit l'unicité des codes
- **Lecture seule** : Les codes ne peuvent pas être modifiés manuellement

### 📋 Formulaire Intelligent
- **Champs conditionnels** : ICE, IF et Adresse deviennent obligatoires pour les clients "Privé"
- **Validation en temps réel** : Vérification des données avant sauvegarde
- **Interface adaptive** : Les champs se désactivent/activent selon la catégorie
- **Messages d'erreur clairs** : Indications précises en cas de problème

### 🔍 Recherche Avancée
- **Recherche multi-critères** : Code, nom, email, contact
- **Résultats instantanés** : Filtrage en temps réel pendant la saisie
- **Mise en surbrillance** : Résultats colorés selon le solde
- **Navigation rapide** : Double-clic pour modification directe

### 📊 Affichage Optimisé
- **Colonnes adaptées** : Affichage de toutes les informations importantes
- **Codes couleur** : Vert pour soldes positifs, rouge pour négatifs
- **Tri automatique** : Classement par code client
- **Interface responsive** : Adaptation à la taille de l'écran

### ⚡ Actions Rapides
- **Menu contextuel** : Clic droit pour accéder aux actions
- **Raccourcis clavier** : Double-clic pour modification
- **Boutons intuitifs** : Actions clairement identifiées
- **Confirmations** : Demande de confirmation pour les suppressions

## 🚀 Prochaines Versions

### Version 2.0
- Interface de création/modification des clients
- Gestion complète des factures
- Calculs automatiques de TVA
- Impression et export PDF

### Version 3.0
- Graphiques avancés avec matplotlib
- Sauvegarde cloud
- Multi-utilisateurs
- Synchronisation mobile

## 📞 Support

Pour toute question ou problème :

1. Vérifiez cette documentation
2. Consultez les messages d'erreur dans le terminal
3. Assurez-vous d'avoir la dernière version de Python

---

**Isoloc** - Comptabilité Professionnelle Simplifiée
