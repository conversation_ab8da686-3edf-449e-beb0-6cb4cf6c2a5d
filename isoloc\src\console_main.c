#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sqlite3.h>

// Structure pour les clients
typedef struct {
    int id;
    char nom[100];
    char prenom[100];
    char email[100];
    char telephone[20];
    char adresse[200];
    double solde;
} Client;

// Structure pour les factures
typedef struct {
    int id;
    int client_id;
    char numero[50];
    char date[20];
    double montant_ht;
    double tva;
    double montant_ttc;
    char statut[20];
} Facture;

// Fonctions de base de données
int init_console_database(sqlite3 **db);
void close_console_database(sqlite3 *db);
void afficher_menu_principal();
void afficher_clients(sqlite3 *db);
void afficher_factures(sqlite3 *db);
void afficher_statistiques(sqlite3 *db);
void ajouter_client(sqlite3 *db);
void ajouter_facture(sqlite3 *db);

int main() {
    sqlite3 *db;
    int choix;
    
    printf("=================================================\n");
    printf("    ISOLOC - Application de Comptabilite\n");
    printf("         Version Console de Test\n");
    printf("=================================================\n\n");
    
    // Initialiser la base de données
    if (init_console_database(&db) != 0) {
        printf("Erreur: Impossible d'initialiser la base de donnees\n");
        return 1;
    }
    
    printf("Base de donnees initialisee avec succes!\n\n");
    
    // Boucle principale
    while (1) {
        afficher_menu_principal();
        printf("Votre choix: ");
        scanf("%d", &choix);
        
        switch (choix) {
            case 1:
                afficher_statistiques(db);
                break;
            case 2:
                afficher_clients(db);
                break;
            case 3:
                afficher_factures(db);
                break;
            case 4:
                ajouter_client(db);
                break;
            case 5:
                ajouter_facture(db);
                break;
            case 0:
                printf("\nMerci d'avoir utilise Isoloc!\n");
                close_console_database(db);
                return 0;
            default:
                printf("\nChoix invalide! Veuillez reessayer.\n\n");
        }
        
        printf("\nAppuyez sur Entree pour continuer...");
        getchar();
        getchar(); // Pour capturer le \n restant
        system("cls"); // Nettoyer l'écran (Windows)
    }
}

void afficher_menu_principal() {
    printf("=== MENU PRINCIPAL ===\n");
    printf("1. Tableau de bord\n");
    printf("2. Gestion des clients\n");
    printf("3. Gestion des factures\n");
    printf("4. Ajouter un client\n");
    printf("5. Ajouter une facture\n");
    printf("0. Quitter\n");
    printf("======================\n");
}

int init_console_database(sqlite3 **db) {
    int rc;
    char *err_msg = 0;
    
    // Ouvrir la base de données
    rc = sqlite3_open("data/isoloc.db", db);
    
    if (rc != SQLITE_OK) {
        printf("Erreur d'ouverture de la base de donnees: %s\n", sqlite3_errmsg(*db));
        sqlite3_close(*db);
        return 1;
    }
    
    // Créer les tables
    const char *sql_create_tables = 
        "CREATE TABLE IF NOT EXISTS clients ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "nom TEXT NOT NULL,"
        "prenom TEXT,"
        "email TEXT,"
        "telephone TEXT,"
        "adresse TEXT,"
        "solde REAL DEFAULT 0.0,"
        "date_creation DATETIME DEFAULT CURRENT_TIMESTAMP"
        ");"
        
        "CREATE TABLE IF NOT EXISTS factures ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "numero TEXT UNIQUE NOT NULL,"
        "client_id INTEGER,"
        "date_facture DATE NOT NULL,"
        "montant_ht REAL NOT NULL,"
        "tva REAL DEFAULT 20.0,"
        "montant_ttc REAL NOT NULL,"
        "statut TEXT DEFAULT 'En attente',"
        "date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,"
        "FOREIGN KEY (client_id) REFERENCES clients (id)"
        ");";
    
    rc = sqlite3_exec(*db, sql_create_tables, 0, 0, &err_msg);
    
    if (rc != SQLITE_OK) {
        printf("Erreur de creation des tables: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(*db);
        return 1;
    }
    
    // Insérer des données de test si la table est vide
    const char *sql_check = "SELECT COUNT(*) FROM clients;";
    sqlite3_stmt *stmt;
    
    rc = sqlite3_prepare_v2(*db, sql_check, -1, &stmt, NULL);
    if (rc == SQLITE_OK) {
        rc = sqlite3_step(stmt);
        if (rc == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            if (count == 0) {
                // Insérer des données de test
                const char *sql_test_data = 
                    "INSERT INTO clients (nom, prenom, email, telephone, solde) VALUES "
                    "('Dupont', 'Jean', '<EMAIL>', '01 23 45 67 89', 1250.50),"
                    "('Martin', 'Marie', '<EMAIL>', '01 98 76 54 32', 890.00),"
                    "('Societe ABC', '', '<EMAIL>', '01 11 22 33 44', 2100.75);"
                    
                    "INSERT INTO factures (numero, client_id, date_facture, montant_ht, tva, montant_ttc, statut) VALUES "
                    "('FAC-2024-001', 1, '2024-01-15', 1041.67, 20.0, 1250.00, 'Payee'),"
                    "('FAC-2024-002', 2, '2024-01-20', 741.67, 20.0, 890.00, 'En attente'),"
                    "('FAC-2024-003', 3, '2024-01-25', 1750.00, 20.0, 2100.00, 'En attente');";
                
                sqlite3_exec(*db, sql_test_data, 0, 0, &err_msg);
            }
        }
    }
    sqlite3_finalize(stmt);
    
    return 0;
}

void close_console_database(sqlite3 *db) {
    if (db) {
        sqlite3_close(db);
    }
}

void afficher_statistiques(sqlite3 *db) {
    printf("\n=== TABLEAU DE BORD ===\n");
    
    // Nombre total de clients
    const char *sql_clients = "SELECT COUNT(*) FROM clients;";
    sqlite3_stmt *stmt;
    
    if (sqlite3_prepare_v2(db, sql_clients, -1, &stmt, NULL) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            printf("Nombre de clients: %d\n", sqlite3_column_int(stmt, 0));
        }
    }
    sqlite3_finalize(stmt);
    
    // Chiffre d'affaires total
    const char *sql_ca = "SELECT SUM(montant_ttc) FROM factures WHERE statut = 'Payee';";
    if (sqlite3_prepare_v2(db, sql_ca, -1, &stmt, NULL) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            printf("Chiffre d'affaires: %.2f EUR\n", sqlite3_column_double(stmt, 0));
        }
    }
    sqlite3_finalize(stmt);
    
    // Factures en attente
    const char *sql_attente = "SELECT COUNT(*) FROM factures WHERE statut = 'En attente';";
    if (sqlite3_prepare_v2(db, sql_attente, -1, &stmt, NULL) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            printf("Factures en attente: %d\n", sqlite3_column_int(stmt, 0));
        }
    }
    sqlite3_finalize(stmt);
    
    printf("=======================\n");
}

void afficher_clients(sqlite3 *db) {
    printf("\n=== LISTE DES CLIENTS ===\n");
    printf("ID | Nom                | Email                  | Solde\n");
    printf("---|--------------------|-----------------------|----------\n");
    
    const char *sql = "SELECT id, nom, prenom, email, solde FROM clients ORDER BY nom;";
    sqlite3_stmt *stmt;
    
    if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            printf("%-3d| %-18s | %-21s | %8.2f\n",
                sqlite3_column_int(stmt, 0),
                sqlite3_column_text(stmt, 1),
                sqlite3_column_text(stmt, 3) ? (char*)sqlite3_column_text(stmt, 3) : "N/A",
                sqlite3_column_double(stmt, 4));
        }
    }
    sqlite3_finalize(stmt);
    printf("=========================\n");
}

void afficher_factures(sqlite3 *db) {
    printf("\n=== LISTE DES FACTURES ===\n");
    printf("Numero        | Client     | Date       | Montant TTC | Statut\n");
    printf("--------------|------------|------------|-------------|----------\n");
    
    const char *sql = "SELECT f.numero, c.nom, f.date_facture, f.montant_ttc, f.statut "
                     "FROM factures f LEFT JOIN clients c ON f.client_id = c.id "
                     "ORDER BY f.date_facture DESC;";
    sqlite3_stmt *stmt;
    
    if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            printf("%-13s | %-10s | %-10s | %11.2f | %s\n",
                sqlite3_column_text(stmt, 0),
                sqlite3_column_text(stmt, 1) ? (char*)sqlite3_column_text(stmt, 1) : "N/A",
                sqlite3_column_text(stmt, 2),
                sqlite3_column_double(stmt, 3),
                sqlite3_column_text(stmt, 4));
        }
    }
    sqlite3_finalize(stmt);
    printf("===========================\n");
}

void ajouter_client(sqlite3 *db) {
    char nom[100], prenom[100], email[100], telephone[20];
    
    printf("\n=== AJOUTER UN CLIENT ===\n");
    printf("Nom: ");
    scanf("%99s", nom);
    printf("Prenom: ");
    scanf("%99s", prenom);
    printf("Email: ");
    scanf("%99s", email);
    printf("Telephone: ");
    scanf("%19s", telephone);
    
    const char *sql = "INSERT INTO clients (nom, prenom, email, telephone) VALUES (?, ?, ?, ?);";
    sqlite3_stmt *stmt;
    
    if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, nom, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, prenom, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 3, email, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 4, telephone, -1, SQLITE_STATIC);
        
        if (sqlite3_step(stmt) == SQLITE_DONE) {
            printf("Client ajoute avec succes!\n");
        } else {
            printf("Erreur lors de l'ajout du client.\n");
        }
    }
    sqlite3_finalize(stmt);
}

void ajouter_facture(sqlite3 *db) {
    char numero[50], date[20];
    int client_id;
    double montant_ht, tva = 20.0;
    
    printf("\n=== AJOUTER UNE FACTURE ===\n");
    printf("Numero de facture: ");
    scanf("%49s", numero);
    printf("ID du client: ");
    scanf("%d", &client_id);
    printf("Date (YYYY-MM-DD): ");
    scanf("%19s", date);
    printf("Montant HT: ");
    scanf("%lf", &montant_ht);
    
    double montant_ttc = montant_ht * (1 + tva / 100);
    
    const char *sql = "INSERT INTO factures (numero, client_id, date_facture, montant_ht, tva, montant_ttc) VALUES (?, ?, ?, ?, ?, ?);";
    sqlite3_stmt *stmt;
    
    if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, numero, -1, SQLITE_STATIC);
        sqlite3_bind_int(stmt, 2, client_id);
        sqlite3_bind_text(stmt, 3, date, -1, SQLITE_STATIC);
        sqlite3_bind_double(stmt, 4, montant_ht);
        sqlite3_bind_double(stmt, 5, tva);
        sqlite3_bind_double(stmt, 6, montant_ttc);
        
        if (sqlite3_step(stmt) == SQLITE_DONE) {
            printf("Facture ajoutee avec succes! Montant TTC: %.2f EUR\n", montant_ttc);
        } else {
            printf("Erreur lors de l'ajout de la facture.\n");
        }
    }
    sqlite3_finalize(stmt);
}
