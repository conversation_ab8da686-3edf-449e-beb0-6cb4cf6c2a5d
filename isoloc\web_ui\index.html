<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Isoloc - Comptabilité Professionnelle</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Barre de navigation supérieure -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Isoloc</span>
            </div>
            <nav class="header-nav">
                <button class="nav-btn active" data-page="dashboard">
                    <i class="fas fa-chart-line"></i>
                    Tableau de Bord
                </button>
                <button class="nav-btn" data-page="clients">
                    <i class="fas fa-users"></i>
                    Clients
                </button>
                <button class="nav-btn" data-page="factures">
                    <i class="fas fa-file-invoice"></i>
                    Factures
                </button>
                <button class="nav-btn" data-page="depenses">
                    <i class="fas fa-receipt"></i>
                    Dépenses
                </button>
                <button class="nav-btn" data-page="rapports">
                    <i class="fas fa-chart-bar"></i>
                    Rapports
                </button>
            </nav>
        </div>
        <div class="header-right">
            <button class="header-btn">
                <i class="fas fa-bell"></i>
                <span class="notification-badge">3</span>
            </button>
            <button class="header-btn">
                <i class="fas fa-cog"></i>
            </button>
            <div class="user-profile">
                <img src="https://via.placeholder.com/32x32/4F46E5/FFFFFF?text=U" alt="Utilisateur" class="user-avatar">
                <span class="user-name">Admin</span>
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </header>

    <!-- Contenu principal -->
    <main class="main-content">
        <!-- Page Tableau de Bord -->
        <div id="dashboard" class="page active">
            <div class="page-header">
                <h1>Tableau de Bord</h1>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Nouvelle Facture
                    </button>
                </div>
            </div>

            <!-- Cartes de statistiques -->
            <div class="stats-grid">
                <div class="stat-card revenue">
                    <div class="stat-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Chiffre d'Affaires</h3>
                        <div class="stat-value">45 230,50 €</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12,5% ce mois
                        </div>
                    </div>
                </div>

                <div class="stat-card invoices">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Factures en Attente</h3>
                        <div class="stat-value">8</div>
                        <div class="stat-change warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            2 en retard
                        </div>
                    </div>
                </div>

                <div class="stat-card clients">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Clients Actifs</h3>
                        <div class="stat-value">24</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +3 ce mois
                        </div>
                    </div>
                </div>

                <div class="stat-card expenses">
                    <div class="stat-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Dépenses du Mois</h3>
                        <div class="stat-value">8 450,00 €</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -5,2% ce mois
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphiques et listes -->
            <div class="dashboard-grid">
                <div class="chart-container">
                    <div class="card">
                        <div class="card-header">
                            <h3>Évolution des Revenus</h3>
                            <div class="card-actions">
                                <button class="btn-icon">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="chart-placeholder">
                                <canvas id="revenueChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="recent-invoices">
                    <div class="card">
                        <div class="card-header">
                            <h3>Dernières Factures</h3>
                            <a href="#" class="link">Voir tout</a>
                        </div>
                        <div class="card-content">
                            <div class="invoice-list">
                                <div class="invoice-item">
                                    <div class="invoice-info">
                                        <div class="invoice-number">FAC-2024-001</div>
                                        <div class="invoice-client">Client ABC</div>
                                    </div>
                                    <div class="invoice-amount">1 250,00 €</div>
                                    <div class="invoice-status status-paid">Payée</div>
                                </div>
                                <div class="invoice-item">
                                    <div class="invoice-info">
                                        <div class="invoice-number">FAC-2024-002</div>
                                        <div class="invoice-client">Société XYZ</div>
                                    </div>
                                    <div class="invoice-amount">890,50 €</div>
                                    <div class="invoice-status status-pending">En attente</div>
                                </div>
                                <div class="invoice-item">
                                    <div class="invoice-info">
                                        <div class="invoice-number">FAC-2024-003</div>
                                        <div class="invoice-client">Entreprise 123</div>
                                    </div>
                                    <div class="invoice-amount">2 100,00 €</div>
                                    <div class="invoice-status status-pending">En attente</div>
                                </div>
                                <div class="invoice-item">
                                    <div class="invoice-info">
                                        <div class="invoice-number">FAC-2024-004</div>
                                        <div class="invoice-client">Client DEF</div>
                                    </div>
                                    <div class="invoice-amount">750,25 €</div>
                                    <div class="invoice-status status-draft">Brouillon</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Clients -->
        <div id="clients" class="page">
            <div class="page-header">
                <h1>Gestion des Clients</h1>
                <div class="page-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Exporter
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Nouveau Client
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Rechercher un client...">
                    </div>
                    <div class="filters">
                        <select class="filter-select">
                            <option>Tous les clients</option>
                            <option>Clients actifs</option>
                            <option>Clients inactifs</option>
                        </select>
                    </div>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Email</th>
                                    <th>Téléphone</th>
                                    <th>Solde</th>
                                    <th>Dernière Facture</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="client-info">
                                            <div class="client-avatar">JD</div>
                                            <div>
                                                <div class="client-name">Jean Dupont</div>
                                                <div class="client-company">Entreprise ABC</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>01 23 45 67 89</td>
                                    <td class="amount positive">1 250,50 €</td>
                                    <td>15/01/2024</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- Plus de lignes... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Autres pages (placeholders) -->
        <div id="factures" class="page">
            <div class="page-header">
                <h1>Gestion des Factures</h1>
            </div>
            <div class="card">
                <div class="card-content">
                    <p>Interface de gestion des factures en cours de développement...</p>
                </div>
            </div>
        </div>

        <div id="depenses" class="page">
            <div class="page-header">
                <h1>Gestion des Dépenses</h1>
            </div>
            <div class="card">
                <div class="card-content">
                    <p>Interface de gestion des dépenses en cours de développement...</p>
                </div>
            </div>
        </div>

        <div id="rapports" class="page">
            <div class="page-header">
                <h1>Rapports Financiers</h1>
            </div>
            <div class="card">
                <div class="card-content">
                    <p>Interface de génération de rapports en cours de développement...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="script.js"></script>
</body>
</html>
