@echo off
REM Script de compilation pour Isoloc sur Windows
REM Nécessite MSYS2 avec GTK+ et SQLite installés

echo Compilation d'Isoloc - Application de Comptabilité
echo ================================================

REM Créer les répertoires nécessaires
if not exist "obj" mkdir obj
if not exist "obj\ui" mkdir obj\ui
if not exist "obj\db" mkdir obj\db
if not exist "bin" mkdir bin
if not exist "data" mkdir data

echo Répertoires créés.

REM Vérifier les dépendances
echo Vérification des dépendances...
pkg-config --exists gtk+-3.0
if %errorlevel% neq 0 (
    echo ERREUR: GTK+ 3.0 non trouvé
    echo Veuillez installer GTK+ 3.0 via MSYS2:
    echo pacman -S mingw-w64-x86_64-gtk3
    pause
    exit /b 1
)

pkg-config --exists sqlite3
if %errorlevel% neq 0 (
    echo ERREUR: SQLite3 non trouvé
    echo Veuillez installer SQLite3 via MSYS2:
    echo pacman -S mingw-w64-x86_64-sqlite3
    pause
    exit /b 1
)

echo Dépendances OK.

REM Compilation des fichiers objets
echo Compilation des fichiers sources...

gcc -Wall -Wextra -std=c99 -g -Iinclude -c src/main.c -o obj/main.o -DGTK_DISABLE_DEPRECATED `pkg-config --cflags gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

gcc -Wall -Wextra -std=c99 -g -Iinclude -c src/ui/main_window.c -o obj/ui/main_window.o -DGTK_DISABLE_DEPRECATED `pkg-config --cflags gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

gcc -Wall -Wextra -std=c99 -g -Iinclude -c src/ui/sidebar.c -o obj/ui/sidebar.o -DGTK_DISABLE_DEPRECATED `pkg-config --cflags gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

gcc -Wall -Wextra -std=c99 -g -Iinclude -c src/ui/dashboard.c -o obj/ui/dashboard.o -DGTK_DISABLE_DEPRECATED `pkg-config --cflags gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

gcc -Wall -Wextra -std=c99 -g -Iinclude -c src/ui/pages.c -o obj/ui/pages.o -DGTK_DISABLE_DEPRECATED `pkg-config --cflags gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

gcc -Wall -Wextra -std=c99 -g -Iinclude -c src/db/database.c -o obj/db/database.o -DGTK_DISABLE_DEPRECATED `pkg-config --cflags gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

echo Fichiers objets compilés avec succès.

REM Édition de liens
echo Édition de liens...
gcc obj/main.o obj/ui/main_window.o obj/ui/sidebar.o obj/ui/dashboard.o obj/ui/pages.o obj/db/database.o -o bin/isoloc.exe `pkg-config --libs gtk+-3.0 sqlite3`
if %errorlevel% neq 0 goto :error

echo.
echo ================================================
echo Compilation terminée avec succès!
echo Exécutable créé: bin/isoloc.exe
echo ================================================
echo.
echo Pour exécuter l'application:
echo   cd bin
echo   isoloc.exe
echo.
pause
exit /b 0

:error
echo.
echo ERREUR: La compilation a échoué!
echo Vérifiez les messages d'erreur ci-dessus.
pause
exit /b 1
