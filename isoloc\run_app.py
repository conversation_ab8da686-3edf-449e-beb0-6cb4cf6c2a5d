#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lanceur pour l'application Isoloc
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire app au path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

try:
    from main import main
    
    print("=" * 60)
    print("    ISOLOC - Application de Comptabilité Professionnelle")
    print("=" * 60)
    print()
    print("🚀 Démarrage de l'application...")
    print("📁 Répertoire de travail:", os.getcwd())
    print("💾 Base de données: data/isoloc.db")
    print()
    print("✨ Interface graphique en cours de chargement...")
    print()
    
    # Lancer l'application
    main()
    
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    print("💡 Assurez-vous que tous les fichiers sont présents.")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    print("💡 Vérifiez la configuration de votre système.")
    sys.exit(1)
