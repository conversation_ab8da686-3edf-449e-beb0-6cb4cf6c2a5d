/* Style CSS pour Isoloc - Application de Comptabilité */

/* Styles généraux */
window {
    background-color: #f8f9fa;
}

/* Barre d'en-tête */
headerbar {
    background: linear-gradient(to bottom, #2c3e50, #34495e);
    color: white;
    border-bottom: 1px solid #1a252f;
}

headerbar button {
    color: white;
    border: none;
    background: transparent;
    padding: 8px;
    margin: 2px;
    border-radius: 4px;
}

headerbar button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Sidebar */
.sidebar {
    background-color: #ecf0f1;
    border-right: 1px solid #bdc3c7;
    padding: 0;
}

.sidebar-title {
    font-weight: bold;
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 15px;
}

/* Liste de navigation */
listbox {
    background-color: transparent;
}

listbox row {
    border: none;
    padding: 0;
    margin: 2px 8px;
    border-radius: 6px;
}

listbox row:selected {
    background-color: #3498db;
    color: white;
}

listbox row:hover {
    background-color: #ecf0f1;
}

listbox row:selected:hover {
    background-color: #2980b9;
}

/* Boutons de la sidebar */
.sidebar button {
    background: linear-gradient(to bottom, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

.sidebar button:hover {
    background: linear-gradient(to bottom, #2980b9, #21618c);
}

/* Zone de contenu principal */
stack {
    background-color: white;
    border-radius: 8px;
    margin: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Titres */
.title-1 {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
}

.title-2 {
    font-size: 20px;
    font-weight: bold;
    color: #34495e;
}

.title-3 {
    font-size: 16px;
    font-weight: 600;
    color: #34495e;
}

/* Cartes de statistiques */
frame {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Texte des cartes */
.caption {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 500;
}

.monospace {
    font-family: "Courier New", monospace;
    font-weight: bold;
}

/* Couleurs pour les statistiques */
.stat-positive {
    color: #27ae60;
}

.stat-negative {
    color: #e74c3c;
}

.stat-warning {
    color: #f39c12;
}

.stat-info {
    color: #3498db;
}

/* Grilles */
grid {
    margin: 10px 0;
}

/* Listes */
listbox {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

listbox row {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
}

listbox row:last-child {
    border-bottom: none;
}

/* Séparateurs */
separator {
    background-color: #e0e0e0;
    min-width: 1px;
    min-height: 1px;
}

/* Fenêtres scrollables */
scrolledwindow {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

/* Boutons généraux */
button {
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
    border: 1px solid #bdc3c7;
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

button:hover {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    border-color: #95a5a6;
}

button:active {
    background: linear-gradient(to bottom, #e9ecef, #dee2e6);
}

/* Boutons primaires */
.suggested-action {
    background: linear-gradient(to bottom, #3498db, #2980b9);
    color: white;
    border-color: #2980b9;
}

.suggested-action:hover {
    background: linear-gradient(to bottom, #2980b9, #21618c);
}

/* Boutons destructifs */
.destructive-action {
    background: linear-gradient(to bottom, #e74c3c, #c0392b);
    color: white;
    border-color: #c0392b;
}

.destructive-action:hover {
    background: linear-gradient(to bottom, #c0392b, #a93226);
}

/* Entrées de texte */
entry {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 8px 12px;
    background-color: white;
}

entry:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Labels d'information */
.info-label {
    color: #7f8c8d;
    font-size: 14px;
}

/* Marges et espacements */
.margin-small {
    margin: 5px;
}

.margin-medium {
    margin: 10px;
}

.margin-large {
    margin: 20px;
}

.padding-small {
    padding: 5px;
}

.padding-medium {
    padding: 10px;
}

.padding-large {
    padding: 20px;
}
