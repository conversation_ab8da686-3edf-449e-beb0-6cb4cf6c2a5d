@echo off
REM Script de compilation pour la version console d'Isoloc
echo Compilation d'Isoloc - Version Console de Test
echo =============================================

REM Créer les répertoires nécessaires
if not exist "bin" mkdir bin
if not exist "data" mkdir data

echo Répertoires créés.

REM Vérifier si SQLite3 est disponible
echo Vérification de SQLite3...

REM Essayer de compiler avec SQLite3 statique d'abord
echo Compilation avec SQLite3...
gcc -Wall -Wextra -std=c99 -g src/console_main.c -o bin/isoloc_console.exe -lsqlite3

if %errorlevel% equ 0 (
    echo.
    echo =============================================
    echo Compilation réussie!
    echo Exécutable créé: bin/isoloc_console.exe
    echo =============================================
    echo.
    echo Pour exécuter l'application:
    echo   cd bin
    echo   isoloc_console.exe
    echo.
    goto :success
)

REM Si SQLite3 n'est pas trouvé, essayer sans
echo SQLite3 non trouvé, compilation d'une version simplifiée...
gcc -Wall -Wextra -std=c99 -g -DNOSQLITE src/console_simple.c -o bin/isoloc_simple.exe

if %errorlevel% equ 0 (
    echo.
    echo =============================================
    echo Compilation réussie (version simplifiée)!
    echo Exécutable créé: bin/isoloc_simple.exe
    echo =============================================
    echo.
    echo Pour exécuter l'application:
    echo   cd bin
    echo   isoloc_simple.exe
    echo.
    goto :success
)

echo.
echo ERREUR: La compilation a échoué!
echo.
echo Solutions possibles:
echo 1. Installer SQLite3 pour Windows
echo 2. Utiliser MSYS2 avec: pacman -S mingw-w64-x86_64-sqlite3
echo 3. Télécharger SQLite3 depuis https://www.sqlite.org/download.html
echo.
pause
exit /b 1

:success
echo Voulez-vous exécuter l'application maintenant? (O/N)
set /p choice=
if /i "%choice%"=="O" (
    cd bin
    if exist "isoloc_console.exe" (
        isoloc_console.exe
    ) else (
        isoloc_simple.exe
    )
    cd ..
)
pause
