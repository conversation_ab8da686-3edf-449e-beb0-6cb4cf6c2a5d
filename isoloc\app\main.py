#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ISOLOC - Application de Comptabilité Professionnelle
Interface graphique avec Tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
from datetime import datetime
from pathlib import Path

class IsolocApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_database()
        self.setup_styles()
        self.create_interface()
        self.load_data()
    
    def setup_window(self):
        """Configuration de la fenêtre principale"""
        self.root.title("Isoloc - Comptabilité Professionnelle")

        # Obtenir les dimensions de l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Définir la taille de la fenêtre (80% de l'écran)
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(800, 600)

        # Permettre le redimensionnement automatique
        self.root.resizable(True, True)

        # Maximiser la fenêtre au démarrage (optionnel)
        # self.root.state('zoomed')  # Windows
        # self.root.attributes('-zoomed', True)  # Linux

        # Icône (si disponible)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # Configurer les styles adaptatifs
        self.setup_adaptive_styles()

    def format_date_field(self, entry_widget):
        """Formater automatiquement les champs de date"""
        def on_date_change(event=None):
            current_text = entry_widget.get()
            # Supprimer tous les caractères non numériques
            numbers_only = ''.join(filter(str.isdigit, current_text))

            if len(numbers_only) >= 8:
                # Format: DDMMYYYY -> DD/MM/YYYY
                formatted = f"{numbers_only[:2]}/{numbers_only[2:4]}/{numbers_only[4:8]}"
                entry_widget.delete(0, tk.END)
                entry_widget.insert(0, formatted)
            elif len(numbers_only) >= 4:
                # Format partiel: DDMM -> DD/MM
                formatted = f"{numbers_only[:2]}/{numbers_only[2:4]}"
                if len(numbers_only) > 4:
                    formatted += f"/{numbers_only[4:]}"
                entry_widget.delete(0, tk.END)
                entry_widget.insert(0, formatted)

        entry_widget.bind('<KeyRelease>', on_date_change)
        entry_widget.bind('<FocusOut>', on_date_change)

        # Définir la date d'aujourd'hui par défaut
        today = datetime.now().strftime("%d/%m/%Y")
        if not entry_widget.get():
            entry_widget.insert(0, today)

    def setup_adaptive_styles(self):
        """Configurer les styles adaptatifs selon la taille de l'écran"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Calculer les tailles de police adaptatives
        if screen_width >= 1920:  # Écrans 4K ou plus
            font_size_normal = 11
            font_size_heading = 13
            font_size_title = 16
            padding_normal = 8
        elif screen_width >= 1366:  # Écrans HD
            font_size_normal = 10
            font_size_heading = 12
            font_size_title = 14
            padding_normal = 6
        else:  # Écrans plus petits
            font_size_normal = 9
            font_size_heading = 11
            font_size_title = 12
            padding_normal = 4

        # Configurer les styles
        style = ttk.Style()

        # Style normal
        style.configure('TLabel', font=('Segoe UI', font_size_normal))
        style.configure('TButton', font=('Segoe UI', font_size_normal), padding=padding_normal)
        style.configure('TEntry', font=('Segoe UI', font_size_normal))
        style.configure('TCombobox', font=('Segoe UI', font_size_normal))

        # Style pour les en-têtes
        style.configure('Heading.TLabel', font=('Segoe UI', font_size_heading, 'bold'))

        # Style pour les titres
        style.configure('Title.TLabel', font=('Segoe UI', font_size_title, 'bold'))

        # Style pour les informations
        style.configure('Info.TLabel', font=('Segoe UI', font_size_normal), foreground='blue')

        # Style pour les boutons principaux
        style.configure('Primary.TButton', font=('Segoe UI', font_size_normal, 'bold'))

        # Configurer les Treeview
        style.configure('Treeview', font=('Segoe UI', font_size_normal))
        style.configure('Treeview.Heading', font=('Segoe UI', font_size_heading, 'bold'))

        # Hauteur des lignes dans les tableaux
        if screen_height >= 1080:
            row_height = 25
        elif screen_height >= 768:
            row_height = 22
        else:
            row_height = 20

        style.configure('Treeview', rowheight=row_height)

    def setup_adaptive_table(self, tree, columns_config):
        """Configurer un tableau pour qu'il s'adapte à la taille de l'écran"""
        def adjust_columns(event=None):
            try:
                total_width = tree.winfo_width()
                if total_width > 100:  # Éviter les erreurs lors de l'initialisation
                    for col_name, config in columns_config.items():
                        min_width = config.get('min_width', 50)
                        ratio = config.get('ratio', 0.1)
                        new_width = max(min_width, int(total_width * ratio))
                        tree.column(col_name, width=new_width, minwidth=min_width)
            except:
                pass  # Ignorer les erreurs si le widget n'est pas encore prêt

        # Lier l'événement de redimensionnement
        tree.bind('<Configure>', adjust_columns)
        # Appliquer immédiatement
        tree.after(100, adjust_columns)

    def setup_database(self):
        """Configuration de la base de données"""
        # Créer le dossier data s'il n'existe pas
        Path("data").mkdir(exist_ok=True)
        
        self.conn = sqlite3.connect("data/isoloc.db")
        self.cursor = self.conn.cursor()
        
        # Créer les tables
        self.create_tables()
        self.insert_sample_data()
    
    def create_tables(self):
        """Création des tables de base de données"""
        # Table clients
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                nom TEXT NOT NULL,
                categorie TEXT NOT NULL DEFAULT 'Public',
                ice TEXT,
                if_field TEXT,
                adresse TEXT,
                personne_contact TEXT,
                contact TEXT,
                n_fix TEXT,
                n_fax TEXT,
                adresse_electronique TEXT,
                solde REAL DEFAULT 0.0,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table factures
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE NOT NULL,
                client_id INTEGER,
                date_facture DATE NOT NULL,
                date_echeance DATE,
                montant_ht REAL NOT NULL,
                tva REAL DEFAULT 20.0,
                montant_ttc REAL NOT NULL,
                statut TEXT DEFAULT 'En attente',
                notes TEXT,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        """)
        
        # Table dépenses
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS depenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                description TEXT NOT NULL,
                montant REAL NOT NULL,
                categorie TEXT,
                date_depense DATE NOT NULL,
                fournisseur TEXT,
                notes TEXT,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Table fournisseurs
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                nom TEXT NOT NULL,
                ice TEXT,
                if_field TEXT,
                adresse TEXT,
                personne_contact TEXT,
                contact TEXT,
                n_fix TEXT,
                n_fax TEXT,
                adresse_electronique TEXT,
                solde REAL DEFAULT 0.0,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Table devis
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS devis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                n_devis TEXT UNIQUE NOT NULL,
                type_client TEXT DEFAULT 'sélectionné Privé / public',
                nature_prestation TEXT DEFAULT 'Travaux / fourniture',
                client TEXT DEFAULT 'sélectionné CLIENT',
                adresse TEXT,
                ice TEXT,
                objet TEXT DEFAULT 'sélectionné ou manuel',
                total_ht REAL DEFAULT 0.0,
                tva_rate REAL DEFAULT 20.0,
                total_ttc REAL DEFAULT 0.0,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Table détails devis
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS details_devis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                devis_id INTEGER,
                n_ligne INTEGER,
                designation TEXT DEFAULT 'sélectionné produit',
                u TEXT DEFAULT 'automatique',
                qte REAL DEFAULT 0.0,
                prix_achat_ht REAL DEFAULT 0.0,
                marge REAL DEFAULT 1.2,
                prix_ht REAL DEFAULT 0.0,
                prix_total_ht REAL DEFAULT 0.0,
                FOREIGN KEY (devis_id) REFERENCES devis (id)
            )
        """)

        # Table bons de livraison
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS bons_livraison (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                n_bon_livraison TEXT UNIQUE NOT NULL,
                type_marche TEXT DEFAULT 'BC',
                devis_n TEXT DEFAULT 'SÉLECTIONNÉ OU TAPER',
                n TEXT,
                client TEXT,
                ice TEXT,
                if_field TEXT,
                adresse TEXT,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Table détails bon de livraison
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS details_bon_livraison (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bon_livraison_id INTEGER,
                n_ligne INTEGER,
                designation TEXT NOT NULL,
                u TEXT DEFAULT 'automatique',
                qte REAL NOT NULL,
                reste_bc_marche TEXT DEFAULT 'AUTO/manuel',
                qte_livre_dans_bc TEXT,
                reste_en_stock TEXT DEFAULT 'AUTO/manuel',
                FOREIGN KEY (bon_livraison_id) REFERENCES bons_livraison (id)
            )
        """)

        # Table produits/achats
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                famille TEXT NOT NULL,
                produit TEXT NOT NULL,
                date_achat DATE NOT NULL,
                fournisseur TEXT,
                n_facture_achat TEXT,
                prix_achat_ht REAL NOT NULL,
                tva REAL DEFAULT 20.0,
                prix_achat_ttc REAL NOT NULL,
                quantite REAL DEFAULT 1.0,
                mode_paiement TEXT DEFAULT 'Chèque',
                date_paiement DATE,
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Table marchés (contrats publics)
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS marches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                n_marche TEXT UNIQUE NOT NULL,
                type_marche TEXT DEFAULT 'BC',
                devis_n TEXT,
                nature_prestation TEXT DEFAULT 'AUTOMATIQUE',
                objet TEXT DEFAULT 'AUTOMATIQUE',
                delai_execution TEXT DEFAULT '3 mois',
                client TEXT NOT NULL,
                montant_ttc REAL DEFAULT 0.0,
                caution_provisoire TEXT DEFAULT 'manuel',
                caution_definitif_population TEXT DEFAULT '5%',
                caution_definitif TEXT DEFAULT 'du montant de marché arrondi',
                ordre_service TEXT DEFAULT 'paré',
                caution_retenue_garantie TEXT DEFAULT '7% si nature de prestation et travaux 0% si fourniture',
                delai_prevu_achevement TEXT DEFAULT '3 mois à compter de date ordre de service',
                date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
                statut TEXT DEFAULT 'En cours'
            )
        """)

        # Table détails marchés (bordereau de prix)
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS details_marches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marche_id INTEGER NOT NULL,
                n_ligne INTEGER NOT NULL,
                designation TEXT NOT NULL DEFAULT 'AUTOMATIQUE',
                u TEXT DEFAULT 'automatique',
                qte REAL DEFAULT 0.0,
                prix_achat_ht REAL DEFAULT 0.0,
                total_achat_ht REAL DEFAULT 0.0,
                prix_ht REAL DEFAULT 0.0,
                prix_ttc REAL DEFAULT 0.0,
                marge REAL DEFAULT 0.0,
                FOREIGN KEY (marche_id) REFERENCES marches (id)
            )
        """)

        # Table paiements marchés
        self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS paiements_marches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marche_id INTEGER NOT NULL,
                mode_paiement TEXT DEFAULT 'sélectionner mode de paiement',
                montant REAL DEFAULT 0.0,
                date_paiement DATE,
                reference TEXT,
                statut TEXT DEFAULT 'En attente',
                FOREIGN KEY (marche_id) REFERENCES marches (id)
            )
        """)

        self.conn.commit()

        # Migrate existing database to remove category column from fournisseurs
        self.migrate_fournisseurs_remove_category()

        # Migrate existing database to add quantity column to produits
        self.migrate_produits_add_quantity()

        # Migrate existing database to add unite column to produits
        self.migrate_produits_add_unite()

    def migrate_fournisseurs_remove_category(self):
        """Migrate fournisseurs table to remove category column while preserving data"""
        try:
            # Check if category column exists
            self.cursor.execute("PRAGMA table_info(fournisseurs)")
            columns = [column[1] for column in self.cursor.fetchall()]

            if 'categorie' in columns:
                print("🔄 Migrating fournisseurs table to remove category column...")

                # Create new table without category column
                self.cursor.execute("""
                    CREATE TABLE fournisseurs_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        code TEXT UNIQUE NOT NULL,
                        nom TEXT NOT NULL,
                        ice TEXT,
                        if_field TEXT,
                        adresse TEXT,
                        personne_contact TEXT,
                        contact TEXT,
                        n_fix TEXT,
                        n_fax TEXT,
                        adresse_electronique TEXT,
                        solde REAL DEFAULT 0.0,
                        date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Copy data from old table to new table (excluding category)
                self.cursor.execute("""
                    INSERT INTO fournisseurs_new (id, code, nom, ice, if_field, adresse,
                    personne_contact, contact, n_fix, n_fax, adresse_electronique, solde, date_creation)
                    SELECT id, code, nom, ice, if_field, adresse,
                    personne_contact, contact, n_fix, n_fax, adresse_electronique, solde, date_creation
                    FROM fournisseurs
                """)

                # Drop old table and rename new table
                self.cursor.execute("DROP TABLE fournisseurs")
                self.cursor.execute("ALTER TABLE fournisseurs_new RENAME TO fournisseurs")

                self.conn.commit()
                print("✅ Migration completed: Category column removed from fournisseurs table")
            else:
                print("✅ Fournisseurs table already up to date (no category column)")

        except Exception as e:
            print(f"❌ Error during fournisseurs migration: {e}")
            # Rollback if something goes wrong
            self.conn.rollback()

    def migrate_produits_add_quantity(self):
        """Migrate produits table to add quantity column"""
        try:
            # Check if quantity column exists
            self.cursor.execute("PRAGMA table_info(produits)")
            columns = [column[1] for column in self.cursor.fetchall()]

            if 'quantite' not in columns:
                print("🔄 Adding quantity column to produits table...")

                # Add quantity column with default value of 1.0
                self.cursor.execute("ALTER TABLE produits ADD COLUMN quantite REAL DEFAULT 1.0")

                # Update existing records to have quantity = 1.0
                self.cursor.execute("UPDATE produits SET quantite = 1.0 WHERE quantite IS NULL")

                self.conn.commit()
                print("✅ Migration completed: Quantity column added to produits table")
            else:
                print("✅ Produits table already up to date (quantity column exists)")

        except Exception as e:
            print(f"❌ Error during produits migration: {e}")
            # Rollback if something goes wrong
            self.conn.rollback()

    def insert_sample_data(self):
        """Insertion de données d'exemple"""
        # Vérifier si des données existent déjà
        self.cursor.execute("SELECT COUNT(*) FROM clients")
        if self.cursor.fetchone()[0] > 0:
            return

        # Nettoyer toutes les tables avant d'insérer (pour éviter les conflits)
        try:
            self.cursor.execute("DELETE FROM factures")
            self.cursor.execute("DELETE FROM details_devis")
            self.cursor.execute("DELETE FROM devis")
            self.cursor.execute("DELETE FROM produits")
            self.cursor.execute("DELETE FROM fournisseurs")
            self.cursor.execute("DELETE FROM clients")
        except:
            pass
        
        # Clients d'exemple
        clients_data = [
            ("C1", "Dupont Jean", "Public", "ICE123456789", "IF987654321", "123 Rue de la République, 75001 Paris", "Jean Dupont", "01 23 45 67 89", "01 23 45 67 90", "01 23 45 67 91", "<EMAIL>", 1250.50),
            ("C2", "Martin Marie", "Public", "ICE234567890", "IF876543210", "456 Avenue des Champs, 69000 Lyon", "Marie Martin", "01 98 76 54 32", "01 98 76 54 33", "01 98 76 54 34", "<EMAIL>", 890.00),
            ("C3", "Société ABC", "Privé", "", "", "", "Ahmed Benali", "01 11 22 33 44", "01 11 22 33 45", "01 11 22 33 46", "<EMAIL>", 2100.75),
            ("C4", "Entreprise XYZ", "Privé", "", "", "", "Fatima Alami", "01 55 66 77 88", "01 55 66 77 89", "01 55 66 77 90", "<EMAIL>", 0.00)
        ]

        self.cursor.executemany("""
            INSERT INTO clients (code, nom, categorie, ice, if_field, adresse, personne_contact, contact, n_fix, n_fax, adresse_electronique, solde)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, clients_data)

        # Fournisseurs d'exemple
        fournisseurs_data = [
            ("F1", "ElectroStore SARL", "ICE345678901", "IF123456789", "789 Avenue Industrielle, 20000 Casablanca", "Hassan Alami", "05 22 33 44 55", "05 22 33 44 56", "05 22 33 44 57", "<EMAIL>", 0.00),
            ("F2", "TechSupply", "ICE456789012", "IF234567890", "456 Rue de la Technologie, 10000 Rabat", "Fatima Benali", "05 37 66 77 88", "05 37 66 77 89", "05 37 66 77 90", "<EMAIL>", -500.00),
            ("F3", "Bureau Plus", "", "", "", "Ahmed Tazi", "05 24 11 22 33", "05 24 11 22 34", "05 24 11 22 35", "<EMAIL>", 750.00),
            ("F4", "Papeterie Central", "ICE567890123", "IF345678901", "123 Boulevard Central, 40000 Marrakech", "Khadija Mansouri", "05 24 88 99 00", "05 24 88 99 01", "05 24 88 99 02", "<EMAIL>", 0.00)
        ]

        self.cursor.executemany("""
            INSERT INTO fournisseurs (code, nom, ice, if_field, adresse, personne_contact, contact, n_fix, n_fax, adresse_electronique, solde)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, fournisseurs_data)
        
        # Factures d'exemple
        factures_data = [
            ("FAC-2024-001", 1, "2024-01-15", "2024-02-15", 1041.67, 20.0, 1250.00, "Payée"),
            ("FAC-2024-002", 2, "2024-01-20", "2024-02-20", 741.67, 20.0, 890.00, "En attente"),
            ("FAC-2024-003", 3, "2024-01-25", "2024-02-25", 1750.00, 20.0, 2100.00, "En attente"),
            ("FAC-2024-004", 1, "2024-02-01", "2024-03-01", 625.00, 20.0, 750.00, "Brouillon")
        ]
        
        self.cursor.executemany("""
            INSERT INTO factures (numero, client_id, date_facture, date_echeance, montant_ht, tva, montant_ttc, statut)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, factures_data)
        
        # Dépenses d'exemple
        depenses_data = [
            ("Fournitures de bureau", 150.00, "Bureau", "2024-01-10", "Bureau Plus"),
            ("Électricité", 280.50, "Utilities", "2024-01-15", "EDF"),
            ("Logiciel comptable", 99.00, "Logiciels", "2024-01-20", "SoftwareStore"),
            ("Déplacement client", 45.80, "Transport", "2024-01-25", "SNCF")
        ]
        
        self.cursor.executemany("""
            INSERT INTO depenses (description, montant, categorie, date_depense, fournisseur)
            VALUES (?, ?, ?, ?, ?)
        """, depenses_data)

        # Produits d'exemple
        produits_data = [
            ("EL001", "ELECTRICITE", "LAMPE LED", "2025-01-01", "ElectroStore", "111111", 12.50, 20.0, 15.00, 10.0, "Chèque", "2025-01-01"),
            ("EL002", "ELECTRICITE", "CABLE ETHERNET", "2025-01-02", "TechSupply", "222222", 8.33, 20.0, 10.00, 25.0, "Espèces", "2025-01-02"),
            ("BU001", "BUREAU", "STYLO BIC", "2025-01-03", "Bureau Plus", "333333", 0.83, 20.0, 1.00, 100.0, "Carte", "2025-01-03"),
            ("BU002", "BUREAU", "PAPIER A4", "2025-01-04", "Papeterie Central", "444444", 4.17, 20.0, 5.00, 50.0, "Virement", "2025-01-05"),
            ("IN001", "INFORMATIQUE", "SOURIS USB", "2025-01-05", "ComputerWorld", "555555", 16.67, 20.0, 20.00, 5.0, "Chèque", "2025-01-10")
        ]

        self.cursor.executemany("""
            INSERT INTO produits (code, famille, produit, date_achat, fournisseur, n_facture_achat, prix_achat_ht, tva, prix_achat_ttc, quantite, mode_paiement, date_paiement)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, produits_data)

        self.conn.commit()
    
    def setup_styles(self):
        """Configuration des styles"""
        style = ttk.Style()
        
        # Thème moderne
        style.theme_use('clam')
        
        # Couleurs personnalisées
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        style.configure('Info.TLabel', font=('Arial', 10), foreground='#7f8c8d')
        
        # Boutons personnalisés
        style.configure('Primary.TButton', font=('Arial', 10, 'bold'))
        style.configure('Success.TButton', font=('Arial', 10, 'bold'))
        style.configure('Danger.TButton', font=('Arial', 10, 'bold'))
        
        # Notebook (onglets)
        style.configure('TNotebook.Tab', padding=[20, 10])
    
    def create_interface(self):
        """Création de l'interface principale"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # En-tête simplifié
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        # Logo et titre
        title_label = ttk.Label(header_frame, text="📦 ISOLOC - Gestion Complète", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)

        subtitle_label = ttk.Label(header_frame, text="Système de gestion des clients, fournisseurs et produits", style='Info.TLabel')
        subtitle_label.grid(row=1, column=0, sticky=tk.W)
        
        # Notebook pour les onglets (Clients et Produits)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Créer les onglets dans l'ordre spécifié
        self.create_fournisseurs_tab()    # 1. Fournisseur (Suppliers) - First
        self.create_produits_tab()        # 2. Produits (Products) - Second
        self.create_clients_tab()         # 3. Clients (Clients) - Third
        self.create_devis_tab()           # 4. DEVIS (Quotes) - Fourth
        self.create_bons_livraison_tab()  # 5. Bon de Livraison (Delivery Notes) - Fifth
        self.create_marches_tab()         # 6. MARCHÉS (Public Contracts) - Sixth
    

    

    
    def run(self):
        """Lancement de l'application"""
        self.root.mainloop()
    
    def load_data(self):
        """Chargement des données"""
        self.load_clients_data()
        self.update_client_stats()
        self.load_fournisseurs_data()
        self.update_fournisseurs_stats()
        self.load_devis_data()
        self.update_devis_stats()
        self.load_bons_livraison_data()
        self.update_bons_stats()
        self.load_produits_data()
        self.update_produits_stats()

        # Load products cache for DEVIS dropdown functionality
        self.load_produits_cache()

        # Clean up invalid supplier references
        self.clean_invalid_supplier_references()
    

    
    # Méthodes pour les actions
    def nouveau_client(self):
        """Ouvrir la fenêtre de nouveau client"""
        self.open_client_form()

    def sauvegarder(self):
        """Sauvegarder les données"""
        self.conn.commit()
        messagebox.showinfo("Succès", "Données sauvegardées avec succès!")
    
    # Interface clients principale
    def create_clients_interface(self, parent):
        """Création de l'interface clients principale"""
        # Configuration du redimensionnement
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)

        # En-tête avec boutons d'action
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        ttk.Label(header_frame, text="📋 Liste des Clients", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)

        # Boutons d'action
        actions_frame = ttk.Frame(header_frame)
        actions_frame.grid(row=0, column=2, sticky=tk.E)

        ttk.Button(actions_frame, text="➕ Nouveau Client", command=self.nouveau_client,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🔄 Actualiser", command=self.load_clients_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="📤 Exporter", command=self.exporter_clients).pack(side=tk.LEFT)

        # Barre de recherche et statistiques
        search_stats_frame = ttk.Frame(parent)
        search_stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_stats_frame.columnconfigure(1, weight=1)

        # Barre de recherche
        search_frame = ttk.Frame(search_stats_frame)
        search_frame.grid(row=0, column=0, sticky=tk.W)

        ttk.Label(search_frame, text="🔍 Rechercher:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_clients)

        # Statistiques rapides
        stats_frame = ttk.Frame(search_stats_frame)
        stats_frame.grid(row=0, column=2, sticky=tk.E)

        self.stats_label = ttk.Label(stats_frame, text="Total: 0 clients", style='Info.TLabel')
        self.stats_label.pack()

        # Tableau des clients
        self.create_clients_table(parent)

    def create_clients_table(self, parent):
        """Création du tableau des clients"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Treeview
        columns = ('code', 'nom', 'categorie', 'ice', 'contact', 'adresse_electronique', 'solde')
        self.clients_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # En-têtes
        self.clients_tree.heading('code', text='Code')
        self.clients_tree.heading('nom', text='Nom / Entreprise')
        self.clients_tree.heading('categorie', text='Catégorie')
        self.clients_tree.heading('ice', text='ICE')
        self.clients_tree.heading('contact', text='Contact')
        self.clients_tree.heading('adresse_electronique', text='Email')
        self.clients_tree.heading('solde', text='Solde')

        # Configuration adaptative des colonnes
        clients_columns_config = {
            'code': {'ratio': 0.08, 'min_width': 50},
            'nom': {'ratio': 0.25, 'min_width': 150},
            'categorie': {'ratio': 0.10, 'min_width': 70},
            'ice': {'ratio': 0.15, 'min_width': 100},
            'contact': {'ratio': 0.15, 'min_width': 100},
            'adresse_electronique': {'ratio': 0.20, 'min_width': 150},
            'solde': {'ratio': 0.07, 'min_width': 80}
        }

        # Appliquer la configuration adaptative
        self.setup_adaptive_table(self.clients_tree, clients_columns_config)

        self.clients_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.clients_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.clients_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.clients_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.clients_tree.configure(xscrollcommand=h_scrollbar.set)

        # Menu contextuel et événements
        self.clients_tree.bind('<Button-3>', self.show_client_context_menu)
        self.clients_tree.bind('<Double-1>', self.edit_client)

        # Barre de statut
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="Prêt", style='Info.TLabel')
        self.status_label.pack(side=tk.LEFT)

        # Informations sur la sélection
        self.selection_label = ttk.Label(status_frame, text="", style='Info.TLabel')
        self.selection_label.pack(side=tk.RIGHT)

        # Lier l'événement de sélection
        self.clients_tree.bind('<<TreeviewSelect>>', self.on_client_selection)

    def create_clients_tab(self):
        """Création de l'onglet clients"""
        clients_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(clients_frame, text="👥 Clients")

        self.create_clients_interface(clients_frame)

    def create_fournisseurs_tab(self):
        """Création de l'onglet fournisseurs"""
        fournisseurs_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(fournisseurs_frame, text="🏭 Fournisseurs")

        self.create_fournisseurs_interface(fournisseurs_frame)

    def create_fournisseurs_interface(self, parent):
        """Création de l'interface fournisseurs principale"""
        # Configuration du redimensionnement
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)

        # En-tête avec boutons d'action
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        ttk.Label(header_frame, text="📋 Liste des Fournisseurs", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)

        # Boutons d'action
        actions_frame = ttk.Frame(header_frame)
        actions_frame.grid(row=0, column=2, sticky=tk.E)

        ttk.Button(actions_frame, text="➕ Nouveau Fournisseur", command=self.nouveau_fournisseur,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🔄 Actualiser", command=self.load_fournisseurs_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="📤 Exporter", command=self.exporter_fournisseurs).pack(side=tk.LEFT)

        # Barre de recherche et statistiques
        search_stats_frame = ttk.Frame(parent)
        search_stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_stats_frame.columnconfigure(1, weight=1)

        # Barre de recherche
        search_frame = ttk.Frame(search_stats_frame)
        search_frame.grid(row=0, column=0, sticky=tk.W)

        ttk.Label(search_frame, text="🔍 Rechercher:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_fournisseurs_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_fournisseurs_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_fournisseurs)

        # Statistiques rapides
        stats_frame = ttk.Frame(search_stats_frame)
        stats_frame.grid(row=0, column=2, sticky=tk.E)

        self.fournisseurs_stats_label = ttk.Label(stats_frame, text="Total: 0 fournisseurs", style='Info.TLabel')
        self.fournisseurs_stats_label.pack()

        # Tableau des fournisseurs
        self.create_fournisseurs_table(parent)

    def create_fournisseurs_table(self, parent):
        """Création du tableau des fournisseurs"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Treeview
        columns = ('code', 'nom', 'ice', 'contact', 'adresse_electronique', 'solde')
        self.fournisseurs_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # En-têtes
        self.fournisseurs_tree.heading('code', text='Code')
        self.fournisseurs_tree.heading('nom', text='Nom / Entreprise')
        self.fournisseurs_tree.heading('ice', text='ICE')
        self.fournisseurs_tree.heading('contact', text='Contact')
        self.fournisseurs_tree.heading('adresse_electronique', text='Email')
        self.fournisseurs_tree.heading('solde', text='Solde')

        # Largeurs des colonnes (redistributed space from removed category column)
        self.fournisseurs_tree.column('code', width=80)
        self.fournisseurs_tree.column('nom', width=250)
        self.fournisseurs_tree.column('ice', width=140)
        self.fournisseurs_tree.column('contact', width=140)
        self.fournisseurs_tree.column('adresse_electronique', width=220)
        self.fournisseurs_tree.column('solde', width=120)

        self.fournisseurs_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.fournisseurs_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.fournisseurs_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.fournisseurs_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.fournisseurs_tree.configure(xscrollcommand=h_scrollbar.set)

        # Menu contextuel et événements
        self.fournisseurs_tree.bind('<Button-3>', self.show_fournisseur_context_menu)
        self.fournisseurs_tree.bind('<Double-1>', self.edit_fournisseur)

        # Barre de statut
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.fournisseurs_status_label = ttk.Label(status_frame, text="Prêt", style='Info.TLabel')
        self.fournisseurs_status_label.pack(side=tk.LEFT)

        # Informations sur la sélection
        self.fournisseurs_selection_label = ttk.Label(status_frame, text="", style='Info.TLabel')
        self.fournisseurs_selection_label.pack(side=tk.RIGHT)

        # Lier l'événement de sélection
        self.fournisseurs_tree.bind('<<TreeviewSelect>>', self.on_fournisseur_selection)

    def create_devis_tab(self):
        """Création de l'onglet devis"""
        devis_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(devis_frame, text="📄 Devis")

        self.create_devis_interface(devis_frame)

    def create_bons_livraison_tab(self):
        """Création de l'onglet bons de livraison"""
        bons_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(bons_frame, text="📋 Bons de Livraison")

        self.create_bons_livraison_interface(bons_frame)

    def create_marches_tab(self):
        """Création de l'onglet marchés"""
        marches_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(marches_frame, text="🏛️ Marchés")

        self.create_marches_interface(marches_frame)

    def create_marches_interface(self, parent):
        """Création de l'interface des marchés"""
        # Configuration du parent pour le redimensionnement
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)  # Le tableau prend l'espace disponible

        # Titre de la section
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        title_frame.columnconfigure(1, weight=1)

        title_label = ttk.Label(title_frame, text="🏛️ GESTION DES MARCHÉS", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)

        # Statistiques
        self.marches_stats_label = ttk.Label(title_frame, text="Total: 0 marchés", style='Info.TLabel')
        self.marches_stats_label.grid(row=0, column=1, sticky=tk.E)

        # Barre d'outils
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        toolbar_frame.columnconfigure(2, weight=1)

        # Boutons d'action
        ttk.Button(toolbar_frame, text="➕ Nouveau Marché",
                  command=self.nouveau_marche, style='Primary.TButton').grid(row=0, column=0, padx=(0, 10))
        ttk.Button(toolbar_frame, text="✏️ Modifier",
                  command=self.modifier_marche).grid(row=0, column=1, padx=(0, 10))

        # Barre de recherche
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.grid(row=0, column=2, sticky=(tk.W, tk.E), padx=(20, 0))
        search_frame.columnconfigure(1, weight=1)

        ttk.Label(search_frame, text="🔍 Recherche:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.marches_search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.marches_search_var, width=25)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        search_entry.bind('<KeyRelease>', lambda e: self.search_marches())

        # Filtre par statut
        ttk.Label(search_frame, text="Statut:", style='Heading.TLabel').grid(row=0, column=2, padx=(15, 5))
        self.marches_status_filter_var = tk.StringVar(value="Tous")
        status_combo = ttk.Combobox(search_frame, textvariable=self.marches_status_filter_var,
                                   values=["Tous", "En cours", "Terminé", "Suspendu", "Annulé"],
                                   state="readonly", width=12)
        status_combo.grid(row=0, column=3, padx=(0, 5))
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.search_marches())

        ttk.Button(search_frame, text="🔍", width=3,
                  command=self.search_marches).grid(row=0, column=4)
        ttk.Button(search_frame, text="🔄", width=3,
                  command=self.refresh_marches).grid(row=0, column=5, padx=(5, 0))

        # Tableau des marchés
        self.create_marches_table(parent)

        # Charger les données
        self.load_marches_data()

    def create_marches_table(self, parent):
        """Création du tableau des marchés"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Colonnes du tableau
        columns = ('n_marche', 'type_marche', 'client', 'objet', 'montant_ttc', 'statut', 'date_creation')
        self.marches_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # Configuration des en-têtes avec tri
        self.marches_tree.heading('n_marche', text='N° Marché ↕️', command=lambda: self.sort_marches_column('n_marche'))
        self.marches_tree.heading('type_marche', text='Type ↕️', command=lambda: self.sort_marches_column('type_marche'))
        self.marches_tree.heading('client', text='Client ↕️', command=lambda: self.sort_marches_column('client'))
        self.marches_tree.heading('objet', text='Objet ↕️', command=lambda: self.sort_marches_column('objet'))
        self.marches_tree.heading('montant_ttc', text='Montant TTC ↕️', command=lambda: self.sort_marches_column('montant_ttc'))
        self.marches_tree.heading('statut', text='Statut ↕️', command=lambda: self.sort_marches_column('statut'))
        self.marches_tree.heading('date_creation', text='Date Création ↕️', command=lambda: self.sort_marches_column('date_creation'))

        # Configuration des largeurs de colonnes
        self.marches_tree.column('n_marche', width=120, minwidth=100)
        self.marches_tree.column('type_marche', width=80, minwidth=60)
        self.marches_tree.column('client', width=200, minwidth=150)
        self.marches_tree.column('objet', width=250, minwidth=200)
        self.marches_tree.column('montant_ttc', width=120, minwidth=100)
        self.marches_tree.column('statut', width=100, minwidth=80)
        self.marches_tree.column('date_creation', width=120, minwidth=100)

        # Placement du tableau
        self.marches_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.marches_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.marches_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.marches_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.marches_tree.configure(xscrollcommand=h_scrollbar.set)

        # Événements
        self.marches_tree.bind('<Double-Button-1>', self.on_marche_double_click)
        self.marches_tree.bind('<Button-3>', self.show_marche_context_menu)
        self.marches_tree.bind('<<TreeviewSelect>>', self.on_marche_selection)

        # Label de sélection
        self.marches_selection_label = ttk.Label(parent, text="", style='Info.TLabel')
        self.marches_selection_label.grid(row=3, column=0, sticky=tk.W, pady=(5, 0))

    def create_produits_tab(self):
        """Création de l'onglet produits"""
        produits_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(produits_frame, text="📦 Produits")

        self.create_produits_interface(produits_frame)

    def create_produits_interface(self, parent):
        """Création de l'interface produits"""
        # Configuration du redimensionnement
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)

        # En-tête avec boutons d'action
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        ttk.Label(header_frame, text="📋 Liste des Produits/Achats", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)

        # Boutons d'action
        actions_frame = ttk.Frame(header_frame)
        actions_frame.grid(row=0, column=2, sticky=tk.E)

        ttk.Button(actions_frame, text="➕ Nouveau Produit", command=self.nouveau_produit,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🔄 Actualiser", command=self.load_produits_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="📤 Exporter", command=self.exporter_produits).pack(side=tk.LEFT)

        # Barre de recherche et statistiques
        search_stats_frame = ttk.Frame(parent)
        search_stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_stats_frame.columnconfigure(1, weight=1)

        # Barre de recherche
        search_frame = ttk.Frame(search_stats_frame)
        search_frame.grid(row=0, column=0, sticky=tk.W)

        ttk.Label(search_frame, text="🔍 Rechercher:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_produits_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_produits_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_produits)

        # Statistiques rapides
        stats_frame = ttk.Frame(search_stats_frame)
        stats_frame.grid(row=0, column=2, sticky=tk.E)

        self.produits_stats_label = ttk.Label(stats_frame, text="Total: 0 produits", style='Info.TLabel')
        self.produits_stats_label.pack()

        # Tableau des produits
        self.create_produits_table(parent)

    def create_produits_table(self, parent):
        """Création du tableau des produits"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Treeview
        columns = ('code', 'famille', 'produit', 'unite', 'date_achat', 'fournisseur', 'quantite', 'tva', 'prix_ttc', 'mode_paiement')
        self.produits_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # En-têtes
        self.produits_tree.heading('code', text='Code')
        self.produits_tree.heading('famille', text='Famille')
        self.produits_tree.heading('produit', text='Produit')
        self.produits_tree.heading('unite', text='Unité')
        self.produits_tree.heading('date_achat', text='Date Achat')
        self.produits_tree.heading('fournisseur', text='Fournisseur')
        self.produits_tree.heading('quantite', text='Quantité')
        self.produits_tree.heading('tva', text='TVA')
        self.produits_tree.heading('prix_ttc', text='Prix TTC')
        self.produits_tree.heading('mode_paiement', text='Mode Paiement')

        # Configuration adaptative des colonnes
        produits_columns_config = {
            'code': {'ratio': 0.08, 'min_width': 60},
            'famille': {'ratio': 0.12, 'min_width': 100},
            'produit': {'ratio': 0.16, 'min_width': 120},
            'unite': {'ratio': 0.08, 'min_width': 60},
            'date_achat': {'ratio': 0.10, 'min_width': 80},
            'fournisseur': {'ratio': 0.12, 'min_width': 100},
            'quantite': {'ratio': 0.08, 'min_width': 60},
            'tva': {'ratio': 0.06, 'min_width': 50},
            'prix_ttc': {'ratio': 0.10, 'min_width': 80},
            'mode_paiement': {'ratio': 0.10, 'min_width': 80}
        }

        # Appliquer la configuration adaptative
        self.setup_adaptive_table(self.produits_tree, produits_columns_config)

        self.produits_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.produits_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.produits_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.produits_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.produits_tree.configure(xscrollcommand=h_scrollbar.set)

        # Menu contextuel et événements
        self.produits_tree.bind('<Button-3>', self.show_produit_context_menu)
        self.produits_tree.bind('<Double-1>', self.edit_produit)

        # Barre de statut
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.produits_status_label = ttk.Label(status_frame, text="Prêt", style='Info.TLabel')
        self.produits_status_label.pack(side=tk.LEFT)

        # Informations sur la sélection
        self.produits_selection_label = ttk.Label(status_frame, text="", style='Info.TLabel')
        self.produits_selection_label.pack(side=tk.RIGHT)

        # Lier l'événement de sélection
        self.produits_tree.bind('<<TreeviewSelect>>', self.on_produit_selection)

    def load_clients_data(self):
        """Chargement des données clients"""
        # Vider le treeview
        for item in self.clients_tree.get_children():
            self.clients_tree.delete(item)

        # Charger les données
        self.cursor.execute("""
            SELECT code, nom, categorie, ice, contact, adresse_electronique, solde
            FROM clients
            ORDER BY code
        """)

        for row in self.cursor.fetchall():
            code, nom, categorie, ice, contact, email, solde = row
            solde_str = f"{solde:,.2f} DH".replace(',', ' ')
            ice_display = ice if ice else ""

            # Couleur selon le solde
            tags = ('positive',) if solde > 0 else ('negative',) if solde < 0 else ()

            self.clients_tree.insert('', tk.END, values=(code, nom, categorie, ice_display, contact, email, solde_str), tags=tags)

        # Configuration des couleurs
        self.clients_tree.tag_configure('positive', foreground='#27ae60')
        self.clients_tree.tag_configure('negative', foreground='#e74c3c')

        # Mettre à jour les statistiques
        self.update_client_stats()

    def filter_clients(self, event=None):
        """Filtrage des clients"""
        search_term = self.search_var.get().lower()

        # Vider et recharger avec filtre
        for item in self.clients_tree.get_children():
            self.clients_tree.delete(item)

        # Requête avec filtre
        self.cursor.execute("""
            SELECT code, nom, categorie, ice, contact, adresse_electronique, solde
            FROM clients
            WHERE LOWER(code) LIKE ? OR LOWER(nom) LIKE ? OR LOWER(adresse_electronique) LIKE ? OR LOWER(contact) LIKE ?
            ORDER BY code
        """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        for row in self.cursor.fetchall():
            code, nom, categorie, ice, contact, email, solde = row
            solde_str = f"{solde:,.2f} DH".replace(',', ' ')
            ice_display = ice if ice else ""

            tags = ('positive',) if solde > 0 else ('negative',) if solde < 0 else ()
            self.clients_tree.insert('', tk.END, values=(code, nom, categorie, ice_display, contact, email, solde_str), tags=tags)

        self.clients_tree.tag_configure('positive', foreground='#27ae60')
        self.clients_tree.tag_configure('negative', foreground='#e74c3c')

    def show_client_context_menu(self, event):
        """Affichage du menu contextuel pour les clients"""
        item = self.clients_tree.selection()[0] if self.clients_tree.selection() else None
        if item:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="✏️ Modifier", command=self.edit_client)
            menu.add_command(label="👁️ Voir détails", command=self.view_client)
            menu.add_separator()
            menu.add_command(label="🗑️ Supprimer", command=self.delete_client)
            menu.tk_popup(event.x_root, event.y_root)

    def edit_client(self, event=None):
        """Modifier un client"""
        selection = self.clients_tree.selection()
        if selection:
            # Récupérer les données du client sélectionné
            item = selection[0]
            values = self.clients_tree.item(item, 'values')
            code_client = values[0]

            # Récupérer toutes les données du client
            self.cursor.execute("""
                SELECT code, nom, categorie, ice, if_field, adresse, personne_contact,
                contact, n_fix, n_fax, adresse_electronique
                FROM clients WHERE code = ?
            """, (code_client,))

            client_data = self.cursor.fetchone()
            if client_data:
                self.open_client_form(client_data)

    def view_client(self):
        """Voir les détails d'un client"""
        selection = self.clients_tree.selection()
        if selection:
            # Récupérer les données du client sélectionné
            item = selection[0]
            values = self.clients_tree.item(item, 'values')
            code_client = values[0]

            # Récupérer toutes les données du client
            self.cursor.execute("""
                SELECT code, nom, categorie, ice, if_field, adresse, personne_contact,
                contact, n_fix, n_fax, adresse_electronique, solde, date_creation
                FROM clients WHERE code = ?
            """, (code_client,))

            client_data = self.cursor.fetchone()
            if client_data:
                # Créer une fenêtre de détails
                details_window = tk.Toplevel(self.root)
                details_window.title(f"Détails Client - {client_data[0]}")

                # Taille adaptative
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                window_width = min(600, int(screen_width * 0.4))
                window_height = min(700, int(screen_height * 0.7))

                x = (screen_width - window_width) // 2
                y = (screen_height - window_height) // 2
                details_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

                details_window.resizable(True, True)
                details_window.minsize(400, 500)
                details_window.transient(self.root)
                details_window.grab_set()

                # Frame principal
                main_frame = ttk.Frame(details_window, padding="20")
                main_frame.pack(fill=tk.BOTH, expand=True)

                # Titre
                title_label = ttk.Label(main_frame, text=f"Informations Client - {client_data[0]}", style='Title.TLabel')
                title_label.pack(pady=(0, 20))

                # Informations
                info_frame = ttk.LabelFrame(main_frame, text="Détails", padding="15")
                info_frame.pack(fill=tk.BOTH, expand=True)

                details = [
                    ("Code:", client_data[0]),
                    ("Nom/Entreprise:", client_data[1]),
                    ("Catégorie:", client_data[2]),
                    ("ICE:", client_data[3] or "N/A"),
                    ("IF:", client_data[4] or "N/A"),
                    ("Adresse:", client_data[5] or "N/A"),
                    ("Personne à contacter:", client_data[6] or "N/A"),
                    ("Contact:", client_data[7] or "N/A"),
                    ("N° Fix:", client_data[8] or "N/A"),
                    ("N° Fax:", client_data[9] or "N/A"),
                    ("Email:", client_data[10] or "N/A"),
                    ("Solde:", f"{client_data[11]:,.2f} DH".replace(',', ' ')),
                    ("Date création:", client_data[12])
                ]

                for i, (label, value) in enumerate(details):
                    ttk.Label(info_frame, text=label, style='Heading.TLabel').grid(row=i, column=0, sticky=tk.W, pady=2)
                    ttk.Label(info_frame, text=str(value)).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

                # Bouton fermer
                ttk.Button(main_frame, text="Fermer", command=details_window.destroy).pack(pady=20)

    def delete_client(self):
        """Supprimer un client"""
        selection = self.clients_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à supprimer.")
            return

        item = selection[0]
        values = self.clients_tree.item(item, 'values')
        code_client = values[0]
        nom_client = values[1]

        # Vérifier les dépendances
        try:
            # Vérifier s'il y a des factures liées
            self.cursor.execute("SELECT COUNT(*) FROM factures WHERE client_id = (SELECT id FROM clients WHERE code = ?)", (code_client,))
            factures_count = self.cursor.fetchone()[0]

            # Vérifier s'il y a des devis liés
            self.cursor.execute("SELECT COUNT(*) FROM devis WHERE client = ?", (nom_client,))
            devis_count = self.cursor.fetchone()[0]

            if factures_count > 0 or devis_count > 0:
                message = f"Impossible de supprimer le client {code_client} ({nom_client}).\n\n"
                message += f"Ce client a {factures_count} facture(s) et {devis_count} devis associé(s).\n"
                message += "Supprimez d'abord les documents associés."
                messagebox.showerror("Suppression impossible", message)
                return

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la vérification des dépendances: {str(e)}")
            return

        # Confirmation avec détails
        if messagebox.askyesno("Confirmation",
                              f"Êtes-vous sûr de vouloir supprimer le client ?\n\n"
                              f"Code: {code_client}\n"
                              f"Nom: {nom_client}\n\n"
                              f"Cette action est irréversible."):
            try:
                # Supprimer de la base de données
                self.cursor.execute("DELETE FROM clients WHERE code = ?", (code_client,))
                self.conn.commit()

                # Réorganiser les codes
                self.reorganize_client_codes()

                # Recharger les données
                self.load_clients_data()
                self.update_client_stats()

                messagebox.showinfo("Succès", f"Client {code_client} supprimé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")
                self.conn.rollback()

    def reorganize_client_codes(self):
        """Réorganiser les codes clients après suppression"""
        # Récupérer tous les clients triés par ID
        self.cursor.execute("SELECT id FROM clients ORDER BY id")
        clients = self.cursor.fetchall()

        # Mettre à jour les codes
        for i, (client_id,) in enumerate(clients, 1):
            new_code = f"C{i}"
            self.cursor.execute("UPDATE clients SET code = ? WHERE id = ?", (new_code, client_id))

        self.conn.commit()

    def get_next_client_code(self):
        """Générer le prochain code client"""
        self.cursor.execute("SELECT COUNT(*) FROM clients")
        count = self.cursor.fetchone()[0]
        return f"C{count + 1}"

    def open_client_form(self, client_data=None):
        """Ouvrir le formulaire d'ajout/modification de client"""
        # Créer la fenêtre
        form_window = tk.Toplevel(self.root)
        form_window.title("Nouveau Client" if not client_data else "Modifier Client")

        # Taille adaptative basée sur l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(700, int(screen_width * 0.6))
        window_height = min(800, int(screen_height * 0.8))

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        form_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Permettre le redimensionnement
        form_window.resizable(True, True)
        form_window.minsize(500, 600)

        form_window.transient(self.root)
        form_window.grab_set()

        # Frame principal avec scrollbar
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configuration pour le redimensionnement automatique
        form_window.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Variables pour les champs
        code_var = tk.StringVar(value=client_data[0] if client_data else self.get_next_client_code())
        nom_var = tk.StringVar(value=client_data[1] if client_data else "")
        categorie_var = tk.StringVar(value=client_data[2] if client_data else "Public")
        ice_var = tk.StringVar(value=client_data[3] if client_data else "")
        if_var = tk.StringVar(value=client_data[4] if client_data else "")
        adresse_var = tk.StringVar(value=client_data[5] if client_data else "")
        personne_contact_var = tk.StringVar(value=client_data[6] if client_data else "")
        contact_var = tk.StringVar(value=client_data[7] if client_data else "")
        n_fix_var = tk.StringVar(value=client_data[8] if client_data else "")
        n_fax_var = tk.StringVar(value=client_data[9] if client_data else "")
        email_var = tk.StringVar(value=client_data[10] if client_data else "")

        # Titre
        title_label = ttk.Label(main_frame, text="Informations Client", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        row = 1

        # Code (lecture seule)
        ttk.Label(main_frame, text="Code:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        code_entry = ttk.Entry(main_frame, textvariable=code_var, state='readonly', width=40)
        code_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Client
        ttk.Label(main_frame, text="Client:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(main_frame, textvariable=nom_var, width=40)
        nom_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Catégorie
        ttk.Label(main_frame, text="Catégorie:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        categorie_combo = ttk.Combobox(main_frame, textvariable=categorie_var,
                                      values=["Public", "Privé"], state="readonly", width=37)
        categorie_combo.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # ICE (conditional) - Create but don't grid initially
        ice_label = ttk.Label(main_frame, text="ICE:", style='Heading.TLabel')
        ice_entry = ttk.Entry(main_frame, textvariable=ice_var, width=40)
        ice_row = row

        # IF (conditional) - Create but don't grid initially
        if_label = ttk.Label(main_frame, text="IF:", style='Heading.TLabel')
        if_entry = ttk.Entry(main_frame, textvariable=if_var, width=40)
        if_row = row + 1

        # Adresse (conditional) - Create but don't grid initially
        adresse_label = ttk.Label(main_frame, text="Adresse:", style='Heading.TLabel')
        adresse_entry = ttk.Entry(main_frame, textvariable=adresse_var, width=40)
        adresse_row = row + 2

        # Skip rows for conditional fields - they will be managed by on_categorie_change
        row += 3

        # Personne à contacter
        ttk.Label(main_frame, text="Personne à contacter:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        personne_entry = ttk.Entry(main_frame, textvariable=personne_contact_var, width=40)
        personne_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Contact
        ttk.Label(main_frame, text="Contact:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        contact_entry = ttk.Entry(main_frame, textvariable=contact_var, width=40)
        contact_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # N Fix
        ttk.Label(main_frame, text="N° Fix:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        n_fix_entry = ttk.Entry(main_frame, textvariable=n_fix_var, width=40)
        n_fix_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # N Fax
        ttk.Label(main_frame, text="N° Fax:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        n_fax_entry = ttk.Entry(main_frame, textvariable=n_fax_var, width=40)
        n_fax_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Adresse électronique
        ttk.Label(main_frame, text="Adresse électronique:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        email_entry = ttk.Entry(main_frame, textvariable=email_var, width=40)
        email_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Configuration du redimensionnement
        main_frame.columnconfigure(1, weight=1)

        # Fonction pour gérer les champs conditionnels
        def on_categorie_change(*args):
            if categorie_var.get() == "Privé":
                # Si Privé: afficher et activer ICE, IF, Adresse (requis pour entreprises privées)
                ice_label.grid(row=ice_row, column=0, sticky=tk.W, pady=5)
                ice_entry.grid(row=ice_row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
                ice_entry.config(state='normal')
                if_label.grid(row=if_row, column=0, sticky=tk.W, pady=5)
                if_entry.grid(row=if_row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
                if_entry.config(state='normal')
                adresse_label.grid(row=adresse_row, column=0, sticky=tk.W, pady=5)
                adresse_entry.grid(row=adresse_row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
                adresse_entry.config(state='normal')
            else:
                # Si Public: cacher/désactiver ICE, IF, Adresse (entités gouvernementales n'en ont pas besoin)
                ice_label.grid_remove()
                ice_entry.grid_remove()
                ice_entry.config(state='disabled')
                if_label.grid_remove()
                if_entry.grid_remove()
                if_entry.config(state='disabled')
                adresse_label.grid_remove()
                adresse_entry.grid_remove()
                adresse_entry.config(state='disabled')
                # Vider les champs quand ils sont désactivés
                ice_var.set("")
                if_var.set("")
                adresse_var.set("")

        # Lier l'événement de changement de catégorie
        categorie_var.trace('w', on_categorie_change)

        # Appliquer l'état initial
        on_categorie_change()

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row+1, column=0, columnspan=2, pady=20)

        def save_client():
            # Validation
            if not nom_var.get().strip():
                messagebox.showerror("Erreur", "Le nom est obligatoire!")
                return

            if categorie_var.get() == "Privé":
                # Pour les clients privés, vérifier que ICE, IF, Adresse sont remplis
                if not ice_var.get().strip():
                    messagebox.showerror("Erreur", "ICE est obligatoire pour les clients privés!")
                    return
                if not if_var.get().strip():
                    messagebox.showerror("Erreur", "IF est obligatoire pour les clients privés!")
                    return
                if not adresse_var.get().strip():
                    messagebox.showerror("Erreur", "Adresse est obligatoire pour les clients privés!")
                    return

            # Sauvegarder
            try:
                if client_data:  # Modification
                    self.cursor.execute("""
                        UPDATE clients SET nom=?, categorie=?, ice=?, if_field=?, adresse=?,
                        personne_contact=?, contact=?, n_fix=?, n_fax=?, adresse_electronique=?
                        WHERE code=?
                    """, (nom_var.get(), categorie_var.get(), ice_var.get(), if_var.get(),
                          adresse_var.get(), personne_contact_var.get(), contact_var.get(),
                          n_fix_var.get(), n_fax_var.get(), email_var.get(), code_var.get()))
                else:  # Nouveau
                    self.cursor.execute("""
                        INSERT INTO clients (code, nom, categorie, ice, if_field, adresse,
                        personne_contact, contact, n_fix, n_fax, adresse_electronique, solde)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0.0)
                    """, (code_var.get(), nom_var.get(), categorie_var.get(), ice_var.get(),
                          if_var.get(), adresse_var.get(), personne_contact_var.get(),
                          contact_var.get(), n_fix_var.get(), n_fax_var.get(), email_var.get()))

                self.conn.commit()
                self.load_clients_data()
                form_window.destroy()
                messagebox.showinfo("Succès", "Client sauvegardé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        def cancel():
            form_window.destroy()

        ttk.Button(buttons_frame, text="💾 Sauvegarder", command=save_client,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ Annuler", command=cancel).pack(side=tk.LEFT)

        # Focus sur le nom
        nom_entry.focus()

    def update_client_stats(self):
        """Mise à jour des statistiques des clients"""
        # Compter le nombre total de clients
        self.cursor.execute("SELECT COUNT(*) FROM clients")
        total_clients = self.cursor.fetchone()[0]

        # Compter par catégorie
        self.cursor.execute("SELECT COUNT(*) FROM clients WHERE categorie = 'Public'")
        public_count = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM clients WHERE categorie = 'Privé'")
        prive_count = self.cursor.fetchone()[0]

        # Mettre à jour le label des statistiques
        stats_text = f"Total: {total_clients} clients ({public_count} Public, {prive_count} Privé)"
        self.stats_label.config(text=stats_text)

    def on_client_selection(self, event):
        """Gestion de la sélection d'un client"""
        selection = self.clients_tree.selection()
        if selection:
            item = selection[0]
            values = self.clients_tree.item(item, 'values')
            code_client = values[0]
            nom_client = values[1]
            self.selection_label.config(text=f"Sélectionné: {code_client} - {nom_client}")
        else:
            self.selection_label.config(text="")

    def exporter_clients(self):
        """Exporter la liste des clients"""
        try:
            from tkinter import filedialog
            import csv

            # Demander où sauvegarder
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Exporter les clients"
            )

            if filename:
                # Récupérer toutes les données
                self.cursor.execute("""
                    SELECT code, nom, categorie, ice, if_field, adresse,
                    personne_contact, contact, n_fix, n_fax, adresse_electronique, solde
                    FROM clients ORDER BY code
                """)

                clients = self.cursor.fetchall()

                # Écrire le fichier CSV
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # En-têtes
                    writer.writerow([
                        'Code', 'Nom/Entreprise', 'Catégorie', 'ICE', 'IF', 'Adresse',
                        'Personne Contact', 'Contact', 'N° Fix', 'N° Fax', 'Email', 'Solde'
                    ])

                    # Données
                    for client in clients:
                        writer.writerow(client)

                messagebox.showinfo("Succès", f"Clients exportés vers:\n{filename}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    # Fonctions pour les produits
    def load_produits_data(self):
        """Chargement des données produits"""
        # Vider le treeview
        for item in self.produits_tree.get_children():
            self.produits_tree.delete(item)

        # Charger les données
        self.cursor.execute("""
            SELECT code, famille, produit, unite, date_achat, fournisseur, quantite, tva, prix_achat_ttc, mode_paiement
            FROM produits
            ORDER BY code
        """)

        for row in self.cursor.fetchall():
            code, famille, produit, unite, date_achat, fournisseur, quantite, tva, prix_ttc, mode_paiement = row
            prix_str = f"{prix_ttc:,.2f} DH".replace(',', ' ')
            tva_str = f"{tva:.0f}%"
            quantite_str = f"{quantite:.1f}" if quantite % 1 != 0 else f"{int(quantite)}"
            unite_str = unite if unite else "unité"

            self.produits_tree.insert('', tk.END, values=(code, famille, produit, unite_str, date_achat, fournisseur, quantite_str, tva_str, prix_str, mode_paiement))

        # Mettre à jour les statistiques
        self.update_produits_stats()

    def update_produits_stats(self):
        """Mise à jour des statistiques des produits"""
        # Compter le nombre total de produits
        self.cursor.execute("SELECT COUNT(*) FROM produits")
        total_produits = self.cursor.fetchone()[0]

        # Calculer la valeur totale
        self.cursor.execute("SELECT SUM(prix_achat_ttc) FROM produits")
        valeur_totale = self.cursor.fetchone()[0] or 0

        # Mettre à jour le label des statistiques
        stats_text = f"Total: {total_produits} produits (Valeur: {valeur_totale:,.2f} DH)".replace(',', ' ')
        self.produits_stats_label.config(text=stats_text)

    def filter_produits(self, event=None):
        """Filtrage des produits"""
        search_term = self.search_produits_var.get().lower()

        # Vider et recharger avec filtre
        for item in self.produits_tree.get_children():
            self.produits_tree.delete(item)

        # Requête avec filtre
        self.cursor.execute("""
            SELECT code, famille, produit, date_achat, fournisseur, quantite, tva, prix_achat_ttc, mode_paiement
            FROM produits
            WHERE LOWER(code) LIKE ? OR LOWER(famille) LIKE ? OR LOWER(produit) LIKE ? OR LOWER(fournisseur) LIKE ?
            ORDER BY code
        """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        for row in self.cursor.fetchall():
            code, famille, produit, date_achat, fournisseur, quantite, tva, prix_ttc, mode_paiement = row
            prix_str = f"{prix_ttc:,.2f} DH".replace(',', ' ')
            tva_str = f"{tva:.0f}%"
            quantite_str = f"{quantite:.1f}" if quantite % 1 != 0 else f"{int(quantite)}"

            self.produits_tree.insert('', tk.END, values=(code, famille, produit, date_achat, fournisseur, quantite_str, tva_str, prix_str, mode_paiement))

    def on_produit_selection(self, event):
        """Gestion de la sélection d'un produit"""
        selection = self.produits_tree.selection()
        if selection:
            item = selection[0]
            values = self.produits_tree.item(item, 'values')
            code_produit = values[0]
            nom_produit = values[2]
            self.produits_selection_label.config(text=f"Sélectionné: {code_produit} - {nom_produit}")
        else:
            self.produits_selection_label.config(text="")

    def nouveau_produit(self):
        """Ouvrir la fenêtre de nouveau produit"""
        self.open_produit_form()

    def get_next_produit_code(self, famille):
        """Générer le prochain code produit selon la famille"""
        # Obtenir le préfixe selon la famille
        prefixes = {
            "ELECTRICITE": "EL",
            "BUREAU": "BU",
            "INFORMATIQUE": "IN",
            "MOBILIER": "MO",
            "OUTILLAGE": "OU"
        }

        prefix = prefixes.get(famille, "PR")  # PR par défaut

        # Compter les produits existants avec ce préfixe
        self.cursor.execute("SELECT COUNT(*) FROM produits WHERE code LIKE ?", (f"{prefix}%",))
        count = self.cursor.fetchone()[0]

        return f"{prefix}{count + 1:03d}"  # Format: EL001, BU002, etc.

    def open_produit_form(self, produit_data=None):
        """Ouvrir le formulaire d'ajout/modification de produit"""
        # Créer la fenêtre
        form_window = tk.Toplevel(self.root)
        form_window.title("Nouveau Produit" if not produit_data else "Modifier Produit")

        # Taille adaptative basée sur l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(800, int(screen_width * 0.7))
        window_height = min(900, int(screen_height * 0.9))

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        form_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Permettre le redimensionnement
        form_window.resizable(True, True)
        form_window.minsize(600, 700)

        form_window.transient(self.root)
        form_window.grab_set()

        # Frame principal avec scrollbar
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configuration pour le redimensionnement automatique
        form_window.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Variables pour les champs
        famille_var = tk.StringVar(value=produit_data[1] if produit_data else "ELECTRICITE")
        code_var = tk.StringVar(value=produit_data[0] if produit_data else "")
        produit_var = tk.StringVar(value=produit_data[2] if produit_data else "")
        unite_var = tk.StringVar(value=produit_data[12] if produit_data and len(produit_data) > 12 else "unité")
        date_achat_var = tk.StringVar(value=produit_data[3] if produit_data else "01/01/2025")
        fournisseur_var = tk.StringVar(value=produit_data[4] if produit_data else "")
        n_facture_var = tk.StringVar(value=produit_data[5] if produit_data and len(produit_data) > 5 else "")
        prix_ht_var = tk.StringVar(value=str(produit_data[6]) if produit_data and len(produit_data) > 6 else "15,00")
        tva_var = tk.StringVar(value=str(int(produit_data[7])) if produit_data and len(produit_data) > 7 else "20")
        prix_ttc_var = tk.StringVar(value="HT + TVA")
        quantite_var = tk.StringVar(value=str(produit_data[9]) if produit_data and len(produit_data) > 9 else "1")
        mode_paiement_var = tk.StringVar(value=produit_data[10] if produit_data and len(produit_data) > 10 else "Chèque")
        date_paiement_var = tk.StringVar(value=produit_data[11] if produit_data and len(produit_data) > 11 else "")

        # Titre
        title_label = ttk.Label(main_frame, text="Informations Produit/Achat", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        row = 1

        # Famille
        ttk.Label(main_frame, text="Famille:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)

        # Family selection with add button
        family_selection_frame = ttk.Frame(main_frame)
        family_selection_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        family_selection_frame.columnconfigure(0, weight=1)

        # Liste des familles (sans l'option d'ajout)
        familles_list = ["ELECTRICITE", "BUREAU", "INFORMATIQUE", "MOBILIER", "OUTILLAGE"]
        famille_combo = ttk.Combobox(family_selection_frame, textvariable=famille_var,
                                    values=familles_list,
                                    state="readonly", width=45)
        famille_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add family button
        def add_family_and_refresh():
            self.add_new_family_direct(famille_combo, famille_var)

        add_family_btn = ttk.Button(family_selection_frame, text="➕", width=3,
                                   command=add_family_and_refresh)
        add_family_btn.grid(row=0, column=1)
        row += 1

        # Code (automatique)
        ttk.Label(main_frame, text="CODE:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        code_entry = ttk.Entry(main_frame, textvariable=code_var, state='readonly', width=50)
        code_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Produit
        ttk.Label(main_frame, text="Produit:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        produit_entry = ttk.Entry(main_frame, textvariable=produit_var, width=50)
        produit_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Unité
        ttk.Label(main_frame, text="Unité:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        unit_frame, unite_combo, add_unit_btn = self.create_unit_field_with_add_button(
            main_frame, unite_var, row=row, column=1, width=45
        )
        row += 1

        # Date achat
        ttk.Label(main_frame, text="DATE ACHAT:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        date_achat_entry = ttk.Entry(main_frame, textvariable=date_achat_var, width=50)
        date_achat_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        # Appliquer le formatage de date
        self.format_date_field(date_achat_entry)
        row += 1

        # Fournisseur
        ttk.Label(main_frame, text="FOURNISSEUR:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)

        # Charger uniquement les fournisseurs depuis la base de données
        try:
            self.cursor.execute("SELECT nom FROM fournisseurs ORDER BY nom")
            fournisseurs_db = [row[0] for row in self.cursor.fetchall()]
        except Exception as e:
            print(f"Error loading suppliers: {e}")
            fournisseurs_db = []

        # Ajouter une option par défaut si aucun fournisseur n'existe
        if not fournisseurs_db:
            fournisseurs_db = ["Aucun fournisseur disponible - Veuillez en ajouter un"]

        # Supplier selection with add button
        supplier_selection_frame = ttk.Frame(main_frame)
        supplier_selection_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        supplier_selection_frame.columnconfigure(0, weight=1)

        fournisseur_combo = ttk.Combobox(supplier_selection_frame, textvariable=fournisseur_var,
                                        values=fournisseurs_db, width=45)
        fournisseur_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add supplier button
        def add_supplier_and_refresh():
            self.open_fournisseur_form()
            # Refresh supplier list after potential addition
            self.root.after(100, lambda: self.refresh_supplier_dropdown(fournisseur_combo, fournisseur_var))

        add_supplier_btn = ttk.Button(supplier_selection_frame, text="➕", width=3,
                                     command=add_supplier_and_refresh)
        add_supplier_btn.grid(row=0, column=1)

        # Store reference to combo for refreshing
        self.current_fournisseur_combo = fournisseur_combo

        row += 1

        # N facture d'achat
        ttk.Label(main_frame, text="N facture d'achat:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        n_facture_entry = ttk.Entry(main_frame, textvariable=n_facture_var, width=50)
        n_facture_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Prix d'achat HT
        ttk.Label(main_frame, text="PRIX D'achat ht:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        prix_ht_entry = ttk.Entry(main_frame, textvariable=prix_ht_var, width=50)
        prix_ht_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # TVA
        ttk.Label(main_frame, text="TVA:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        tva_combo = ttk.Combobox(main_frame, textvariable=tva_var,
                                values=["0", "7", "10", "14", "20"],
                                state="readonly", width=50)
        tva_combo.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Quantité
        ttk.Label(main_frame, text="QUANTITÉ:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        quantite_entry = ttk.Entry(main_frame, textvariable=quantite_var, width=50)
        quantite_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Prix d'achat TTC
        ttk.Label(main_frame, text="PRIX D'achat TTC:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        prix_ttc_entry = ttk.Entry(main_frame, textvariable=prix_ttc_var, state='readonly', width=50)
        prix_ttc_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Mode de paiement
        ttk.Label(main_frame, text="MODE DE PAIEMENT:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)

        # Mode de paiement selection with add button
        mode_selection_frame = ttk.Frame(main_frame)
        mode_selection_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        mode_selection_frame.columnconfigure(0, weight=1)

        mode_combo = ttk.Combobox(mode_selection_frame, textvariable=mode_paiement_var,
                                 values=["Chèque", "Espèces", "Carte", "Virement"],
                                 state="readonly", width=45)
        mode_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add mode button
        def add_mode_and_refresh():
            self.add_new_payment_mode(mode_combo, mode_paiement_var)

        add_mode_btn = ttk.Button(mode_selection_frame, text="➕", width=3,
                                 command=add_mode_and_refresh)
        add_mode_btn.grid(row=0, column=1)
        row += 1

        # Date de paiement
        ttk.Label(main_frame, text="date de paiement:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        date_paiement_entry = ttk.Entry(main_frame, textvariable=date_paiement_var, width=50)
        date_paiement_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        # Appliquer le formatage de date
        self.format_date_field(date_paiement_entry)
        row += 1

        # Configuration du redimensionnement
        main_frame.columnconfigure(1, weight=1)

        # Fonction pour mettre à jour le code automatiquement
        def on_famille_change(*args):
            if not produit_data:  # Seulement pour les nouveaux produits
                famille_selectionnee = famille_var.get()
                # Ignorer l'option d'ajout
                if famille_selectionnee and famille_selectionnee != "--- Ajouter nouvelle famille ---":
                    new_code = self.get_next_produit_code(famille_selectionnee)
                    code_var.set(new_code)

        # Fonction pour calculer le prix TTC
        def calculate_ttc(*args):
            try:
                prix_ht = float(prix_ht_var.get().replace(',', '.'))
                tva_rate = float(tva_var.get()) / 100.0  # Convertir le pourcentage en décimal
                tva_amount = prix_ht * tva_rate
                prix_ttc = prix_ht + tva_amount
                prix_ttc_var.set(f"{prix_ttc:.2f}".replace('.', ','))
            except:
                prix_ttc_var.set("HT + TVA")

        # Lier les événements
        famille_var.trace('w', on_famille_change)
        prix_ht_var.trace('w', calculate_ttc)
        tva_var.trace('w', calculate_ttc)  # Recalculer quand la TVA change

        # Appliquer l'état initial
        on_famille_change()
        calculate_ttc()

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row+1, column=0, columnspan=2, pady=20)

        def save_produit():
            # Validation
            if not produit_var.get().strip():
                messagebox.showerror("Erreur", "Le nom du produit est obligatoire!")
                return

            # Validate supplier exists in database
            if fournisseur_var.get() and fournisseur_var.get() != "Aucun fournisseur disponible - Veuillez en ajouter un":
                try:
                    self.cursor.execute("SELECT COUNT(*) FROM fournisseurs WHERE nom=?", (fournisseur_var.get(),))
                    if self.cursor.fetchone()[0] == 0:
                        messagebox.showerror("Erreur", f"Le fournisseur '{fournisseur_var.get()}' n'existe pas dans la base de données!")
                        return
                except Exception as e:
                    print(f"Error validating supplier: {e}")
                    messagebox.showerror("Erreur", "Erreur lors de la validation du fournisseur!")
                    return

            if not prix_ht_var.get().strip():
                messagebox.showerror("Erreur", "Le prix HT est obligatoire!")
                return

            if not quantite_var.get().strip():
                messagebox.showerror("Erreur", "La quantité est obligatoire!")
                return

            # Sauvegarder
            try:
                prix_ht = float(prix_ht_var.get().replace(',', '.'))
                tva_rate = float(tva_var.get())
                quantite = float(quantite_var.get().replace(',', '.'))

                if quantite < 0:
                    messagebox.showerror("Erreur", "La quantité ne peut pas être négative!")
                    return

                prix_ttc = prix_ht * (1 + tva_rate / 100.0)

                if produit_data:  # Modification
                    self.cursor.execute("""
                        UPDATE produits SET famille=?, produit=?, unite=?, date_achat=?, fournisseur=?,
                        n_facture_achat=?, prix_achat_ht=?, tva=?, prix_achat_ttc=?, quantite=?, mode_paiement=?, date_paiement=?
                        WHERE code=?
                    """, (famille_var.get(), produit_var.get(), unite_var.get(), date_achat_var.get(), fournisseur_var.get(),
                          n_facture_var.get(), prix_ht, tva_rate, prix_ttc, quantite, mode_paiement_var.get(),
                          date_paiement_var.get(), code_var.get()))
                else:  # Nouveau
                    self.cursor.execute("""
                        INSERT INTO produits (code, famille, produit, unite, date_achat, fournisseur,
                        n_facture_achat, prix_achat_ht, tva, prix_achat_ttc, quantite, mode_paiement, date_paiement)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (code_var.get(), famille_var.get(), produit_var.get(), unite_var.get(), date_achat_var.get(),
                          fournisseur_var.get(), n_facture_var.get(), prix_ht, tva_rate, prix_ttc, quantite,
                          mode_paiement_var.get(), date_paiement_var.get()))

                self.conn.commit()
                self.load_produits_data()
                form_window.destroy()
                messagebox.showinfo("Succès", "Produit sauvegardé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        def cancel():
            form_window.destroy()

        ttk.Button(buttons_frame, text="💾 Sauvegarder", command=save_produit,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ Annuler", command=cancel).pack(side=tk.LEFT)

        # Focus sur le produit
        produit_entry.focus()

    # Fonctions utilitaires pour le formulaire produit
    def select_or_add_famille(self):
        """Sélectionner ou ajouter une famille de produit"""
        # Créer une fenêtre de dialogue simple
        dialog = tk.Toplevel(self.root)
        dialog.title("Sélectionner Famille")

        # Taille adaptative
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(500, int(screen_width * 0.3))
        window_height = min(400, int(screen_height * 0.4))

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

        dialog.resizable(True, True)
        dialog.minsize(300, 250)
        dialog.transient(self.root)
        dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Familles disponibles:", style='Heading.TLabel').pack(pady=(0, 10))

        # Liste des familles
        familles = ["ELECTRICITE", "BUREAU", "INFORMATIQUE", "MOBILIER", "OUTILLAGE", "PLOMBERIE", "PEINTURE"]

        listbox = tk.Listbox(main_frame, height=8)
        for famille in familles:
            listbox.insert(tk.END, famille)
        listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        ttk.Button(buttons_frame, text="Sélectionner", command=dialog.destroy).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Annuler", command=dialog.destroy).pack(side=tk.LEFT)

    def select_date(self, date_var):
        """Sélecteur de date simple"""
        from datetime import datetime

        # Utiliser la date actuelle par défaut
        today = datetime.now()
        date_str = today.strftime("%d/%m/%Y")
        date_var.set(date_str)

        messagebox.showinfo("Date", f"Date définie à: {date_str}")

    def select_fournisseur(self):
        """Sélectionner un fournisseur"""
        # Créer une fenêtre de dialogue
        dialog = tk.Toplevel(self.root)
        dialog.title("Sélectionner Fournisseur")

        # Taille adaptative
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(500, int(screen_width * 0.3))
        window_height = min(400, int(screen_height * 0.4))

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

        dialog.resizable(True, True)
        dialog.minsize(300, 250)
        dialog.transient(self.root)
        dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Fournisseurs disponibles:", style='Heading.TLabel').pack(pady=(0, 10))

        # Charger les fournisseurs depuis la base de données
        try:
            self.cursor.execute("SELECT nom FROM fournisseurs ORDER BY nom")
            fournisseurs = [row[0] for row in self.cursor.fetchall()]
        except Exception as e:
            print(f"Error loading suppliers: {e}")
            fournisseurs = []

        if not fournisseurs:
            fournisseurs = ["Aucun fournisseur disponible - Veuillez en ajouter un"]

        listbox = tk.Listbox(main_frame, height=8)
        for fournisseur in fournisseurs:
            listbox.insert(tk.END, fournisseur)
        listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        ttk.Button(buttons_frame, text="Sélectionner", command=dialog.destroy).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Annuler", command=dialog.destroy).pack(side=tk.LEFT)

    def select_payment_mode(self):
        """Sélectionner un mode de paiement"""
        messagebox.showinfo("Mode de Paiement", "Utilisez la liste déroulante pour sélectionner le mode de paiement")

    def ajouter_nouvelle_famille(self, famille_var, famille_combo):
        """Ajouter une nouvelle famille de produit"""
        # Créer une fenêtre de dialogue pour saisir la nouvelle famille
        dialog = tk.Toplevel(self.root)
        dialog.title("Ajouter Nouvelle Famille")

        # Taille et position
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(400, int(screen_width * 0.25))
        window_height = min(200, int(screen_height * 0.2))

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="Nouvelle Famille:", style='Heading.TLabel').pack(pady=(0, 10))

        # Champ de saisie
        nouvelle_famille_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=nouvelle_famille_var, width=30)
        entry.pack(pady=(0, 15))
        entry.focus()

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        def ajouter():
            nouvelle_famille = nouvelle_famille_var.get().strip().upper()
            if nouvelle_famille:
                # Récupérer les valeurs actuelles
                valeurs_actuelles = list(famille_combo['values'])

                # Vérifier si la famille n'existe pas déjà
                if nouvelle_famille not in valeurs_actuelles:
                    # Ajouter la nouvelle famille
                    valeurs_actuelles.append(nouvelle_famille)
                    valeurs_actuelles.sort()  # Trier alphabétiquement

                    # Mettre à jour la combobox
                    famille_combo['values'] = valeurs_actuelles
                    famille_var.set(nouvelle_famille)

                    dialog.destroy()
                    messagebox.showinfo("Succès", f"Famille '{nouvelle_famille}' ajoutée avec succès!")
                else:
                    messagebox.showwarning("Attention", f"La famille '{nouvelle_famille}' existe déjà!")
            else:
                messagebox.showerror("Erreur", "Veuillez saisir un nom de famille!")

        def annuler():
            dialog.destroy()

        # Boutons
        ttk.Button(buttons_frame, text="✓ Ajouter", command=ajouter,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="✗ Annuler", command=annuler).pack(side=tk.LEFT)

        # Permettre d'ajouter avec Entrée
        entry.bind('<Return>', lambda e: ajouter())

    def add_new_family_direct(self, famille_combo, famille_var):
        """Add new family with direct button approach"""
        self.ajouter_nouvelle_famille_inline(famille_var, famille_combo)

    def add_new_nature_option(self, nature_combo, nature_var):
        """Add new nature de prestation option"""
        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Nouvelle Nature de Prestation")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="Nouvelle Nature de Prestation:", style='Heading.TLabel').pack(pady=(0, 10))

        # Champ de saisie
        nouvelle_nature_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=nouvelle_nature_var, width=30)
        entry.pack(pady=(0, 15))
        entry.focus()

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        def ajouter():
            nouvelle_nature = nouvelle_nature_var.get().strip()
            if nouvelle_nature:
                # Récupérer les valeurs actuelles
                valeurs_actuelles = list(nature_combo['values'])

                # Vérifier si la nature n'existe pas déjà
                if nouvelle_nature not in valeurs_actuelles:
                    # Ajouter la nouvelle nature
                    valeurs_actuelles.append(nouvelle_nature)
                    valeurs_actuelles.sort()  # Trier alphabétiquement

                    # Mettre à jour la combobox
                    nature_combo['values'] = valeurs_actuelles
                    nature_var.set(nouvelle_nature)

                    dialog.destroy()
                    messagebox.showinfo("Succès", f"Nature '{nouvelle_nature}' ajoutée avec succès!")
                else:
                    messagebox.showwarning("Attention", f"La nature '{nouvelle_nature}' existe déjà!")
            else:
                messagebox.showerror("Erreur", "Veuillez saisir une nature de prestation!")

        def annuler():
            dialog.destroy()

        # Boutons
        ttk.Button(buttons_frame, text="✓ Ajouter", command=ajouter,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="✗ Annuler", command=annuler).pack(side=tk.LEFT)

        # Permettre d'ajouter avec Entrée
        entry.bind('<Return>', lambda e: ajouter())

    def add_new_type_option(self, type_combo, type_var):
        """Add new type de marché option"""
        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Nouveau Type de Marché")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="Nouveau Type de Marché:", style='Heading.TLabel').pack(pady=(0, 10))

        # Champ de saisie
        nouveau_type_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=nouveau_type_var, width=30)
        entry.pack(pady=(0, 15))
        entry.focus()

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        def ajouter():
            nouveau_type = nouveau_type_var.get().strip().upper()
            if nouveau_type:
                # Récupérer les valeurs actuelles
                valeurs_actuelles = list(type_combo['values'])

                # Vérifier si le type n'existe pas déjà
                if nouveau_type not in valeurs_actuelles:
                    # Ajouter le nouveau type
                    valeurs_actuelles.append(nouveau_type)
                    valeurs_actuelles.sort()  # Trier alphabétiquement

                    # Mettre à jour la combobox
                    type_combo['values'] = valeurs_actuelles
                    type_var.set(nouveau_type)

                    dialog.destroy()
                    messagebox.showinfo("Succès", f"Type '{nouveau_type}' ajouté avec succès!")
                else:
                    messagebox.showwarning("Attention", f"Le type '{nouveau_type}' existe déjà!")
            else:
                messagebox.showerror("Erreur", "Veuillez saisir un type de marché!")

        def annuler():
            dialog.destroy()

        # Boutons
        ttk.Button(buttons_frame, text="✓ Ajouter", command=ajouter,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="✗ Annuler", command=annuler).pack(side=tk.LEFT)

        # Permettre d'ajouter avec Entrée
        entry.bind('<Return>', lambda e: ajouter())

    def ajouter_nouvelle_famille_inline(self, famille_var, famille_combo):
        """Ajouter une nouvelle famille depuis la liste déroulante"""
        # Créer une fenêtre de dialogue pour saisir la nouvelle famille
        dialog = tk.Toplevel(self.root)
        dialog.title("Ajouter Nouvelle Famille")

        # Taille et position
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(400, int(screen_width * 0.25))
        window_height = min(200, int(screen_height * 0.2))

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="Nouvelle Famille:", style='Heading.TLabel').pack(pady=(0, 10))

        # Champ de saisie
        nouvelle_famille_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=nouvelle_famille_var, width=30)
        entry.pack(pady=(0, 15))
        entry.focus()

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        def ajouter():
            nouvelle_famille = nouvelle_famille_var.get().strip().upper()
            if nouvelle_famille:
                # Récupérer les valeurs actuelles
                valeurs_actuelles = list(famille_combo['values'])

                # Vérifier si la famille n'existe pas déjà
                if nouvelle_famille not in valeurs_actuelles:
                    # Ajouter la nouvelle famille
                    valeurs_actuelles.append(nouvelle_famille)
                    valeurs_actuelles.sort()  # Trier alphabétiquement

                    # Mettre à jour la combobox
                    famille_combo['values'] = valeurs_actuelles
                    famille_var.set(nouvelle_famille)

                    dialog.destroy()
                    messagebox.showinfo("Succès", f"Famille '{nouvelle_famille}' ajoutée avec succès!")
                else:
                    messagebox.showwarning("Attention", f"La famille '{nouvelle_famille}' existe déjà!")
            else:
                messagebox.showerror("Erreur", "Veuillez saisir un nom de famille!")

        def annuler():
            dialog.destroy()

        # Boutons
        ttk.Button(buttons_frame, text="✓ Ajouter", command=ajouter,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="✗ Annuler", command=annuler).pack(side=tk.LEFT)

        # Permettre d'ajouter avec Entrée
        entry.bind('<Return>', lambda e: ajouter())

    def ajouter_nouveau_fournisseur(self, fournisseur_var, fournisseur_combo):
        """Ajouter un nouveau fournisseur"""
        # Créer une fenêtre de dialogue pour saisir le nouveau fournisseur
        dialog = tk.Toplevel(self.root)
        dialog.title("Ajouter Nouveau Fournisseur")

        # Taille et position
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(400, int(screen_width * 0.25))
        window_height = min(200, int(screen_height * 0.2))

        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="Nouveau Fournisseur:", style='Heading.TLabel').pack(pady=(0, 10))

        # Champ de saisie
        nouveau_fournisseur_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=nouveau_fournisseur_var, width=30)
        entry.pack(pady=(0, 15))
        entry.focus()

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        def ajouter():
            nouveau_fournisseur = nouveau_fournisseur_var.get().strip()
            if nouveau_fournisseur:
                # Récupérer les valeurs actuelles
                valeurs_actuelles = list(fournisseur_combo['values'])

                # Vérifier si le fournisseur n'existe pas déjà
                if nouveau_fournisseur not in valeurs_actuelles:
                    # Ajouter le nouveau fournisseur
                    valeurs_actuelles.append(nouveau_fournisseur)
                    valeurs_actuelles.sort()  # Trier alphabétiquement

                    # Mettre à jour la combobox
                    fournisseur_combo['values'] = valeurs_actuelles
                    fournisseur_var.set(nouveau_fournisseur)

                    dialog.destroy()
                    messagebox.showinfo("Succès", f"Fournisseur '{nouveau_fournisseur}' ajouté avec succès!")
                else:
                    messagebox.showwarning("Attention", f"Le fournisseur '{nouveau_fournisseur}' existe déjà!")
            else:
                messagebox.showerror("Erreur", "Veuillez saisir un nom de fournisseur!")

        def annuler():
            dialog.destroy()

        # Boutons
        ttk.Button(buttons_frame, text="✓ Ajouter", command=ajouter,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="✗ Annuler", command=annuler).pack(side=tk.LEFT)

        # Permettre d'ajouter avec Entrée
        entry.bind('<Return>', lambda e: ajouter())

    def add_new_payment_mode(self, mode_combo, mode_var):
        """Add new payment mode option"""
        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Nouveau Mode de Paiement")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Frame principal
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        ttk.Label(main_frame, text="Nouveau Mode de Paiement:", style='Heading.TLabel').pack(pady=(0, 10))

        # Champ de saisie
        nouveau_mode_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=nouveau_mode_var, width=30)
        entry.pack(pady=(0, 15))
        entry.focus()

        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()

        def ajouter():
            nouveau_mode = nouveau_mode_var.get().strip()
            if nouveau_mode:
                # Récupérer les valeurs actuelles
                valeurs_actuelles = list(mode_combo['values'])

                # Vérifier si le mode n'existe pas déjà
                if nouveau_mode not in valeurs_actuelles:
                    # Ajouter le nouveau mode
                    valeurs_actuelles.append(nouveau_mode)
                    valeurs_actuelles.sort()  # Trier alphabétiquement

                    # Mettre à jour la combobox
                    mode_combo['values'] = valeurs_actuelles
                    mode_var.set(nouveau_mode)

                    dialog.destroy()
                    messagebox.showinfo("Succès", f"Mode de paiement '{nouveau_mode}' ajouté avec succès!")
                else:
                    messagebox.showwarning("Attention", f"Le mode '{nouveau_mode}' existe déjà!")
            else:
                messagebox.showerror("Erreur", "Veuillez saisir un mode de paiement!")

        def annuler():
            dialog.destroy()

        # Boutons
        ttk.Button(buttons_frame, text="✓ Ajouter", command=ajouter,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="✗ Annuler", command=annuler).pack(side=tk.LEFT)

        # Permettre d'ajouter avec Entrée
        entry.bind('<Return>', lambda e: ajouter())

    def show_produit_context_menu(self, event):
        """Affichage du menu contextuel pour les produits"""
        item = self.produits_tree.selection()[0] if self.produits_tree.selection() else None
        if item:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="✏️ Modifier", command=self.edit_produit)
            menu.add_command(label="👁️ Voir détails", command=self.view_produit)
            menu.add_separator()
            menu.add_command(label="🗑️ Supprimer", command=self.delete_produit)
            menu.tk_popup(event.x_root, event.y_root)

    def edit_produit(self, event=None):
        """Modifier un produit"""
        selection = self.produits_tree.selection()
        if selection:
            # Récupérer les données du produit sélectionné
            item = selection[0]
            values = self.produits_tree.item(item, 'values')
            code_produit = values[0]

            # Récupérer toutes les données du produit
            self.cursor.execute("""
                SELECT code, famille, produit, date_achat, fournisseur, n_facture_achat,
                prix_achat_ht, tva, prix_achat_ttc, quantite, mode_paiement, date_paiement, unite
                FROM produits WHERE code = ?
            """, (code_produit,))

            produit_data = self.cursor.fetchone()
            if produit_data:
                self.open_produit_form(produit_data)

    def view_produit(self):
        """Voir les détails d'un produit"""
        selection = self.produits_tree.selection()
        if selection:
            # Récupérer les données du produit sélectionné
            item = selection[0]
            values = self.produits_tree.item(item, 'values')
            code_produit = values[0]

            # Récupérer toutes les données du produit
            self.cursor.execute("""
                SELECT code, famille, produit, date_achat, fournisseur, n_facture_achat,
                prix_achat_ht, tva, prix_achat_ttc, quantite, mode_paiement, date_paiement, date_creation
                FROM produits WHERE code = ?
            """, (code_produit,))

            produit_data = self.cursor.fetchone()
            if produit_data:
                # Créer une fenêtre de détails
                details_window = tk.Toplevel(self.root)
                details_window.title(f"Détails Produit - {produit_data[0]}")

                # Taille adaptative
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                window_width = min(600, int(screen_width * 0.4))
                window_height = min(700, int(screen_height * 0.7))

                x = (screen_width - window_width) // 2
                y = (screen_height - window_height) // 2
                details_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

                details_window.resizable(True, True)
                details_window.minsize(400, 500)
                details_window.transient(self.root)
                details_window.grab_set()

                # Frame principal
                main_frame = ttk.Frame(details_window, padding="20")
                main_frame.pack(fill=tk.BOTH, expand=True)

                # Titre
                title_label = ttk.Label(main_frame, text=f"Informations Produit - {produit_data[0]}", style='Title.TLabel')
                title_label.pack(pady=(0, 20))

                # Informations
                info_frame = ttk.LabelFrame(main_frame, text="Détails", padding="15")
                info_frame.pack(fill=tk.BOTH, expand=True)

                details = [
                    ("Code:", produit_data[0]),
                    ("Famille:", produit_data[1]),
                    ("Produit:", produit_data[2]),
                    ("Date Achat:", produit_data[3]),
                    ("Fournisseur:", produit_data[4] or "N/A"),
                    ("N° Facture:", produit_data[5] or "N/A"),
                    ("Prix HT:", f"{produit_data[6]:,.2f} DH".replace(',', ' ')),
                    ("TVA:", f"{produit_data[7]}%"),
                    ("Prix TTC:", f"{produit_data[8]:,.2f} DH".replace(',', ' ')),
                    ("Quantité:", f"{produit_data[9]:.1f}" if produit_data[9] % 1 != 0 else f"{int(produit_data[9])}"),
                    ("Mode Paiement:", produit_data[10] or "N/A"),
                    ("Date Paiement:", produit_data[11] or "N/A"),
                    ("Date Création:", produit_data[12])
                ]

                for i, (label, value) in enumerate(details):
                    ttk.Label(info_frame, text=label, style='Heading.TLabel').grid(row=i, column=0, sticky=tk.W, pady=2)
                    ttk.Label(info_frame, text=str(value)).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

                # Bouton fermer
                ttk.Button(main_frame, text="Fermer", command=details_window.destroy).pack(pady=20)

    def delete_produit(self):
        """Supprimer un produit"""
        selection = self.produits_tree.selection()
        if selection:
            if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer ce produit ?"):
                # Récupérer le code du produit sélectionné
                item = selection[0]
                values = self.produits_tree.item(item, 'values')
                code_produit = values[0]

                # Supprimer de la base de données
                self.cursor.execute("DELETE FROM produits WHERE code = ?", (code_produit,))
                self.conn.commit()

                # Recharger les données
                self.load_produits_data()

                messagebox.showinfo("Succès", f"Produit {code_produit} supprimé avec succès!")

    def exporter_produits(self):
        """Exporter la liste des produits"""
        try:
            from tkinter import filedialog
            import csv

            # Demander où sauvegarder
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Exporter les produits"
            )

            if filename:
                # Récupérer toutes les données
                self.cursor.execute("""
                    SELECT code, famille, produit, date_achat, fournisseur, n_facture_achat,
                    prix_achat_ht, tva, prix_achat_ttc, quantite, mode_paiement, date_paiement
                    FROM produits ORDER BY code
                """)

                produits = self.cursor.fetchall()

                # Écrire le fichier CSV
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # En-têtes
                    writer.writerow([
                        'Code', 'Famille', 'Produit', 'Date Achat', 'Fournisseur', 'N° Facture',
                        'Prix HT', 'TVA', 'Prix TTC', 'Quantité', 'Mode Paiement', 'Date Paiement'
                    ])

                    # Données
                    for produit in produits:
                        writer.writerow(produit)

                messagebox.showinfo("Succès", f"Produits exportés vers:\n{filename}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    # Fonctions pour les fournisseurs
    def nouveau_fournisseur(self):
        """Ouvrir la fenêtre de nouveau fournisseur"""
        self.open_fournisseur_form()

    def refresh_supplier_dropdowns(self):
        """Refresh all supplier dropdowns with current database data"""
        try:
            # Get current suppliers from database
            self.cursor.execute("SELECT nom FROM fournisseurs ORDER BY nom")
            fournisseurs_db = [row[0] for row in self.cursor.fetchall()]

            if not fournisseurs_db:
                fournisseurs_db = ["Aucun fournisseur disponible - Veuillez en ajouter un"]

            # Update the current fournisseur combo if it exists
            if hasattr(self, 'current_fournisseur_combo') and self.current_fournisseur_combo:
                try:
                    current_value = self.current_fournisseur_combo.get()
                    self.current_fournisseur_combo['values'] = fournisseurs_db

                    # Restore selection if it still exists
                    if current_value in fournisseurs_db:
                        self.current_fournisseur_combo.set(current_value)
                    elif fournisseurs_db and fournisseurs_db[0] != "Aucun fournisseur disponible - Veuillez en ajouter un":
                        self.current_fournisseur_combo.set("")  # Clear selection

                except Exception as e:
                    print(f"Error updating supplier dropdown: {e}")

        except Exception as e:
            print(f"Error refreshing supplier dropdowns: {e}")

    def refresh_client_dropdown(self, combo_widget, client_var):
        """Refresh client dropdown with current database data"""
        try:
            # Get current clients from database
            self.cursor.execute("SELECT code, nom FROM clients ORDER BY code")
            clients_list = [f"{code} - {nom}" for code, nom in self.cursor.fetchall()]

            # Update the combo values
            current_value = combo_widget.get()
            combo_widget['values'] = ["sélectionné CLIENT"] + clients_list

            # If a new client was just added, try to select it
            if current_value not in combo_widget['values']:
                # Get the most recently added client
                if clients_list:
                    latest_client = clients_list[-1]  # Last in the list (highest code)
                    combo_widget.set(latest_client)
                    client_var.set(latest_client)

        except Exception as e:
            print(f"Error refreshing client dropdown: {e}")

    def refresh_supplier_dropdown(self, combo_widget, supplier_var):
        """Refresh supplier dropdown with current database data"""
        try:
            # Get current suppliers from database
            self.cursor.execute("SELECT nom FROM fournisseurs ORDER BY nom")
            suppliers_list = [row[0] for row in self.cursor.fetchall()]

            if not suppliers_list:
                suppliers_list = ["Aucun fournisseur disponible - Veuillez en ajouter un"]

            # Update the combo values
            current_value = combo_widget.get()
            combo_widget['values'] = suppliers_list

            # If a new supplier was just added, try to select it
            if current_value not in combo_widget['values'] and suppliers_list:
                # Get the most recently added supplier (last in alphabetical order)
                latest_supplier = suppliers_list[-1]
                combo_widget.set(latest_supplier)
                supplier_var.set(latest_supplier)

        except Exception as e:
            print(f"Error refreshing supplier dropdown: {e}")

    def migrate_produits_add_unite(self):
        """Add unite column to produits table if it doesn't exist"""
        try:
            # Check if unite column exists
            self.cursor.execute("PRAGMA table_info(produits)")
            columns = [column[1] for column in self.cursor.fetchall()]

            if 'unite' not in columns:
                # Add unite column with default value
                self.cursor.execute("ALTER TABLE produits ADD COLUMN unite TEXT DEFAULT 'unité'")
                self.conn.commit()
                print("✅ Added 'unite' column to produits table")
            else:
                print("✅ Produits table already has 'unite' column")

        except Exception as e:
            print(f"✅ Produits table already up to date (unite column exists)")

    def clean_invalid_supplier_references(self):
        """Clean up invalid supplier references in products table"""
        try:
            # Get all valid supplier names
            self.cursor.execute("SELECT nom FROM fournisseurs")
            valid_suppliers = {row[0] for row in self.cursor.fetchall()}

            # Get all products with suppliers
            self.cursor.execute("SELECT code, fournisseur FROM produits WHERE fournisseur IS NOT NULL AND fournisseur != ''")
            products_with_suppliers = self.cursor.fetchall()

            # Find and fix invalid references
            invalid_count = 0
            for code, supplier in products_with_suppliers:
                if supplier not in valid_suppliers:
                    # Set to empty string for invalid suppliers
                    self.cursor.execute("UPDATE produits SET fournisseur = '' WHERE code = ?", (code,))
                    invalid_count += 1
                    print(f"Cleaned invalid supplier reference: {supplier} for product {code}")

            if invalid_count > 0:
                self.conn.commit()
                print(f"Cleaned {invalid_count} invalid supplier references")

        except Exception as e:
            print(f"Error cleaning invalid supplier references: {e}")

    def load_fournisseurs_data(self):
        """Chargement des données fournisseurs"""
        # Vider le treeview
        for item in self.fournisseurs_tree.get_children():
            self.fournisseurs_tree.delete(item)

        # Charger les données
        self.cursor.execute("""
            SELECT code, nom, ice, contact, adresse_electronique, solde
            FROM fournisseurs
            ORDER BY code
        """)

        for row in self.cursor.fetchall():
            code, nom, ice, contact, email, solde = row
            solde_str = f"{solde:,.2f} DH".replace(',', ' ')
            ice_display = ice if ice else ""

            # Couleur selon le solde
            tags = ('positive',) if solde > 0 else ('negative',) if solde < 0 else ()

            self.fournisseurs_tree.insert('', tk.END, values=(code, nom, ice_display, contact, email, solde_str), tags=tags)

        # Configuration des couleurs
        self.fournisseurs_tree.tag_configure('positive', foreground='#27ae60')
        self.fournisseurs_tree.tag_configure('negative', foreground='#e74c3c')

        # Mettre à jour les statistiques
        self.update_fournisseurs_stats()

    def update_fournisseurs_stats(self):
        """Mise à jour des statistiques des fournisseurs"""
        # Compter le nombre total de fournisseurs
        self.cursor.execute("SELECT COUNT(*) FROM fournisseurs")
        total_fournisseurs = self.cursor.fetchone()[0]

        # Calculer le solde total
        self.cursor.execute("SELECT SUM(solde) FROM fournisseurs")
        total_solde = self.cursor.fetchone()[0] or 0

        # Mettre à jour le label des statistiques
        stats_text = f"Total: {total_fournisseurs} fournisseurs | Solde total: {total_solde:,.2f} DH".replace(',', ' ')
        self.fournisseurs_stats_label.config(text=stats_text)

    def filter_fournisseurs(self, event=None):
        """Filtrage des fournisseurs"""
        search_term = self.search_fournisseurs_var.get().lower()

        # Vider et recharger avec filtre
        for item in self.fournisseurs_tree.get_children():
            self.fournisseurs_tree.delete(item)

        # Requête avec filtre
        self.cursor.execute("""
            SELECT code, nom, ice, contact, adresse_electronique, solde
            FROM fournisseurs
            WHERE LOWER(code) LIKE ? OR LOWER(nom) LIKE ? OR LOWER(adresse_electronique) LIKE ? OR LOWER(contact) LIKE ?
            ORDER BY code
        """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        for row in self.cursor.fetchall():
            code, nom, ice, contact, email, solde = row
            solde_str = f"{solde:,.2f} DH".replace(',', ' ')
            ice_display = ice if ice else ""

            tags = ('positive',) if solde > 0 else ('negative',) if solde < 0 else ()
            self.fournisseurs_tree.insert('', tk.END, values=(code, nom, ice_display, contact, email, solde_str), tags=tags)

        self.fournisseurs_tree.tag_configure('positive', foreground='#27ae60')
        self.fournisseurs_tree.tag_configure('negative', foreground='#e74c3c')

    def on_fournisseur_selection(self, event):
        """Gestion de la sélection d'un fournisseur"""
        selection = self.fournisseurs_tree.selection()
        if selection:
            item = selection[0]
            values = self.fournisseurs_tree.item(item, 'values')
            code_fournisseur = values[0]
            nom_fournisseur = values[1]
            self.fournisseurs_selection_label.config(text=f"Sélectionné: {code_fournisseur} - {nom_fournisseur}")
        else:
            self.fournisseurs_selection_label.config(text="")

    def show_fournisseur_context_menu(self, event):
        """Affichage du menu contextuel pour les fournisseurs"""
        item = self.fournisseurs_tree.selection()[0] if self.fournisseurs_tree.selection() else None
        if item:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="✏️ Modifier", command=self.edit_fournisseur)
            menu.add_command(label="👁️ Voir détails", command=self.view_fournisseur)
            menu.add_separator()
            menu.add_command(label="🗑️ Supprimer", command=self.delete_fournisseur)
            menu.tk_popup(event.x_root, event.y_root)

    def edit_fournisseur(self, event=None):
        """Modifier un fournisseur"""
        selection = self.fournisseurs_tree.selection()
        if selection:
            # Récupérer les données du fournisseur sélectionné
            item = selection[0]
            values = self.fournisseurs_tree.item(item, 'values')
            code_fournisseur = values[0]

            # Récupérer toutes les données du fournisseur
            self.cursor.execute("""
                SELECT code, nom, ice, if_field, adresse, personne_contact,
                contact, n_fix, n_fax, adresse_electronique
                FROM fournisseurs WHERE code = ?
            """, (code_fournisseur,))

            fournisseur_data = self.cursor.fetchone()
            if fournisseur_data:
                self.open_fournisseur_form(fournisseur_data)

    def view_fournisseur(self):
        """Voir les détails d'un fournisseur"""
        selection = self.fournisseurs_tree.selection()
        if selection:
            # Récupérer les données du fournisseur sélectionné
            item = selection[0]
            values = self.fournisseurs_tree.item(item, 'values')
            code_fournisseur = values[0]

            # Récupérer toutes les données du fournisseur
            self.cursor.execute("""
                SELECT code, nom, ice, if_field, adresse, personne_contact,
                contact, n_fix, n_fax, adresse_electronique, solde, date_creation
                FROM fournisseurs WHERE code = ?
            """, (code_fournisseur,))

            fournisseur_data = self.cursor.fetchone()
            if fournisseur_data:
                # Créer une fenêtre de détails
                details_window = tk.Toplevel(self.root)
                details_window.title(f"Détails Fournisseur - {fournisseur_data[0]}")

                # Taille adaptative
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                window_width = min(600, int(screen_width * 0.4))
                window_height = min(700, int(screen_height * 0.7))

                x = (screen_width - window_width) // 2
                y = (screen_height - window_height) // 2
                details_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

                details_window.resizable(True, True)
                details_window.minsize(400, 500)
                details_window.transient(self.root)
                details_window.grab_set()

                # Frame principal
                main_frame = ttk.Frame(details_window, padding="20")
                main_frame.pack(fill=tk.BOTH, expand=True)

                # Titre
                title_label = ttk.Label(main_frame, text=f"Informations Fournisseur - {fournisseur_data[0]}", style='Title.TLabel')
                title_label.pack(pady=(0, 20))

                # Informations
                info_frame = ttk.LabelFrame(main_frame, text="Détails", padding="15")
                info_frame.pack(fill=tk.BOTH, expand=True)

                details = [
                    ("Code:", fournisseur_data[0]),
                    ("Nom/Entreprise:", fournisseur_data[1]),
                    ("ICE:", fournisseur_data[3] or "N/A"),
                    ("IF:", fournisseur_data[4] or "N/A"),
                    ("Adresse:", fournisseur_data[5] or "N/A"),
                    ("Personne à contacter:", fournisseur_data[6] or "N/A"),
                    ("Contact:", fournisseur_data[7] or "N/A"),
                    ("N° Fix:", fournisseur_data[8] or "N/A"),
                    ("N° Fax:", fournisseur_data[9] or "N/A"),
                    ("Email:", fournisseur_data[10] or "N/A"),
                    ("Solde:", f"{fournisseur_data[11]:,.2f} DH".replace(',', ' ')),
                    ("Date création:", fournisseur_data[12])
                ]

                for i, (label, value) in enumerate(details):
                    ttk.Label(info_frame, text=label, style='Heading.TLabel').grid(row=i, column=0, sticky=tk.W, pady=2)
                    ttk.Label(info_frame, text=str(value)).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

                # Bouton fermer
                ttk.Button(main_frame, text="Fermer", command=details_window.destroy).pack(pady=20)

    def delete_fournisseur(self):
        """Supprimer un fournisseur"""
        selection = self.fournisseurs_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un fournisseur à supprimer.")
            return

        item = selection[0]
        values = self.fournisseurs_tree.item(item, 'values')
        code_fournisseur = values[0]
        nom_fournisseur = values[1]

        # Vérifier les dépendances
        try:
            # Vérifier s'il y a des produits liés
            self.cursor.execute("SELECT COUNT(*) FROM produits WHERE fournisseur = ?", (nom_fournisseur,))
            produits_count = self.cursor.fetchone()[0]

            if produits_count > 0:
                message = f"Impossible de supprimer le fournisseur {code_fournisseur} ({nom_fournisseur}).\n\n"
                message += f"Ce fournisseur a {produits_count} produit(s) associé(s).\n"
                message += "Modifiez d'abord les produits pour changer leur fournisseur."
                messagebox.showerror("Suppression impossible", message)
                return

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la vérification des dépendances: {str(e)}")
            return

        # Confirmation avec détails
        if messagebox.askyesno("Confirmation",
                              f"Êtes-vous sûr de vouloir supprimer le fournisseur ?\n\n"
                              f"Code: {code_fournisseur}\n"
                              f"Nom: {nom_fournisseur}\n\n"
                              f"Cette action est irréversible."):
            try:
                # Supprimer de la base de données
                self.cursor.execute("DELETE FROM fournisseurs WHERE code = ?", (code_fournisseur,))
                self.conn.commit()

                # Réorganiser les codes
                self.reorganize_fournisseur_codes()

                # Recharger les données
                self.load_fournisseurs_data()
                self.update_fournisseurs_stats()

                messagebox.showinfo("Succès", f"Fournisseur {code_fournisseur} supprimé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")
                self.conn.rollback()

    def reorganize_fournisseur_codes(self):
        """Réorganiser les codes fournisseurs après suppression"""
        # Récupérer tous les fournisseurs triés par ID
        self.cursor.execute("SELECT id FROM fournisseurs ORDER BY id")
        fournisseurs = self.cursor.fetchall()

        # Mettre à jour les codes
        for i, (fournisseur_id,) in enumerate(fournisseurs, 1):
            new_code = f"F{i}"
            self.cursor.execute("UPDATE fournisseurs SET code = ? WHERE id = ?", (new_code, fournisseur_id))

        self.conn.commit()

    def get_next_fournisseur_code(self):
        """Générer le prochain code fournisseur"""
        try:
            # Récupérer le dernier numéro utilisé
            self.cursor.execute("SELECT code FROM fournisseurs WHERE code LIKE 'F%' ORDER BY CAST(SUBSTR(code, 2) AS INTEGER) DESC LIMIT 1")
            result = self.cursor.fetchone()
            if result:
                last_code = result[0]
                # Extraire le numéro et l'incrémenter
                last_num = int(last_code[1:])
                return f"F{last_num + 1}"
            else:
                return "F1"
        except:
            # En cas d'erreur, compter simplement
            self.cursor.execute("SELECT COUNT(*) FROM fournisseurs")
            count = self.cursor.fetchone()[0]
            return f"F{count + 1}"

    def open_fournisseur_form(self, fournisseur_data=None):
        """Ouvrir le formulaire d'ajout/modification de fournisseur"""
        # Créer la fenêtre
        form_window = tk.Toplevel(self.root)
        form_window.title("Nouveau Fournisseur" if not fournisseur_data else "Modifier Fournisseur")

        # Taille adaptative basée sur l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(700, int(screen_width * 0.6))
        window_height = min(800, int(screen_height * 0.8))

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        form_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Permettre le redimensionnement
        form_window.resizable(True, True)
        form_window.minsize(500, 600)

        form_window.transient(self.root)
        form_window.grab_set()

        # Frame principal avec scrollbar
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configuration pour le redimensionnement automatique
        form_window.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Variables pour les champs
        code_var = tk.StringVar(value=fournisseur_data[0] if fournisseur_data else self.get_next_fournisseur_code())
        nom_var = tk.StringVar(value=fournisseur_data[1] if fournisseur_data else "")
        ice_var = tk.StringVar(value=fournisseur_data[2] if fournisseur_data else "")
        if_var = tk.StringVar(value=fournisseur_data[3] if fournisseur_data else "")
        adresse_var = tk.StringVar(value=fournisseur_data[4] if fournisseur_data else "")
        personne_contact_var = tk.StringVar(value=fournisseur_data[5] if fournisseur_data else "")
        contact_var = tk.StringVar(value=fournisseur_data[6] if fournisseur_data else "")
        n_fix_var = tk.StringVar(value=fournisseur_data[7] if fournisseur_data else "")
        n_fax_var = tk.StringVar(value=fournisseur_data[8] if fournisseur_data else "")
        email_var = tk.StringVar(value=fournisseur_data[9] if fournisseur_data else "")

        # Titre
        title_label = ttk.Label(main_frame, text="Informations Fournisseur", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        row = 1

        # Code (lecture seule)
        ttk.Label(main_frame, text="Code:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        code_entry = ttk.Entry(main_frame, textvariable=code_var, state='readonly', width=40)
        code_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Nom
        ttk.Label(main_frame, text="Nom / Entreprise:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(main_frame, textvariable=nom_var, width=40)
        nom_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1



        # ICE
        ttk.Label(main_frame, text="ICE:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        ice_entry = ttk.Entry(main_frame, textvariable=ice_var, width=40)
        ice_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # IF
        ttk.Label(main_frame, text="IF:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        if_entry = ttk.Entry(main_frame, textvariable=if_var, width=40)
        if_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Adresse
        ttk.Label(main_frame, text="Adresse:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        adresse_entry = ttk.Entry(main_frame, textvariable=adresse_var, width=40)
        adresse_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Personne à contacter
        ttk.Label(main_frame, text="Personne à contacter:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        personne_entry = ttk.Entry(main_frame, textvariable=personne_contact_var, width=40)
        personne_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Contact
        ttk.Label(main_frame, text="Contact:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        contact_entry = ttk.Entry(main_frame, textvariable=contact_var, width=40)
        contact_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # N Fix
        ttk.Label(main_frame, text="N° Fix:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        n_fix_entry = ttk.Entry(main_frame, textvariable=n_fix_var, width=40)
        n_fix_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # N Fax
        ttk.Label(main_frame, text="N° Fax:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        n_fax_entry = ttk.Entry(main_frame, textvariable=n_fax_var, width=40)
        n_fax_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Adresse électronique
        ttk.Label(main_frame, text="Adresse électronique:", style='Heading.TLabel').grid(row=row, column=0, sticky=tk.W, pady=5)
        email_entry = ttk.Entry(main_frame, textvariable=email_var, width=40)
        email_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Configuration du redimensionnement
        main_frame.columnconfigure(1, weight=1)



        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row+1, column=0, columnspan=2, pady=20)

        def save_fournisseur():
            # Validation
            if not nom_var.get().strip():
                messagebox.showerror("Erreur", "Le nom est obligatoire!")
                return



            # Sauvegarder
            try:
                if fournisseur_data:  # Modification
                    self.cursor.execute("""
                        UPDATE fournisseurs SET nom=?, ice=?, if_field=?, adresse=?,
                        personne_contact=?, contact=?, n_fix=?, n_fax=?, adresse_electronique=?
                        WHERE code=?
                    """, (nom_var.get(), ice_var.get(), if_var.get(),
                          adresse_var.get(), personne_contact_var.get(), contact_var.get(),
                          n_fix_var.get(), n_fax_var.get(), email_var.get(), code_var.get()))
                else:  # Nouveau
                    self.cursor.execute("""
                        INSERT INTO fournisseurs (code, nom, ice, if_field, adresse,
                        personne_contact, contact, n_fix, n_fax, adresse_electronique, solde)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0.0)
                    """, (code_var.get(), nom_var.get(), ice_var.get(),
                          if_var.get(), adresse_var.get(), personne_contact_var.get(),
                          contact_var.get(), n_fix_var.get(), n_fax_var.get(), email_var.get()))

                self.conn.commit()
                self.load_fournisseurs_data()

                # Refresh supplier dropdowns in other forms
                self.refresh_supplier_dropdowns()

                form_window.destroy()
                messagebox.showinfo("Succès", "Fournisseur sauvegardé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        def cancel():
            form_window.destroy()

        ttk.Button(buttons_frame, text="💾 Sauvegarder", command=save_fournisseur,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ Annuler", command=cancel).pack(side=tk.LEFT)

        # Focus sur le nom
        nom_entry.focus()

    def exporter_fournisseurs(self):
        """Exporter la liste des fournisseurs"""
        try:
            from tkinter import filedialog
            import csv

            # Demander où sauvegarder
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Exporter les fournisseurs"
            )

            if filename:
                # Récupérer toutes les données
                self.cursor.execute("""
                    SELECT code, nom, categorie, ice, if_field, adresse,
                    personne_contact, contact, n_fix, n_fax, adresse_electronique, solde
                    FROM fournisseurs ORDER BY code
                """)

                fournisseurs = self.cursor.fetchall()

                # Écrire le fichier CSV
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # En-têtes
                    writer.writerow([
                        'Code', 'Nom/Entreprise', 'Catégorie', 'ICE', 'IF', 'Adresse',
                        'Personne Contact', 'Contact', 'N° Fix', 'N° Fax', 'Email', 'Solde'
                    ])

                    # Données
                    for fournisseur in fournisseurs:
                        writer.writerow(fournisseur)

                messagebox.showinfo("Succès", f"Fournisseurs exportés vers:\n{filename}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    # Fonctions pour les devis
    def create_devis_interface(self, parent):
        """Création de l'interface devis principale"""
        # Configuration du redimensionnement
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)

        # En-tête avec boutons d'action
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        ttk.Label(header_frame, text="📄 Devis", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)

        # Boutons d'action
        actions_frame = ttk.Frame(header_frame)
        actions_frame.grid(row=0, column=2, sticky=tk.E)

        ttk.Button(actions_frame, text="➕ Nouveau Devis", command=self.nouveau_devis,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🔄 Actualiser", command=self.load_devis_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🖨️ Imprimer", command=self.imprimer_devis).pack(side=tk.LEFT)

        # Barre de recherche et statistiques
        search_stats_frame = ttk.Frame(parent)
        search_stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_stats_frame.columnconfigure(1, weight=1)

        # Barre de recherche
        search_frame = ttk.Frame(search_stats_frame)
        search_frame.grid(row=0, column=0, sticky=tk.W)

        ttk.Label(search_frame, text="🔍 Rechercher:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_devis_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_devis_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_devis)

        # Statistiques rapides
        stats_frame = ttk.Frame(search_stats_frame)
        stats_frame.grid(row=0, column=2, sticky=tk.E)

        self.devis_stats_label = ttk.Label(stats_frame, text="Total: 0 devis", style='Info.TLabel')
        self.devis_stats_label.pack()

        # Tableau des devis
        self.create_devis_table(parent)

    def create_devis_table(self, parent):
        """Création du tableau des devis"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Treeview
        columns = ('n_devis', 'type_client', 'client', 'nature_prestation', 'total_ttc', 'date_creation')
        self.devis_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # En-têtes
        self.devis_tree.heading('n_devis', text='N° Devis')
        self.devis_tree.heading('type_client', text='Type Client')
        self.devis_tree.heading('client', text='Client')
        self.devis_tree.heading('nature_prestation', text='Nature Prestation')
        self.devis_tree.heading('total_ttc', text='Total TTC')
        self.devis_tree.heading('date_creation', text='Date Création')

        # Largeurs des colonnes
        self.devis_tree.column('n_devis', width=100)
        self.devis_tree.column('type_client', width=120)
        self.devis_tree.column('client', width=200)
        self.devis_tree.column('nature_prestation', width=150)
        self.devis_tree.column('total_ttc', width=100)
        self.devis_tree.column('date_creation', width=120)

        self.devis_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.devis_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.devis_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.devis_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.devis_tree.configure(xscrollcommand=h_scrollbar.set)

        # Menu contextuel et événements
        self.devis_tree.bind('<Button-3>', self.show_devis_context_menu)
        self.devis_tree.bind('<Double-1>', self.edit_devis)

        # Barre de statut
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.devis_status_label = ttk.Label(status_frame, text="Prêt", style='Info.TLabel')
        self.devis_status_label.pack(side=tk.LEFT)

        # Informations sur la sélection
        self.devis_selection_label = ttk.Label(status_frame, text="", style='Info.TLabel')
        self.devis_selection_label.pack(side=tk.RIGHT)

        # Lier l'événement de sélection
        self.devis_tree.bind('<<TreeviewSelect>>', self.on_devis_selection)

    def nouveau_devis(self):
        """Ouvrir la fenêtre de nouveau devis"""
        self.open_devis_form()

    def load_devis_data(self):
        """Chargement des données devis"""
        # Vider le treeview
        for item in self.devis_tree.get_children():
            self.devis_tree.delete(item)

        # Charger les données
        self.cursor.execute("""
            SELECT n_devis, type_client, client, nature_prestation, total_ttc, date_creation
            FROM devis
            ORDER BY n_devis
        """)

        for row in self.cursor.fetchall():
            n_devis, type_client, client, nature_prestation, total_ttc, date_creation = row
            total_ttc_display = f"{total_ttc:.2f} DH" if total_ttc else "0.00 DH"

            self.devis_tree.insert('', tk.END, values=(n_devis, type_client, client, nature_prestation, total_ttc_display, date_creation))

        # Mettre à jour les statistiques
        self.update_devis_stats()

    def update_devis_stats(self):
        """Mise à jour des statistiques des devis"""
        # Compter le nombre total de devis
        self.cursor.execute("SELECT COUNT(*) FROM devis")
        total_devis = self.cursor.fetchone()[0]

        # Calculer le montant total
        self.cursor.execute("SELECT SUM(total_ttc) FROM devis")
        total_montant = self.cursor.fetchone()[0] or 0

        # Mettre à jour le label des statistiques
        stats_text = f"Total: {total_devis} devis - Montant: {total_montant:.2f} DH"
        self.devis_stats_label.config(text=stats_text)

    def filter_devis(self, event=None):
        """Filtrage des devis"""
        search_term = self.search_devis_var.get().lower()

        # Vider et recharger avec filtre
        for item in self.devis_tree.get_children():
            self.devis_tree.delete(item)

        # Requête avec filtre
        self.cursor.execute("""
            SELECT n_devis, type_client, client, nature_prestation, total_ttc, date_creation
            FROM devis
            WHERE LOWER(n_devis) LIKE ? OR LOWER(client) LIKE ? OR LOWER(nature_prestation) LIKE ?
            ORDER BY n_devis
        """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        for row in self.cursor.fetchall():
            n_devis, type_client, client, nature_prestation, total_ttc, date_creation = row
            total_ttc_display = f"{total_ttc:.2f} DH" if total_ttc else "0.00 DH"

            self.devis_tree.insert('', tk.END, values=(n_devis, type_client, client, nature_prestation, total_ttc_display, date_creation))

    def on_devis_selection(self, event):
        """Gestion de la sélection d'un devis"""
        selection = self.devis_tree.selection()
        if selection:
            item = selection[0]
            values = self.devis_tree.item(item, 'values')
            n_devis = values[0]
            client = values[2]
            self.devis_selection_label.config(text=f"Sélectionné: {n_devis} - {client}")
        else:
            self.devis_selection_label.config(text="")

    def show_devis_context_menu(self, event):
        """Affichage du menu contextuel pour les devis"""
        item = self.devis_tree.selection()[0] if self.devis_tree.selection() else None
        if item:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="✏️ Modifier", command=self.edit_devis)
            menu.add_command(label="👁️ Voir détails", command=self.view_devis)
            menu.add_command(label="🖨️ Imprimer", command=self.imprimer_devis)
            menu.add_separator()
            menu.add_command(label="🗑️ Supprimer", command=self.delete_devis)
            menu.tk_popup(event.x_root, event.y_root)

    def edit_devis(self, event=None):
        """Modifier un devis"""
        selection = self.devis_tree.selection()
        if selection:
            item = selection[0]
            values = self.devis_tree.item(item, 'values')
            n_devis = values[0]

            # Récupérer les données complètes du devis
            self.cursor.execute("""
                SELECT n_devis, type_client, nature_prestation, client, adresse, ice, objet
                FROM devis WHERE n_devis = ?
            """, (n_devis,))
            devis_data = self.cursor.fetchone()

            if devis_data:
                self.open_devis_form(devis_data)

    def view_devis(self):
        """Voir les détails d'un devis"""
        messagebox.showinfo("Info", "Affichage détails en cours de développement")

    def delete_devis(self):
        """Supprimer un devis"""
        selection = self.devis_tree.selection()
        if selection:
            item = selection[0]
            values = self.devis_tree.item(item, 'values')
            n_devis = values[0]

            if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le devis {n_devis}?"):
                try:
                    # Supprimer les détails d'abord
                    self.cursor.execute("DELETE FROM details_devis WHERE devis_id = (SELECT id FROM devis WHERE n_devis = ?)", (n_devis,))
                    # Supprimer le devis
                    self.cursor.execute("DELETE FROM devis WHERE n_devis = ?", (n_devis,))
                    self.conn.commit()
                    self.load_devis_data()
                    messagebox.showinfo("Succès", "Devis supprimé avec succès!")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

    def imprimer_devis(self):
        """Imprimer un devis"""
        messagebox.showinfo("Info", "Impression en cours de développement")

    def open_devis_form(self, devis_data=None):
        """Ouvrir le formulaire de devis"""
        # Créer la fenêtre
        form_window = tk.Toplevel(self.root)
        form_window.title("Nouveau Devis" if not devis_data else "Modifier Devis")

        # Taille adaptative basée sur l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(1000, int(screen_width * 0.9))
        window_height = min(800, int(screen_height * 0.9))

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        form_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Permettre le redimensionnement
        form_window.resizable(True, True)
        form_window.minsize(800, 600)

        form_window.transient(self.root)
        form_window.grab_set()

        # Frame principal avec scrollbar
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configuration pour le redimensionnement automatique
        form_window.columnconfigure(0, weight=1)
        form_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Variables pour les champs
        n_devis_var = tk.StringVar(value=devis_data[0] if devis_data else self.get_next_devis_number())
        nature_prestation_var = tk.StringVar(value=devis_data[1] if devis_data else "Travaux")
        client_var = tk.StringVar(value=devis_data[2] if devis_data else "sélectionné CLIENT")
        adresse_var = tk.StringVar(value=devis_data[3] if devis_data else "")
        ice_var = tk.StringVar(value=devis_data[4] if devis_data else "")
        objet_var = tk.StringVar(value=devis_data[5] if devis_data else "")

        # Titre
        title_label = ttk.Label(main_frame, text="DEVIS", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        row = 1

        # Section en-tête du devis
        header_frame = ttk.LabelFrame(main_frame, text="Informations Générales", padding="10")
        header_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)
        header_frame.columnconfigure(3, weight=1)

        # N° DEVIS
        ttk.Label(header_frame, text="N°:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)
        n_devis_entry = ttk.Entry(header_frame, textvariable=n_devis_var, state='readonly', width=20)
        n_devis_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))



        # Nature de prestation
        ttk.Label(header_frame, text="Nature de prestation:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)

        # Nature selection with add button
        nature_selection_frame = ttk.Frame(header_frame)
        nature_selection_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))
        nature_selection_frame.columnconfigure(0, weight=1)

        nature_combo = ttk.Combobox(nature_selection_frame, textvariable=nature_prestation_var,
                                   values=["Travaux", "fourniture"],
                                   width=20)
        nature_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add nature button
        def add_nature_and_refresh():
            self.add_new_nature_option(nature_combo, nature_prestation_var)

        add_nature_btn = ttk.Button(nature_selection_frame, text="➕", width=3,
                                   command=add_nature_and_refresh)
        add_nature_btn.grid(row=0, column=1)

        # Section client
        client_frame = ttk.LabelFrame(main_frame, text="Informations Client", padding="10")
        client_frame.grid(row=row+1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        client_frame.columnconfigure(1, weight=1)

        # CLIENT
        ttk.Label(client_frame, text="CLIENT:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)

        # Récupérer la liste des clients
        self.cursor.execute("SELECT code, nom FROM clients ORDER BY code")
        clients_list = [f"{code} - {nom}" for code, nom in self.cursor.fetchall()]

        # Client selection with add button
        client_selection_frame = ttk.Frame(client_frame)
        client_selection_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        client_selection_frame.columnconfigure(0, weight=1)

        client_combo = ttk.Combobox(client_selection_frame, textvariable=client_var,
                                   values=["sélectionné CLIENT"] + clients_list, width=35)
        client_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add client button
        def add_client_and_refresh():
            self.open_client_form()
            # Refresh client list after potential addition
            self.root.after(100, lambda: self.refresh_client_dropdown(client_combo, client_var))

        add_client_btn = ttk.Button(client_selection_frame, text="➕", width=3,
                                   command=add_client_and_refresh)
        add_client_btn.grid(row=0, column=1)

        # Adresse
        ttk.Label(client_frame, text="Adresse:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)
        adresse_entry = ttk.Entry(client_frame, textvariable=adresse_var, width=40)
        adresse_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # ICE
        ttk.Label(client_frame, text="ICE:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W, pady=5)
        ice_entry = ttk.Entry(client_frame, textvariable=ice_var, width=40)
        ice_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Objet
        ttk.Label(client_frame, text="Objet:", style='Heading.TLabel').grid(row=3, column=0, sticky=tk.W, pady=5)
        objet_entry = ttk.Entry(client_frame, textvariable=objet_var, width=40)
        objet_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Section détails - NEW IMPLEMENTATION
        self.create_new_details_section(main_frame, row+2)

        # Load existing details if editing
        if devis_data:
            # Use root.after to ensure the Details table is fully created before loading data
            form_window.after(100, lambda: self.load_devis_details(devis_data[0]))





        # Note finale
        note_frame = ttk.Frame(main_frame)
        note_frame.grid(row=row+3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        note_label = ttk.Label(note_frame, text="Arrête le présent devis à la somme de : EX ( CINQ MILLIONS CENT UN MILLE HTC )",
                              style='Info.TLabel', wraplength=600)
        note_label.pack()

        # Fonction pour mettre à jour les informations client automatiquement
        def on_client_change(*args):
            client_selection = client_var.get()
            if client_selection != "sélectionné CLIENT" and " - " in client_selection:
                client_code = client_selection.split(" - ")[0]
                # Récupérer les informations du client
                self.cursor.execute("""
                    SELECT ice, adresse, categorie FROM clients WHERE code = ?
                """, (client_code,))
                client_info = self.cursor.fetchone()
                if client_info:
                    ice_var.set(client_info[0] or "")
                    adresse_var.set(client_info[1] or "")

        # Lier l'événement de changement de client
        client_var.trace('w', on_client_change)

        # Boutons principaux
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row+4, column=0, columnspan=3, pady=20)

        def save_devis():
            # Validation
            if not client_var.get().strip() or client_var.get() == "sélectionné CLIENT":
                messagebox.showerror("Erreur", "Veuillez sélectionner un client!")
                return

            # Sauvegarder
            try:
                devis_id = None

                if devis_data:  # Modification
                    # Update main devis record
                    self.cursor.execute("""
                        UPDATE devis SET nature_prestation=?, client=?,
                        adresse=?, ice=?, objet=? WHERE n_devis=?
                    """, (nature_prestation_var.get(), client_var.get(),
                          adresse_var.get(), ice_var.get(), objet_var.get(), n_devis_var.get()))

                    # Get devis ID
                    self.cursor.execute("SELECT id FROM devis WHERE n_devis=?", (n_devis_var.get(),))
                    devis_id = self.cursor.fetchone()[0]

                    # Delete existing details
                    self.cursor.execute("DELETE FROM details_devis WHERE devis_id=?", (devis_id,))

                else:  # Nouveau
                    # Insert new devis record
                    self.cursor.execute("""
                        INSERT INTO devis (n_devis, nature_prestation, client,
                        adresse, ice, objet) VALUES (?, ?, ?, ?, ?, ?)
                    """, (n_devis_var.get(), nature_prestation_var.get(),
                          client_var.get(), adresse_var.get(), ice_var.get(), objet_var.get()))

                    devis_id = self.cursor.lastrowid

                # Save details from the Details table
                self.save_devis_details(devis_id)

                # Calculate and update totals
                self.update_devis_totals(devis_id)

                self.conn.commit()
                self.load_devis_data()
                form_window.destroy()
                messagebox.showinfo("Succès", "Devis sauvegardé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        def cancel():
            form_window.destroy()

        ttk.Button(buttons_frame, text="✓ Valider", command=save_devis,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="🗑️ Supprimer", command=cancel).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="🖨️ Imprimer", command=cancel).pack(side=tk.LEFT)

        # Focus sur la nature de prestation
        nature_combo.focus()

    def ajouter_ligne_devis(self):
        """Ajouter une ligne de détail au devis"""
        # Calculer le numéro de ligne
        ligne_count = len(self.devis_details_tree.get_children())
        n_ligne = ligne_count + 1

        # Ajouter une ligne vide que l'utilisateur peut éditer
        self.devis_details_tree.insert('', tk.END, values=(
            n_ligne,
            'Cliquez pour sélectionner un produit',
            'unité',
            '1',
            '0.00',
            '1.20',
            '0.00',
            '0.00'
        ))

        messagebox.showinfo("Info", "Ligne ajoutée! Double-cliquez sur 'sélectionné produit' pour choisir un produit.")



    def modifier_ligne_devis(self):
        """Modifier une ligne de détail du devis"""
        selection = self.devis_details_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner une ligne à modifier!")
            return

        messagebox.showinfo("Info", "Double-cliquez sur les cellules pour les modifier directement!")

    def setup_devis_cell_editing(self):
        """Configurer l'édition directe des cellules du devis"""
        print("🔧 Setting up DEVIS cell editing...")

        # Clear any existing bindings first
        self.devis_details_tree.unbind('<Double-1>')
        self.devis_details_tree.unbind('<Double-Button-1>')

        # Bind double-click pour éditer les cellules - PRIORITÉ ABSOLUE
        self.devis_details_tree.bind('<Double-1>', self.on_devis_cell_double_click)
        print("✅ DEVIS Double-click binding set up")

        # Bind keyboard navigation
        self.devis_details_tree.bind('<Tab>', self.on_devis_tab_navigation)
        self.devis_details_tree.bind('<Return>', self.on_devis_enter_key)
        self.devis_details_tree.bind('<F2>', self.on_devis_f2_edit)

        # Variables pour l'édition
        self.editing_item = None
        self.editing_column = None

        # Variables for new details system
        self.details_data = []  # Store details data
        self.edit_widget = None



    def create_new_details_section(self, parent, row):
        """Create a new, clean Details section with working dropdown functionality"""
        # Create the details frame
        details_frame = ttk.LabelFrame(parent, text="Détails", padding="10")
        details_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(1, weight=1)

        # Create the table frame
        table_frame = ttk.Frame(details_frame)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Create the Treeview
        columns = ('n', 'designation', 'u', 'qte', 'prix_achat_ht', 'marge', 'prix_ht', 'prix_total_ht')
        self.devis_details_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=8)

        # Configure headers
        self.devis_details_tree.heading('n', text='N°')
        self.devis_details_tree.heading('designation', text='Désignation')
        self.devis_details_tree.heading('u', text='U')
        self.devis_details_tree.heading('qte', text='Qté')
        self.devis_details_tree.heading('prix_achat_ht', text='Prix achat HT')
        self.devis_details_tree.heading('marge', text='Marge')
        self.devis_details_tree.heading('prix_ht', text='Prix HT')
        self.devis_details_tree.heading('prix_total_ht', text='Prix total HT')

        # Configure column widths
        self.devis_details_tree.column('n', width=40)
        self.devis_details_tree.column('designation', width=200)
        self.devis_details_tree.column('u', width=60)
        self.devis_details_tree.column('qte', width=60)
        self.devis_details_tree.column('prix_achat_ht', width=100)
        self.devis_details_tree.column('marge', width=80)
        self.devis_details_tree.column('prix_ht', width=100)
        self.devis_details_tree.column('prix_total_ht', width=120)

        # Add to grid
        self.devis_details_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.devis_details_tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.devis_details_tree.configure(yscrollcommand=scrollbar.set)

        # Add management buttons
        buttons_frame = ttk.Frame(details_frame)
        buttons_frame.grid(row=2, column=0, pady=(10, 0))

        ttk.Button(buttons_frame, text="➕ Ajouter Ligne",
                  command=self.add_detail_row).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="✏️ Modifier Ligne",
                  command=self.edit_detail_row).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ Supprimer Ligne",
                  command=self.delete_detail_row).pack(side=tk.LEFT)

        # Add sample rows
        self.add_sample_rows()

        # Setup event bindings for product selection
        self.setup_product_selection()

        # Setup cell editing AFTER creating the tree
        self.setup_devis_cell_editing()
        print("✅ DEVIS details section created with cell editing enabled")

        # Store reference to details frame for loading data
        self.current_details_frame = details_frame

        return details_frame

    def add_sample_rows(self):
        """Add sample rows to the details table"""
        self.devis_details_tree.insert('', tk.END, values=('1', 'Cliquez pour sélectionner', 'unité', '1', '0', '1.2', '0.00', '0.00'))
        self.devis_details_tree.insert('', tk.END, values=('2', 'Cliquez pour sélectionner', 'unité', '1', '0', '1.2', '0.00', '0.00'))

    def setup_product_selection(self):
        """Setup product selection functionality"""
        # Note: Double-click is now handled by the universal cell editing system
        pass

    def on_designation_double_click(self, event):
        """Handle double-click on designation column - now handled by universal system"""
        # This is now handled by the universal cell editing system
        pass

    def open_product_selection_dialog(self, item):
        """Open product selection dialog"""
        # Create dialog window
        dialog = tk.Toplevel(self.root)
        dialog.title("Sélectionner un produit")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"500x400+{x}+{y}")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Sélectionner un produit",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 10))

        # Search frame
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="Recherche:").pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)

        # Products list frame
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Create treeview for products
        columns = ('product', 'price')
        products_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        products_tree.heading('product', text='Produit')
        products_tree.heading('price', text='Prix (DH)')
        products_tree.column('product', width=300)
        products_tree.column('price', width=100)

        products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=products_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        products_tree.configure(yscrollcommand=scrollbar.set)

        # Load products
        self.load_products_in_dialog(products_tree, "")

        # Search functionality
        def filter_products(*args):
            search_term = search_var.get().lower()
            self.load_products_in_dialog(products_tree, search_term)

        search_var.trace('w', filter_products)

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def select_product():
            selection = products_tree.selection()
            if selection:
                item_data = products_tree.item(selection[0], 'values')
                product_name = item_data[0]
                product_price = item_data[1]
                self.apply_selected_product(item, product_name, product_price)
                dialog.destroy()
            else:
                messagebox.showwarning("Sélection", "Veuillez sélectionner un produit!")

        # Add product button
        def add_product_and_refresh():
            self.open_produit_form()
            # Refresh product list after potential addition
            self.root.after(100, lambda: self.load_products_in_dialog(products_tree, search_var.get().lower()))

        ttk.Button(buttons_frame, text="➕ Nouveau Produit",
                  command=add_product_and_refresh).pack(side=tk.LEFT)

        ttk.Button(buttons_frame, text="Sélectionner",
                  command=select_product).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="Annuler",
                  command=dialog.destroy).pack(side=tk.RIGHT)

        # Bind double-click to select
        products_tree.bind('<Double-Button-1>', lambda e: select_product())

        # Focus on search entry
        search_entry.focus()

    def load_products_in_dialog(self, tree, search_term):
        """Load products in the selection dialog"""
        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Get products from database
        try:
            if search_term:
                self.cursor.execute("SELECT produit, prix_achat_ht FROM produits WHERE LOWER(produit) LIKE ? ORDER BY produit",
                                  (f'%{search_term}%',))
            else:
                self.cursor.execute("SELECT produit, prix_achat_ht FROM produits ORDER BY produit")

            products = self.cursor.fetchall()

            # Add products to tree
            for product, price in products:
                price_display = f"{price:.2f}" if price else "0.00"
                tree.insert('', tk.END, values=(product, price_display))

        except Exception as e:
            print(f"Error loading products: {e}")
            # Add sample products if database fails
            sample_products = [
                ("Ordinateur portable", "5000.00"),
                ("Chaise de bureau", "800.00"),
                ("Papier A4", "50.00"),
                ("Écran 24 pouces", "1200.00"),
                ("Table de bureau", "1500.00")
            ]
            for product, price in sample_products:
                if not search_term or search_term in product.lower():
                    tree.insert('', tk.END, values=(product, price))

    def apply_selected_product(self, item, product_name, product_price):
        """Apply selected product to the row"""
        try:
            # Get current values
            current_values = list(self.devis_details_tree.item(item, 'values'))

            if len(current_values) >= 8:
                # Update values
                current_values[1] = product_name  # Designation
                current_values[2] = "unité"       # Unit
                current_values[4] = product_price # Purchase price

                # Calculate other values
                try:
                    quantity = float(current_values[3]) if current_values[3] else 1.0
                    purchase_price = float(product_price) if product_price else 0.0
                    margin = float(current_values[5]) if current_values[5] and current_values[5] != '1.2' else 1.2

                    selling_price = purchase_price * margin
                    total_price = selling_price * quantity

                    current_values[6] = f"{selling_price:.2f}"
                    current_values[7] = f"{total_price:.2f}"

                except (ValueError, TypeError):
                    current_values[6] = "0.00"
                    current_values[7] = "0.00"

                # Update the row
                self.devis_details_tree.item(item, values=current_values)

        except Exception as e:
            print(f"Error applying product: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de l'application du produit: {e}")

    def add_detail_row(self):
        """Add a new detail row"""
        # Get next row number
        children = self.devis_details_tree.get_children()
        next_num = len(children) + 1

        # Insert new row
        self.devis_details_tree.insert('', tk.END,
            values=(str(next_num), 'Cliquez pour sélectionner', 'unité', '1', '0', '1.2', '0.00', '0.00'))

    def edit_detail_row(self):
        """Edit selected detail row"""
        selection = self.devis_details_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à modifier!")
            return

        item = selection[0]
        self.open_product_selection_dialog(item)

    def delete_detail_row(self):
        """Delete selected detail row"""
        selection = self.devis_details_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à supprimer!")
            return

        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer cette ligne?"):
            self.devis_details_tree.delete(selection[0])
            # Renumber remaining rows
            self.renumber_detail_rows()

    def renumber_detail_rows(self):
        """Renumber all detail rows"""
        children = self.devis_details_tree.get_children()
        for i, child in enumerate(children, 1):
            values = list(self.devis_details_tree.item(child, 'values'))
            values[0] = str(i)
            self.devis_details_tree.item(child, values=values)

    def save_devis_details(self, devis_id):
        """Save details from the Details table to the database"""
        try:
            # Get all rows from the Details table
            children = self.devis_details_tree.get_children()

            for i, child in enumerate(children, 1):
                values = self.devis_details_tree.item(child, 'values')

                if len(values) >= 8:
                    n_ligne = i
                    designation = values[1] if values[1] else 'sélectionné produit'
                    u = values[2] if values[2] else 'automatique'
                    qte = float(values[3]) if values[3] and values[3] != '' else 0.0
                    prix_achat_ht = float(values[4]) if values[4] and values[4] != '' else 0.0
                    marge = float(values[5]) if values[5] and values[5] != '' else 1.2
                    prix_ht = float(values[6]) if values[6] and values[6] != '' else 0.0
                    prix_total_ht = float(values[7]) if values[7] and values[7] != '' else 0.0

                    # Insert detail row
                    self.cursor.execute("""
                        INSERT INTO details_devis (devis_id, n_ligne, designation, u, qte,
                        prix_achat_ht, marge, prix_ht, prix_total_ht)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (devis_id, n_ligne, designation, u, qte, prix_achat_ht, marge, prix_ht, prix_total_ht))

        except Exception as e:
            print(f"Error saving devis details: {e}")
            raise e

    def load_devis_details(self, n_devis):
        """Load details from database into the Details table"""
        try:
            # Clear existing rows
            for item in self.devis_details_tree.get_children():
                self.devis_details_tree.delete(item)

            # Get devis ID
            self.cursor.execute("SELECT id FROM devis WHERE n_devis=?", (n_devis,))
            result = self.cursor.fetchone()

            if result:
                devis_id = result[0]

                # Load details
                self.cursor.execute("""
                    SELECT n_ligne, designation, u, qte, prix_achat_ht, marge, prix_ht, prix_total_ht
                    FROM details_devis
                    WHERE devis_id=?
                    ORDER BY n_ligne
                """, (devis_id,))

                details = self.cursor.fetchall()

                # Populate the Details table
                for detail in details:
                    n_ligne, designation, u, qte, prix_achat_ht, marge, prix_ht, prix_total_ht = detail

                    # Format values for display
                    qte_display = f"{qte:.0f}" if qte == int(qte) else f"{qte:.2f}"
                    prix_achat_display = f"{prix_achat_ht:.2f}" if prix_achat_ht else "0.00"
                    marge_display = f"{marge:.1f}" if marge else "1.2"
                    prix_ht_display = f"{prix_ht:.2f}" if prix_ht else "0.00"
                    prix_total_display = f"{prix_total_ht:.2f}" if prix_total_ht else "0.00"

                    self.devis_details_tree.insert('', tk.END, values=(
                        str(n_ligne), designation, u, qte_display, prix_achat_display,
                        marge_display, prix_ht_display, prix_total_display
                    ))

        except Exception as e:
            print(f"Error loading devis details: {e}")

    def update_devis_totals(self, devis_id):
        """Calculate and update totals for the devis"""
        try:
            # Calculate total from details
            self.cursor.execute("""
                SELECT SUM(prix_total_ht) FROM details_devis WHERE devis_id=?
            """, (devis_id,))

            result = self.cursor.fetchone()
            total_ht = result[0] if result and result[0] else 0.0

            # Calculate TVA (20% by default)
            tva_rate = 20.0
            total_ttc = total_ht * (1 + tva_rate / 100)

            # Update devis totals
            self.cursor.execute("""
                UPDATE devis SET total_ht=?, tva_rate=?, total_ttc=? WHERE id=?
            """, (total_ht, tva_rate, total_ttc, devis_id))

        except Exception as e:
            print(f"Error updating devis totals: {e}")

    # Old dropdown functions removed - using new clean implementation

    def add_test_dropdown_button(self):
        """Add a test button to verify dropdown functionality"""
        try:
            # Find the details frame
            details_frame = None
            for child in self.root.winfo_children():
                if isinstance(child, tk.Toplevel):
                    for subchild in child.winfo_children():
                        if hasattr(subchild, 'winfo_children'):
                            for frame in subchild.winfo_children():
                                if isinstance(frame, ttk.LabelFrame) and 'Détails' in str(frame):
                                    details_frame = frame
                                    break

            if details_frame:
                test_btn = ttk.Button(details_frame, text="🧪 Test Dropdown",
                                    command=self.test_dropdown_functionality)
                test_btn.grid(row=4, column=0, pady=5, sticky=tk.W)
                print("✅ Test button added")
        except Exception as e:
            print(f"⚠️ Could not add test button: {e}")

    def test_dropdown_functionality(self):
        """Test the dropdown functionality"""
        print("🧪 Testing dropdown functionality...")

        # Get the first item in the tree
        children = self.devis_details_tree.get_children()
        if children:
            first_item = children[0]
            print(f"🎯 Testing with first item: {first_item}")
            self.open_products_dropdown(first_item)
        else:
            print("❌ No items in tree to test with")

    # Old event handlers removed - using new clean implementation

    def load_produits_cache(self):
        """تحميل المنتجات في الذاكرة المؤقتة"""
        try:
            self.cursor.execute("SELECT code, produit, prix_achat_ht FROM produits ORDER BY produit")
            self.produits_cache = self.cursor.fetchall()
            print(f"✅ تم تحميل {len(self.produits_cache)} منتج من قاعدة البيانات")

            # إذا لم توجد منتجات، إضافة منتجات تجريبية
            if not self.produits_cache:
                print("⚠️ لا توجد منتجات في قاعدة البيانات، إضافة منتجات تجريبية...")
                self.produits_cache = [
                    ("EL001", "كمبيوتر محمول Dell", 5000.0),
                    ("BU001", "كرسي مكتب جلد", 800.0),
                    ("PA001", "ورق A4 أبيض", 50.0),
                    ("EL002", "شاشة 24 بوصة Samsung", 1200.0),
                    ("BU002", "طاولة مكتب خشبية", 1500.0),
                    ("EL003", "ماوس لاسلكي", 150.0),
                    ("EL004", "لوحة مفاتيح", 200.0),
                    ("BU003", "خزانة ملفات", 900.0),
                    ("PA002", "أقلام حبر جاف", 25.0),
                    ("EL005", "طابعة ليزر", 2500.0)
                ]
                print(f"✅ تم إضافة {len(self.produits_cache)} منتج تجريبي")

            # طباعة قائمة المنتجات للتأكد
            print("📋 قائمة المنتجات المتاحة:")
            for i, (code, name, price) in enumerate(self.produits_cache[:5]):
                print(f"  {i+1}. {name} - {price} DH")
            if len(self.produits_cache) > 5:
                print(f"  ... و {len(self.produits_cache) - 5} منتج آخر")

        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {e}")
            # منتجات افتراضية في حالة الخطأ
            self.produits_cache = [
                ("EL001", "كمبيوتر محمول", 5000.0),
                ("BU001", "كرسي مكتب", 800.0),
                ("PA001", "ورق A4", 50.0),
                ("EL002", "شاشة كمبيوتر", 1200.0),
                ("BU002", "طاولة مكتب", 1500.0)
            ]
            print(f"✅ تم تحميل {len(self.produits_cache)} منتج افتراضي")

    # Old event handlers removed - now using universal cell editing system

    def close_dropdown_on_outside_click(self, event):
        """Fermer dropdown en cliquant à l'extérieur"""
        if self.current_dropdown and event.widget != self.current_dropdown:
            # Vérifier si le clic est en dehors de la dropdown
            try:
                if not str(event.widget).startswith(str(self.current_dropdown)):
                    self.close_current_dropdown()
            except:
                self.close_current_dropdown()

    def open_products_dropdown(self, item):
        """Open products dropdown for product selection"""
        try:
            # Check if item exists
            if not self.devis_details_tree.exists(item):
                print(f"❌ Item {item} does not exist in tree")
                return

            # Close any existing dropdown
            self.close_current_dropdown()

            # Get cell position
            bbox = self.devis_details_tree.bbox(item, '#2')
            if not bbox:
                print(f"❌ Could not get bbox for item {item}, column #2")
                return

            x, y, width, height = bbox

            # Calculate absolute position
            tree_x = self.devis_details_tree.winfo_rootx()
            tree_y = self.devis_details_tree.winfo_rooty()

            # Create dropdown window - FIXED VERSION
            dropdown = tk.Toplevel(self.root)
            dropdown.title("Sélectionner un produit")
            dropdown.configure(bg='#ffffff')
            dropdown.resizable(False, False)
            dropdown.transient(self.root)
            dropdown.grab_set()  # Make it modal

            # Calculate position and size
            dropdown_x = tree_x + x
            dropdown_y = tree_y + y + height + 2
            dropdown_width = max(width + 100, 400)
            dropdown_height = 300

            # Ensure dropdown stays within screen bounds
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            if dropdown_x + dropdown_width > screen_width:
                dropdown_x = screen_width - dropdown_width - 10
            if dropdown_y + dropdown_height > screen_height:
                dropdown_y = tree_y + y - dropdown_height - 2

            dropdown.geometry(f"{dropdown_width}x{dropdown_height}+{dropdown_x}+{dropdown_y}")

            # Create dropdown content
            self.create_products_dropdown_content(dropdown, item)

            # Save dropdown reference
            self.current_dropdown = dropdown
            self.dropdown_active = True

            # Focus on dropdown
            dropdown.focus_set()
            dropdown.lift()
            dropdown.update()

        except Exception as e:
            print(f"❌ Error opening products dropdown: {e}")
            import traceback
            traceback.print_exc()
            self.close_current_dropdown()

    def create_products_dropdown_content(self, dropdown, item):
        """Create products dropdown content with debugging"""
        print(f"🎨 Creating dropdown content for item: {item}")
        try:
            # Main frame
            print(f"🏗️ Creating main frame...")
            main_frame = ttk.Frame(dropdown)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            print(f"✅ Main frame created and packed")

            # العنوان
            title_frame = ttk.Frame(main_frame)
            title_frame.pack(fill=tk.X, pady=(0, 8))

            title_label = ttk.Label(title_frame, text="🛍️ اختر منتج من القائمة",
                                  font=('Segoe UI', 10, 'bold'), foreground='#2c3e50')
            title_label.pack(side=tk.LEFT)

            close_btn = ttk.Button(title_frame, text="✕", width=3,
                                 command=self.close_current_dropdown)
            close_btn.pack(side=tk.RIGHT)

            # إطار البحث
            search_frame = ttk.Frame(main_frame)
            search_frame.pack(fill=tk.X, pady=(0, 8))

            ttk.Label(search_frame, text="🔍 بحث:", font=('Segoe UI', 9)).pack(side=tk.LEFT)

            search_var = tk.StringVar()
            search_entry = ttk.Entry(search_frame, textvariable=search_var,
                                   font=('Segoe UI', 9), width=30)
            search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

            # قائمة المنتجات
            list_frame = ttk.Frame(main_frame)
            list_frame.pack(fill=tk.BOTH, expand=True)

            # إنشاء Treeview للمنتجات
            columns = ('product', 'price')
            products_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

            # Set column headers (French)
            products_tree.heading('product', text='Produit')
            products_tree.heading('price', text='Prix (DH)')

            # Set column widths
            products_tree.column('product', width=250)
            products_tree.column('price', width=100)

            products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # شريط التمرير
            scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=products_tree.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            products_tree.configure(yscrollcommand=scrollbar.set)

            # ملء القائمة بالمنتجات
            self.populate_products_tree(products_tree, "")

            # دالة البحث
            def on_search(*args):
                search_term = search_var.get().lower()
                self.populate_products_tree(products_tree, search_term)

            search_var.trace('w', on_search)

            # Product selection function - SIMPLIFIED
            def on_product_select(event=None):
                print(f"🖱️ Product selection triggered")
                selection = products_tree.selection()
                print(f"📋 Selection: {selection}")
                if selection:
                    item_data = products_tree.item(selection[0], 'values')
                    product_name = item_data[0]
                    print(f"🛍️ Selected product: {product_name}")
                    # Store the product name before closing dropdown
                    selected_product = product_name
                    # Close dropdown
                    self.close_current_dropdown()
                    # Apply product with delay to ensure dropdown is closed
                    self.root.after(200, lambda: self.apply_product_to_row_simple(item, selected_product))
                else:
                    print("❌ No product selected")

            # Simple event bindings - FIXED VERSION
            def on_tree_select(event):
                """Handle tree selection"""
                print(f"🖱️ Tree selection changed")
                selection = products_tree.selection()
                if selection:
                    print(f"📋 Item selected: {selection[0]}")

            def on_tree_double_click(event):
                """Handle double click"""
                print(f"🖱️🖱️ Tree double-clicked")
                on_product_select()

            # Bind events
            products_tree.bind('<<TreeviewSelect>>', on_tree_select)
            products_tree.bind('<Double-Button-1>', on_tree_double_click)
            products_tree.bind('<Return>', lambda e: on_product_select())
            search_entry.bind('<Return>', lambda e: on_product_select())

            # Make sure tree can receive focus
            products_tree.focus_set()

            # Buttons frame
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X, pady=(5, 0))

            # Test button to check if buttons work
            test_btn = ttk.Button(buttons_frame, text="🧪 Test",
                                 command=lambda: print("🧪 Test button clicked!"))
            test_btn.pack(side=tk.LEFT)

            # Select button
            select_btn = ttk.Button(buttons_frame, text="✓ Sélectionner",
                                   command=on_product_select)
            select_btn.pack(side=tk.RIGHT, padx=(5, 0))

            # Cancel button
            cancel_btn = ttk.Button(buttons_frame, text="✗ Annuler",
                                   command=self.close_current_dropdown)
            cancel_btn.pack(side=tk.RIGHT)

            # Focus on search
            search_entry.focus_set()

            # Add keyboard shortcuts
            dropdown.bind('<Escape>', lambda e: self.close_current_dropdown())
            search_entry.bind('<Escape>', lambda e: self.close_current_dropdown())
            products_tree.bind('<Escape>', lambda e: self.close_current_dropdown())

        except Exception as e:
            print(f"Error creating dropdown content: {e}")
            self.close_current_dropdown()

    def on_tree_click(self, event, tree):
        """Handle single click on tree to select item"""
        try:
            # Identify the clicked item
            item = tree.identify_row(event.y)
            if item:
                # Select the item
                tree.selection_set(item)
                tree.focus(item)
                print(f"🖱️ Item selected: {item}")
        except Exception as e:
            print(f"Error handling tree click: {e}")

    def populate_products_tree(self, tree, search_term):
        """Fill products tree with filtered data"""
        try:
            # Clear current content
            for item in tree.get_children():
                tree.delete(item)

            # Ensure products cache is loaded
            if not hasattr(self, 'produits_cache') or not self.produits_cache:
                print("⚠️ Products cache not loaded, loading now...")
                self.load_produits_cache()

            # Add filtered products
            if hasattr(self, 'produits_cache') and self.produits_cache:
                for code, product, price in self.produits_cache:
                    if search_term == "" or search_term in product.lower():
                        price_display = f"{price:.2f}" if price else "0.00"
                        tree.insert('', tk.END, values=(product, price_display))
            else:
                # Fallback: Load directly from database
                print("⚠️ Using fallback database query for products...")
                try:
                    if search_term:
                        self.cursor.execute("SELECT produit, prix_achat_ht FROM produits WHERE LOWER(produit) LIKE ? ORDER BY produit",
                                          (f'%{search_term}%',))
                    else:
                        self.cursor.execute("SELECT produit, prix_achat_ht FROM produits ORDER BY produit")

                    products = self.cursor.fetchall()
                    for product, price in products:
                        price_display = f"{price:.2f}" if price else "0.00"
                        tree.insert('', tk.END, values=(product, price_display))

                except Exception as db_error:
                    print(f"Database fallback failed: {db_error}")

            # Select first item if available
            children = tree.get_children()
            if children:
                tree.selection_set(children[0])
                tree.focus(children[0])
            else:
                print("⚠️ No products found to display")

        except Exception as e:
            print(f"Error populating products tree: {e}")
            import traceback
            traceback.print_exc()

    def apply_product_to_row(self, item, product_name):
        """Apply selected product to the row"""
        try:
            print(f"🔄 Applying product '{product_name}' to item '{item}'")

            # Search for product details
            product_info = None
            if hasattr(self, 'produits_cache') and self.produits_cache:
                for code, product, price in self.produits_cache:
                    if product == product_name:
                        product_info = (code, product, price)
                        break

            # Fallback: query database directly if cache not available
            if not product_info:
                try:
                    self.cursor.execute("SELECT code, produit, prix_achat_ht FROM produits WHERE produit = ?", (product_name,))
                    result = self.cursor.fetchone()
                    if result:
                        product_info = result
                except Exception as db_error:
                    print(f"Database query failed: {db_error}")

            if product_info:
                code, product, purchase_price = product_info
                print(f"📦 Product info found: {code} - {product} - {purchase_price} DH")

                # Get current values - use a safer approach
                try:
                    # Check if tree widget is still valid
                    print(f"🔍 Checking tree widget availability...")
                    print(f"   - hasattr(self, 'devis_details_tree'): {hasattr(self, 'devis_details_tree')}")

                    if hasattr(self, 'devis_details_tree'):
                        try:
                            exists = self.devis_details_tree.winfo_exists()
                            print(f"   - tree.winfo_exists(): {exists}")
                        except Exception as e:
                            print(f"   - Error checking winfo_exists(): {e}")
                            exists = False

                        if not exists:
                            print("❌ DEVIS tree widget is not available")
                            return
                    else:
                        print("❌ DEVIS tree widget attribute not found")
                        return

                    current_values = list(self.devis_details_tree.item(item, 'values'))
                    print(f"📋 Current values: {current_values}")
                except Exception as tree_error:
                    print(f"❌ Error accessing tree item: {tree_error}")
                    return

                if len(current_values) >= 8:
                    # Update values
                    current_values[1] = product  # Designation
                    current_values[2] = "unité"   # Unit (French)
                    current_values[4] = str(purchase_price) if purchase_price else "0"  # Purchase price

                    # Calculate other values automatically
                    try:
                        quantity = float(current_values[3]) if current_values[3] and current_values[3] not in ['', 'qte'] else 1.0
                        purchase_price_num = float(current_values[4]) if current_values[4] else 0.0
                        margin = float(current_values[5]) if current_values[5] and current_values[5] not in ['ex 1.2', 'ex 1.1', 'marge'] else 1.2

                        selling_price = purchase_price_num * margin
                        total_price = selling_price * quantity

                        current_values[6] = f"{selling_price:.2f}"  # Selling price
                        current_values[7] = f"{total_price:.2f}"    # Total

                    except (ValueError, TypeError):
                        current_values[6] = "0.00"
                        current_values[7] = "0.00"

                    # Update the row
                    try:
                        self.devis_details_tree.item(item, values=current_values)
                        print(f"✅ Product applied successfully: {product}")
                    except Exception as update_error:
                        print(f"❌ Error updating tree item: {update_error}")
                else:
                    print(f"❌ Insufficient columns in row: {len(current_values)}")
            else:
                print(f"❌ Product info not found for: {product_name}")

        except Exception as e:
            print(f"❌ Error applying product: {e}")
            import traceback
            traceback.print_exc()

    def apply_product_to_row_simple(self, item, product_name):
        """Apply selected product to the row - simplified version"""
        try:
            print(f"🔄 Applying product '{product_name}' to item '{item}' (SIMPLE)")

            # Get product info from cache
            product_info = None
            if hasattr(self, 'produits_cache') and self.produits_cache:
                for code, product, price in self.produits_cache:
                    if product == product_name:
                        product_info = (code, product, price)
                        break

            if not product_info:
                print(f"❌ Product '{product_name}' not found in cache")
                return

            code, product, purchase_price = product_info
            print(f"📦 Product found: {code} - {product} - {purchase_price} DH")

            # Update the tree item directly
            try:
                # Get current values
                current_values = list(self.devis_details_tree.item(item, 'values'))

                # Update the designation column (index 1)
                current_values[1] = product
                current_values[2] = "unité"
                current_values[4] = str(purchase_price) if purchase_price else "0"

                # Calculate selling price and total
                try:
                    quantity = float(current_values[3]) if current_values[3] and current_values[3] != '' else 1.0
                    purchase_price_num = float(purchase_price) if purchase_price else 0.0
                    margin = 1.2  # Default margin

                    selling_price = purchase_price_num * margin
                    total_price = selling_price * quantity

                    current_values[6] = f"{selling_price:.2f}"
                    current_values[7] = f"{total_price:.2f}"
                except:
                    current_values[6] = "0.00"
                    current_values[7] = "0.00"

                # Update the tree
                self.devis_details_tree.item(item, values=current_values)
                print(f"✅ Product applied successfully: {product}")

            except Exception as tree_error:
                print(f"❌ Error updating tree: {tree_error}")

        except Exception as e:
            print(f"❌ Error in simple apply: {e}")
            import traceback
            traceback.print_exc()

    def close_current_dropdown(self):
        """إغلاق القائمة المنسدلة الحالية"""
        try:
            if self.current_dropdown:
                self.current_dropdown.grab_release()  # Release modal grab
                self.current_dropdown.destroy()
                self.current_dropdown = None
                self.dropdown_active = False
                print("🔒 Dropdown closed")
        except Exception as e:
            print(f"❌ Error closing dropdown: {e}")
            self.current_dropdown = None
            self.dropdown_active = False

    # النظام الجديد المحسن يحل محل جميع الدوال القديمة

    def on_devis_cell_double_click(self, event):
        """Gérer le double-clic sur une cellule du devis - TOUTES les colonnes sont éditables"""
        print(f"🖱️ DEVIS Double-click detected at ({event.x}, {event.y})")

        # Identifier l'élément et la colonne cliquée
        item = self.devis_details_tree.identify_row(event.y)
        column = self.devis_details_tree.identify_column(event.x)

        print(f"📍 DEVIS Identified: item='{item}', column='{column}'")

        if not item or not column:
            print("❌ DEVIS No item or column identified")
            return

        # Sélectionner l'élément
        self.devis_details_tree.selection_set(item)
        self.devis_details_tree.focus(item)

        # Obtenir le nom de la colonne
        try:
            column_name = self.devis_details_tree.heading(column)['text']
            print(f"🖱️ DEVIS Double-click sur colonne: {column_name} (#{column})")
        except Exception as e:
            print(f"❌ DEVIS Error getting column name: {e}")
            return

        # Obtenir la position et taille de la cellule
        bbox = self.devis_details_tree.bbox(item, column)
        if not bbox:
            return

        # Obtenir la valeur actuelle
        values = self.devis_details_tree.item(item, 'values')
        column_index = int(column.replace('#', '')) - 1
        current_value = values[column_index] if column_index < len(values) else ""

        print(f"📝 Édition de la cellule: {column_name} = '{current_value}'")

        # TOUTES les colonnes sont éditables - utiliser des éditeurs simples et efficaces
        if column_name == 'Désignation':
            # Pour la désignation, ouvrir la sélection de produits
            self.close_current_dropdown()
            self.open_products_dropdown(item)
        elif column_name == 'U':
            # Pour l'unité, utiliser l'éditeur avec bouton d'ajout
            self.edit_unit_cell(item, column, bbox, current_value)
        else:
            # Pour tous les autres champs, utiliser l'éditeur texte simple
            self.edit_text_cell(item, column, bbox, current_value, column_name)

    def edit_designation_cell(self, item, column, bbox, current_value):
        """Éditer la cellule Désignation avec liste des produits"""
        # Supprimer le widget précédent s'il existe
        if self.edit_widget:
            self.edit_widget.destroy()

        # Récupérer la liste des produits
        self.cursor.execute("SELECT produit FROM produits ORDER BY produit")
        produits_list = [row[0] for row in self.cursor.fetchall()]

        # Créer un Combobox pour la sélection
        self.edit_widget = ttk.Combobox(self.devis_details_tree, values=produits_list)
        self.edit_widget.set(current_value)

        # Positionner le widget
        x, y, width, height = bbox
        self.edit_widget.place(x=x, y=y, width=width, height=height)

        # Variables pour l'édition
        self.editing_item = item
        self.editing_column = column

        # Bind les événements
        self.edit_widget.bind('<Return>', self.save_designation_edit)
        self.edit_widget.bind('<Escape>', self.cancel_edit)
        self.edit_widget.bind('<FocusOut>', self.save_designation_edit)
        self.edit_widget.bind('<<ComboboxSelected>>', self.on_produit_selected)

        # Focus et sélection
        self.edit_widget.focus()
        self.edit_widget.select_range(0, tk.END)

    def edit_unit_cell(self, item, column, bbox, current_value):
        """Éditer la cellule Unité avec dropdown et bouton d'ajout"""
        # Supprimer le widget précédent s'il existe
        if self.edit_widget:
            self.edit_widget.destroy()

        # Créer un frame pour contenir le combobox et le bouton
        edit_frame = tk.Frame(self.devis_details_tree)
        edit_frame.place(x=bbox[0], y=bbox[1], width=bbox[2] + 30, height=bbox[3])

        # Combobox pour les unités
        unit_var = tk.StringVar(value=current_value)
        unit_combo = ttk.Combobox(edit_frame, textvariable=unit_var,
                                 values=self.get_available_units(), width=8)
        unit_combo.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Bouton d'ajout d'unité
        add_unit_btn = ttk.Button(edit_frame, text="➕", width=3,
                                 command=lambda: self.open_unit_add_dialog(
                                     callback_function=lambda new_unit: self.refresh_unit_dropdown(unit_combo, new_unit),
                                     current_widget=unit_combo
                                 ))
        add_unit_btn.pack(side=tk.RIGHT, padx=(2, 0))

        self.edit_widget = edit_frame
        self.editing_item = item
        self.editing_column = column

        def save_unit_value():
            new_value = unit_var.get()
            values = list(self.devis_details_tree.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            if column_index < len(values):
                values[column_index] = new_value
                self.devis_details_tree.item(item, values=values)

            edit_frame.destroy()
            self.edit_widget = None

        def cancel_edit():
            edit_frame.destroy()
            self.edit_widget = None

        # Bind events
        unit_combo.bind('<Return>', lambda e: save_unit_value())
        unit_combo.bind('<Escape>', lambda e: cancel_edit())
        unit_combo.bind('<FocusOut>', lambda e: save_unit_value())

        # Focus on combobox
        unit_combo.focus()

    def on_devis_tab_navigation(self, event):
        """Handle Tab key navigation in DEVIS details"""
        try:
            current_selection = self.devis_details_tree.selection()
            if not current_selection:
                return

            current_item = current_selection[0]

            # Get all items
            all_items = self.devis_details_tree.get_children()
            current_index = all_items.index(current_item)

            # Move to next item
            if current_index < len(all_items) - 1:
                next_item = all_items[current_index + 1]
                self.devis_details_tree.selection_set(next_item)
                self.devis_details_tree.focus(next_item)

            return "break"  # Prevent default Tab behavior
        except Exception as e:
            print(f"Error in DEVIS tab navigation: {e}")

    def on_devis_enter_key(self, event):
        """Handle Enter key in DEVIS details - start editing current cell"""
        try:
            current_selection = self.devis_details_tree.selection()
            if current_selection:
                # Simulate double-click on first editable column (U)
                item = current_selection[0]
                bbox = self.devis_details_tree.bbox(item, '#3')  # U column
                if bbox:
                    self.edit_unit_cell(item, '#3', bbox,
                                      self.devis_details_tree.item(item, 'values')[2])
            return "break"
        except Exception as e:
            print(f"Error in DEVIS enter key: {e}")

    def on_devis_f2_edit(self, event):
        """Handle F2 key - edit current cell"""
        try:
            current_selection = self.devis_details_tree.selection()
            if current_selection:
                # Start editing the designation column
                item = current_selection[0]
                bbox = self.devis_details_tree.bbox(item, '#2')  # Designation column
                if bbox:
                    self.close_current_dropdown()
                    self.open_products_dropdown(item)
            return "break"
        except Exception as e:
            print(f"Error in DEVIS F2 edit: {e}")

    def on_devis_cell_changed(self, item, column, new_value):
        """Handle DEVIS cell value changes with auto-calculation and validation"""
        try:
            # Get current values
            values = list(self.devis_details_tree.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            # Update the specific cell
            if column_index < len(values):
                values[column_index] = new_value

                # Trigger auto-calculation for numeric fields
                if column_index in [3, 4, 5]:  # Qté, Prix achat HT, Marge
                    self.auto_calculate_devis_row(item, values, column_index)

                # Update tree display
                self.devis_details_tree.item(item, values=values)

                print(f"DEVIS cell updated: Column {column_index} = {new_value}")

        except Exception as e:
            print(f"Error handling DEVIS cell change: {e}")

    def save_devis_detail_to_database(self, item):
        """Save DEVIS detail row to database"""
        try:
            values = self.devis_details_tree.item(item, 'values')
            if len(values) >= 8:
                # Get the current DEVIS ID from the form
                current_devis = getattr(self, 'current_editing_devis', None)
                if not current_devis:
                    print("No current DEVIS context for saving")
                    return

                # Get DEVIS ID
                self.cursor.execute("SELECT id FROM devis WHERE n_devis=?", (current_devis,))
                result = self.cursor.fetchone()
                if not result:
                    print(f"DEVIS {current_devis} not found in database")
                    return

                devis_id = result[0]
                n_ligne = int(values[0]) if values[0] else 1

                # Check if detail already exists
                self.cursor.execute("""
                    SELECT id FROM details_devis WHERE devis_id=? AND n_ligne=?
                """, (devis_id, n_ligne))

                detail_values = (
                    devis_id, n_ligne, values[1], values[2],
                    float(values[3]) if values[3] else 0.0,
                    float(values[4]) if values[4] else 0.0,
                    float(values[5]) if values[5] else 0.0,
                    float(values[6]) if values[6] else 0.0,
                    float(values[7]) if values[7] else 0.0
                )

                if self.cursor.fetchone():
                    # Update existing
                    self.cursor.execute("""
                        UPDATE details_devis SET designation=?, u=?, qte=?, prix_achat_ht=?,
                        marge=?, prix_ht=?, prix_total_ht=?
                        WHERE devis_id=? AND n_ligne=?
                    """, detail_values[2:] + (devis_id, n_ligne))
                else:
                    # Insert new
                    self.cursor.execute("""
                        INSERT INTO details_devis (devis_id, n_ligne, designation, u, qte,
                        prix_achat_ht, marge, prix_ht, prix_total_ht)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, detail_values)

                self.conn.commit()
                print(f"DEVIS detail saved: Line {n_ligne}")

        except Exception as e:
            print(f"Error saving DEVIS detail to database: {e}")

    def edit_text_cell(self, item, column, bbox, current_value, column_name):
        """Éditer une cellule texte normale - VERSION SIMPLIFIÉE"""
        print(f"📝 Creating text editor for: {column_name} = '{current_value}'")

        # Supprimer le widget précédent s'il existe
        if hasattr(self, 'edit_widget') and self.edit_widget:
            self.edit_widget.destroy()

        # Créer un Entry simple pour l'édition
        edit_var = tk.StringVar(value=current_value)
        self.edit_widget = tk.Entry(self.devis_details_tree, textvariable=edit_var,
                                   font=('Segoe UI', 9), relief='solid', borderwidth=1)

        # Positionner le widget
        x, y, width, height = bbox
        self.edit_widget.place(x=x, y=y, width=width, height=height)

        # Variables pour l'édition
        self.editing_item = item
        self.editing_column = column

        def save_edit():
            try:
                new_value = edit_var.get()
                print(f"💾 Saving: {column_name} = '{new_value}'")

                # Mettre à jour la cellule
                values = list(self.devis_details_tree.item(item, 'values'))
                column_index = int(column.replace('#', '')) - 1

                if column_index < len(values):
                    values[column_index] = new_value
                    self.devis_details_tree.item(item, values=values)
                    print(f"✅ Cell updated successfully")

                # Nettoyer
                self.edit_widget.destroy()
                self.edit_widget = None

            except Exception as e:
                print(f"❌ Error saving: {e}")

        def cancel_edit():
            print("❌ Edit cancelled")
            self.edit_widget.destroy()
            self.edit_widget = None

        # Bind les événements
        self.edit_widget.bind('<Return>', lambda e: save_edit())
        self.edit_widget.bind('<Escape>', lambda e: cancel_edit())
        self.edit_widget.bind('<FocusOut>', lambda e: save_edit())

        # Focus et sélection - TRÈS IMPORTANT
        self.edit_widget.focus_set()
        self.edit_widget.select_range(0, tk.END)
        print(f"🎯 Focus set on text editor - Ready for typing!")

        # Test if widget is ready for input
        try:
            self.edit_widget.after(100, lambda: print(f"✅ Text editor is active and ready for input"))
        except:
            pass

    def on_produit_selected(self, event=None):
        """Quand un produit est sélectionné, remplir automatiquement les autres champs"""
        if not self.edit_widget or not self.editing_item:
            return

        try:
            produit_nom = self.edit_widget.get()

            # Récupérer les informations du produit
            self.cursor.execute("""
                SELECT prix_achat_ht FROM produits WHERE produit = ?
            """, (produit_nom,))
            result = self.cursor.fetchone()

            if result:
                prix_achat = result[0]

                # Obtenir les valeurs actuelles
                values = list(self.devis_details_tree.item(self.editing_item, 'values'))

                # Mettre à jour automatiquement
                if len(values) >= 8:
                    values[2] = "unité"  # Unité par défaut
                    values[4] = str(prix_achat) if prix_achat else "0"  # Prix achat

                    # Calculer automatiquement si on a les valeurs
                    try:
                        qte = float(values[3]) if values[3] and values[3] != '' else 1.0
                        prix_achat_num = float(values[4]) if values[4] and values[4] != 'automatique' else 0.0
                        marge = float(values[5]) if values[5] and values[5] not in ['ex 1.2', 'ex 1.1'] else 1.2

                        prix_ht = prix_achat_num * marge
                        prix_total = prix_ht * qte

                        values[6] = f"{prix_ht:.2f}"  # Prix HT
                        values[7] = f"{prix_total:.2f}"  # Prix total HT

                    except (ValueError, TypeError):
                        values[6] = "prix achat ht x marge"
                        values[7] = "prix de vente HT"

                    # Mettre à jour la ligne
                    self.devis_details_tree.item(self.editing_item, values=values)
        except Exception as e:
            print(f"Erreur lors de la sélection du produit: {e}")

    def save_designation_edit(self, event=None):
        """Sauvegarder l'édition de la désignation"""
        if not self.edit_widget or not self.editing_item:
            return

        try:
            new_value = self.edit_widget.get()

            # Obtenir les valeurs actuelles
            values = list(self.devis_details_tree.item(self.editing_item, 'values'))
            column_index = int(self.editing_column.replace('#', '')) - 1

            # Mettre à jour la valeur
            if column_index < len(values):
                values[column_index] = new_value
                self.devis_details_tree.item(self.editing_item, values=values)

            # Remplir automatiquement les autres champs si c'est un produit valide
            self.on_produit_selected()



        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
        finally:
            # Nettoyer
            self.cancel_edit()

    def save_text_edit(self, event=None):
        """Sauvegarder l'édition d'une cellule texte"""
        if not self.edit_widget or not self.editing_item:
            return

        try:
            new_value = self.edit_widget.get()

            # Obtenir les valeurs actuelles
            values = list(self.devis_details_tree.item(self.editing_item, 'values'))
            column_index = int(self.editing_column.replace('#', '')) - 1

            # Mettre à jour la valeur
            if column_index < len(values):
                values[column_index] = new_value

                # Recalculer automatiquement si c'est une valeur numérique
                if column_index in [3, 4, 5]:  # Qté, Prix achat, Marge
                    try:
                        qte = float(values[3]) if values[3] and values[3] != '' else 1.0
                        prix_achat = float(values[4]) if values[4] and values[4] not in ['automatique', ''] else 0.0
                        marge = float(values[5]) if values[5] and values[5] not in ['ex 1.2', 'ex 1.1', ''] else 1.2

                        prix_ht = prix_achat * marge
                        prix_total = prix_ht * qte

                        values[6] = f"{prix_ht:.2f}"  # Prix HT
                        values[7] = f"{prix_total:.2f}"  # Prix total HT

                    except (ValueError, TypeError):
                        pass

                self.devis_details_tree.item(self.editing_item, values=values)



        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
        finally:
            # Nettoyer
            self.cancel_edit()

    def cancel_edit(self, event=None):
        """Annuler l'édition"""
        if self.edit_widget:
            self.edit_widget.destroy()
            self.edit_widget = None

        self.editing_item = None
        self.editing_column = None

    def supprimer_ligne_devis(self):
        """Supprimer une ligne de détail du devis"""
        selection = self.devis_details_tree.selection()
        if selection:
            self.devis_details_tree.delete(selection[0])



    def get_next_devis_number(self):
        """Générer le prochain numéro de devis"""
        self.cursor.execute("SELECT COUNT(*) FROM devis")
        count = self.cursor.fetchone()[0]
        return f"DV{count + 1:04d}"

    # Fonctions pour les bons de livraison
    def create_bons_livraison_interface(self, parent):
        """Création de l'interface bons de livraison principale"""
        # Configuration du redimensionnement
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)

        # En-tête avec boutons d'action
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        ttk.Label(header_frame, text="📋 Bons de Livraison", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)

        # Boutons d'action
        actions_frame = ttk.Frame(header_frame)
        actions_frame.grid(row=0, column=2, sticky=tk.E)

        ttk.Button(actions_frame, text="➕ Nouveau Bon", command=self.nouveau_bon_livraison,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🔄 Actualiser", command=self.load_bons_livraison_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="🖨️ Imprimer", command=self.imprimer_bon_livraison).pack(side=tk.LEFT)

        # Barre de recherche et statistiques
        search_stats_frame = ttk.Frame(parent)
        search_stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_stats_frame.columnconfigure(1, weight=1)

        # Barre de recherche
        search_frame = ttk.Frame(search_stats_frame)
        search_frame.grid(row=0, column=0, sticky=tk.W)

        ttk.Label(search_frame, text="🔍 Rechercher:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_bons_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_bons_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_bons_livraison)

        # Statistiques rapides
        stats_frame = ttk.Frame(search_stats_frame)
        stats_frame.grid(row=0, column=2, sticky=tk.E)

        self.bons_stats_label = ttk.Label(stats_frame, text="Total: 0 bons", style='Info.TLabel')
        self.bons_stats_label.pack()

        # Tableau des bons de livraison
        self.create_bons_livraison_table(parent)

    def create_bons_livraison_table(self, parent):
        """Création du tableau des bons de livraison"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Treeview
        columns = ('n_bon', 'type_marche', 'client', 'ice', 'date_creation')
        self.bons_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # En-têtes
        self.bons_tree.heading('n_bon', text='N° Bon')
        self.bons_tree.heading('type_marche', text='Type de Marché')
        self.bons_tree.heading('client', text='Client')
        self.bons_tree.heading('ice', text='ICE')
        self.bons_tree.heading('date_creation', text='Date Création')

        # Largeurs des colonnes
        self.bons_tree.column('n_bon', width=100)
        self.bons_tree.column('type_marche', width=150)
        self.bons_tree.column('client', width=200)
        self.bons_tree.column('ice', width=120)
        self.bons_tree.column('date_creation', width=120)

        self.bons_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.bons_tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.bons_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.bons_tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.bons_tree.configure(xscrollcommand=h_scrollbar.set)

        # Menu contextuel et événements
        self.bons_tree.bind('<Button-3>', self.show_bon_context_menu)
        self.bons_tree.bind('<Double-1>', self.edit_bon_livraison)

        # Barre de statut
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.bons_status_label = ttk.Label(status_frame, text="Prêt", style='Info.TLabel')
        self.bons_status_label.pack(side=tk.LEFT)

        # Informations sur la sélection
        self.bons_selection_label = ttk.Label(status_frame, text="", style='Info.TLabel')
        self.bons_selection_label.pack(side=tk.RIGHT)

        # Lier l'événement de sélection
        self.bons_tree.bind('<<TreeviewSelect>>', self.on_bon_selection)

    def nouveau_bon_livraison(self):
        """Ouvrir la fenêtre de nouveau bon de livraison"""
        self.open_bon_livraison_form()

    def load_bons_livraison_data(self):
        """Chargement des données bons de livraison"""
        # Vider le treeview
        for item in self.bons_tree.get_children():
            self.bons_tree.delete(item)

        # Charger les données
        self.cursor.execute("""
            SELECT n_bon_livraison, type_marche, client, ice, date_creation
            FROM bons_livraison
            ORDER BY n_bon_livraison
        """)

        for row in self.cursor.fetchall():
            n_bon, type_marche, client, ice, date_creation = row
            ice_display = ice if ice else ""

            self.bons_tree.insert('', tk.END, values=(n_bon, type_marche, client, ice_display, date_creation))

        # Mettre à jour les statistiques
        self.update_bons_stats()

    def update_bons_stats(self):
        """Mise à jour des statistiques des bons"""
        # Compter le nombre total de bons
        self.cursor.execute("SELECT COUNT(*) FROM bons_livraison")
        total_bons = self.cursor.fetchone()[0]

        # Mettre à jour le label des statistiques
        stats_text = f"Total: {total_bons} bons de livraison"
        self.bons_stats_label.config(text=stats_text)

    def filter_bons_livraison(self, event=None):
        """Filtrage des bons de livraison"""
        search_term = self.search_bons_var.get().lower()

        # Vider et recharger avec filtre
        for item in self.bons_tree.get_children():
            self.bons_tree.delete(item)

        # Requête avec filtre
        self.cursor.execute("""
            SELECT n_bon_livraison, type_marche, client, ice, date_creation
            FROM bons_livraison
            WHERE LOWER(n_bon_livraison) LIKE ? OR LOWER(client) LIKE ? OR LOWER(ice) LIKE ?
            ORDER BY n_bon_livraison
        """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        for row in self.cursor.fetchall():
            n_bon, type_marche, client, ice, date_creation = row
            ice_display = ice if ice else ""

            self.bons_tree.insert('', tk.END, values=(n_bon, type_marche, client, ice_display, date_creation))

    def on_bon_selection(self, event):
        """Gestion de la sélection d'un bon"""
        selection = self.bons_tree.selection()
        if selection:
            item = selection[0]
            values = self.bons_tree.item(item, 'values')
            n_bon = values[0]
            client = values[2]
            self.bons_selection_label.config(text=f"Sélectionné: {n_bon} - {client}")
        else:
            self.bons_selection_label.config(text="")

    def show_bon_context_menu(self, event):
        """Affichage du menu contextuel pour les bons"""
        item = self.bons_tree.selection()[0] if self.bons_tree.selection() else None
        if item:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="✏️ Modifier", command=self.edit_bon_livraison)
            menu.add_command(label="👁️ Voir détails", command=self.view_bon_livraison)
            menu.add_command(label="🖨️ Imprimer", command=self.imprimer_bon_livraison)
            menu.add_separator()
            menu.add_command(label="🗑️ Supprimer", command=self.delete_bon_livraison)
            menu.tk_popup(event.x_root, event.y_root)

    def edit_bon_livraison(self, event=None):
        """Modifier un bon de livraison - Version améliorée"""
        selection = self.bons_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un bon de livraison à modifier!")
            return

        # Get the selected bon data
        item = self.bons_tree.item(selection[0])
        n_bon = item['values'][0]

        # Fetch full bon data from database
        try:
            self.cursor.execute("""
                SELECT n_bon_livraison, type_marche, devis_n, n, client, ice, if_field, adresse
                FROM bons_livraison
                WHERE n_bon_livraison = ?
            """, (n_bon,))

            bon_data = self.cursor.fetchone()
            if bon_data:
                self.open_bon_livraison_form(bon_data)
            else:
                messagebox.showerror("Erreur", "Impossible de charger les données du bon de livraison!")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {str(e)}")

    def view_bon_livraison(self):
        """Voir les détails d'un bon"""
        messagebox.showinfo("Info", "Affichage détails en cours de développement")

    def delete_bon_livraison(self):
        """Supprimer un bon de livraison - Version améliorée"""
        selection = self.bons_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un bon de livraison à supprimer!")
            return

        # Get the selected bon data
        item = self.bons_tree.item(selection[0])
        n_bon = item['values'][0]
        client = item['values'][2]

        # Confirmation
        if not messagebox.askyesno("Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le bon de livraison?\n\n"
            f"N° Bon: {n_bon}\n"
            f"Client: {client}\n\n"
            f"Cette action est irréversible."):
            return

        try:
            # Get bon ID and delete details first
            self.cursor.execute("SELECT id FROM bons_livraison WHERE n_bon_livraison=?", (n_bon,))
            result = self.cursor.fetchone()
            if result:
                bon_id = result[0]
                self.cursor.execute("DELETE FROM details_bon_livraison WHERE bon_livraison_id=?", (bon_id,))

            # Delete main record
            self.cursor.execute("DELETE FROM bons_livraison WHERE n_bon_livraison=?", (n_bon,))
            self.conn.commit()

            # Refresh the list
            self.load_bons_livraison_data()
            messagebox.showinfo("Succès", f"Bon de livraison {n_bon} supprimé avec succès!")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

    def imprimer_bon_livraison(self):
        """Imprimer un bon de livraison"""
        messagebox.showinfo("Info", "Impression en cours de développement")

    def open_bon_livraison_form(self, bon_data=None):
        """Ouvrir le formulaire de bon de livraison - Version améliorée"""
        # Créer la fenêtre
        form_window = tk.Toplevel(self.root)
        form_window.title("📋 Nouveau Bon de Livraison" if not bon_data else "📋 Modifier Bon de Livraison")

        # Taille adaptative basée sur l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(1000, int(screen_width * 0.85))
        window_height = min(800, int(screen_height * 0.85))

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        form_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Permettre le redimensionnement
        form_window.resizable(True, True)
        form_window.minsize(800, 600)

        form_window.transient(self.root)
        form_window.grab_set()

        # Frame principal avec scrollbar
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configuration pour le redimensionnement automatique
        form_window.columnconfigure(0, weight=1)
        form_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)  # Make details section expandable

        # Variables pour les champs
        n_bon_var = tk.StringVar(value=bon_data[0] if bon_data else self.get_next_bon_number())
        type_marche_var = tk.StringVar(value=bon_data[1] if bon_data else "BC")
        devis_n_var = tk.StringVar(value=bon_data[2] if bon_data else "SÉLECTIONNÉ OU TAPER")
        n_var = tk.StringVar(value=bon_data[3] if bon_data else "automatique")
        client_var = tk.StringVar(value=bon_data[4] if bon_data else "automatique")
        ice_var = tk.StringVar(value=bon_data[5] if bon_data else "")
        if_var = tk.StringVar(value=bon_data[6] if bon_data else "")
        adresse_var = tk.StringVar(value=bon_data[7] if bon_data else "")

        # Titre avec icône et informations
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(1, weight=1)

        title_label = ttk.Label(title_frame, text="📋 BON DE LIVRAISON", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)

        # Date de création
        date_label = ttk.Label(title_frame, text=f"Date: {datetime.now().strftime('%d/%m/%Y')}",
                              style='Info.TLabel')
        date_label.grid(row=0, column=2, sticky=tk.E)

        row = 1

        # Section en-tête du bon - Layout amélioré
        header_frame = ttk.LabelFrame(main_frame, text="📄 Informations Générales", padding="15")
        header_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)
        header_frame.columnconfigure(3, weight=1)

        # N BON DE LIVRAISON
        ttk.Label(header_frame, text="N° BON DE LIVRAISON:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8)
        n_bon_entry = ttk.Entry(header_frame, textvariable=n_bon_var, state='readonly', width=20, font=('Segoe UI', 10, 'bold'))
        n_bon_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=8, padx=(10, 20))

        # Type de marché
        ttk.Label(header_frame, text="Type de marché:", style='Heading.TLabel').grid(row=0, column=2, sticky=tk.W, pady=8)

        # Type selection with add button
        type_selection_frame = ttk.Frame(header_frame)
        type_selection_frame.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=8, padx=(10, 0))
        type_selection_frame.columnconfigure(0, weight=1)

        type_combo = ttk.Combobox(type_selection_frame, textvariable=type_marche_var,
                                 values=["BC", "MARCHÉ"],
                                 state="readonly", width=13)
        type_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add type button
        def add_type_and_refresh():
            self.add_new_type_option(type_combo, type_marche_var)

        add_type_btn = ttk.Button(type_selection_frame, text="➕", width=3,
                                 command=add_type_and_refresh)
        add_type_btn.grid(row=0, column=1)

        # DEVIS N° avec bouton de sélection
        ttk.Label(header_frame, text="DEVIS N°:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8)
        devis_frame = ttk.Frame(header_frame)
        devis_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=8, padx=(10, 20))
        devis_frame.columnconfigure(0, weight=1)

        devis_entry = ttk.Entry(devis_frame, textvariable=devis_n_var, width=15)
        devis_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        devis_select_btn = ttk.Button(devis_frame, text="📋", width=3,
                                     command=lambda: self.select_devis_for_bl(devis_n_var, client_var, ice_var, if_var, adresse_var, n_var))
        devis_select_btn.grid(row=0, column=1)

        # Add tooltip for the button
        def show_tooltip(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.configure(bg='#ffffe0', relief='solid', bd=1)
            label = tk.Label(tooltip, text="Sélectionner un DEVIS pour auto-remplir",
                           bg='#ffffe0', font=('Segoe UI', 8))
            label.pack()
            x, y, _, _ = devis_select_btn.bbox("insert")
            x += devis_select_btn.winfo_rootx() + 25
            y += devis_select_btn.winfo_rooty() + 25
            tooltip.geometry(f"+{x}+{y}")
            tooltip.after(3000, tooltip.destroy)

        devis_select_btn.bind('<Enter>', show_tooltip)

        # N (Numéro de référence)
        ttk.Label(header_frame, text="N° Référence:", style='Heading.TLabel').grid(row=1, column=2, sticky=tk.W, pady=8)
        n_entry = ttk.Entry(header_frame, textvariable=n_var, width=18)
        n_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=8, padx=(10, 0))

        # Section client - Layout amélioré
        client_frame = ttk.LabelFrame(main_frame, text="👤 Informations Client", padding="15")
        client_frame.grid(row=row+1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        client_frame.columnconfigure(1, weight=1)
        client_frame.columnconfigure(3, weight=1)

        # CLIENT avec bouton de sélection
        ttk.Label(client_frame, text="CLIENT:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8)

        # Récupérer la liste des clients
        try:
            self.cursor.execute("SELECT code, nom FROM clients ORDER BY nom")
            clients_list = [f"{code} - {nom}" for code, nom in self.cursor.fetchall()]
        except Exception as e:
            print(f"Error loading clients: {e}")
            clients_list = []

        client_combo = ttk.Combobox(client_frame, textvariable=client_var,
                                   values=["Sélectionner un client..."] + clients_list, width=35)
        client_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=8, padx=(10, 10))

        # Bouton nouveau client
        def add_client_and_refresh_bl():
            self.open_client_form()
            # Refresh client list after potential addition
            self.root.after(100, lambda: self.refresh_client_dropdown(client_combo, client_var))

        new_client_btn = ttk.Button(client_frame, text="➕", width=3,
                                   command=add_client_and_refresh_bl)
        new_client_btn.grid(row=0, column=2, pady=8)

        # ICE et IF sur la même ligne
        ttk.Label(client_frame, text="ICE:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8)
        ice_entry = ttk.Entry(client_frame, textvariable=ice_var, width=25)
        ice_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=8, padx=(10, 10))

        ttk.Label(client_frame, text="IF:", style='Heading.TLabel').grid(row=1, column=2, sticky=tk.W, pady=8, padx=(10, 0))
        if_entry = ttk.Entry(client_frame, textvariable=if_var, width=25)
        if_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=8, padx=(10, 0))

        # Adresse sur toute la largeur
        ttk.Label(client_frame, text="Adresse:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W, pady=8)
        adresse_entry = ttk.Entry(client_frame, textvariable=adresse_var, width=50)
        adresse_entry.grid(row=2, column=1, columnspan=3, sticky=(tk.W, tk.E), pady=8, padx=(10, 0))

        # Section détails - Version améliorée
        details_frame = ttk.LabelFrame(main_frame, text="📦 Détails de Livraison", padding="15")
        details_frame.grid(row=row+2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(1, weight=1)

        # Instructions and status
        instruction_frame = ttk.Frame(details_frame)
        instruction_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        instruction_frame.columnconfigure(1, weight=1)

        instruction_label = ttk.Label(instruction_frame,
                                    text="💡 Double-cliquez sur 'Désignation' pour sélectionner un produit",
                                    style='Info.TLabel')
        instruction_label.grid(row=0, column=0, sticky=tk.W)

        # Auto-population status
        self.bl_auto_status_label = ttk.Label(instruction_frame, text="", style='Info.TLabel')
        self.bl_auto_status_label.grid(row=0, column=1, sticky=tk.E)

        # Tableau des détails
        details_table_frame = ttk.Frame(details_frame)
        details_table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        details_table_frame.columnconfigure(0, weight=1)
        details_table_frame.rowconfigure(0, weight=1)

        # Treeview pour les détails avec colonnes améliorées
        details_columns = ('n', 'designation', 'u', 'qte', 'reste_bc', 'qte_livre', 'reste_stock')
        self.bl_details_tree = ttk.Treeview(details_table_frame, columns=details_columns, show='headings', height=10)

        # En-têtes avec descriptions claires
        self.bl_details_tree.heading('n', text='N°')
        self.bl_details_tree.heading('designation', text='Désignation')
        self.bl_details_tree.heading('u', text='Unité')
        self.bl_details_tree.heading('qte', text='Quantité')
        self.bl_details_tree.heading('reste_bc', text='Reste BC/MARCHÉ')
        self.bl_details_tree.heading('qte_livre', text='Qté Livrée')
        self.bl_details_tree.heading('reste_stock', text='Reste Stock')

        # Largeurs des colonnes optimisées
        self.bl_details_tree.column('n', width=50, minwidth=40)
        self.bl_details_tree.column('designation', width=250, minwidth=200)
        self.bl_details_tree.column('u', width=80, minwidth=60)
        self.bl_details_tree.column('qte', width=80, minwidth=60)
        self.bl_details_tree.column('reste_bc', width=120, minwidth=100)
        self.bl_details_tree.column('qte_livre', width=120, minwidth=100)
        self.bl_details_tree.column('reste_stock', width=120, minwidth=100)

        self.bl_details_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars pour le tableau des détails
        details_v_scrollbar = ttk.Scrollbar(details_table_frame, orient=tk.VERTICAL, command=self.bl_details_tree.yview)
        details_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.bl_details_tree.configure(yscrollcommand=details_v_scrollbar.set)

        # Boutons pour gérer les détails - Version améliorée
        details_buttons_frame = ttk.Frame(details_frame)
        details_buttons_frame.grid(row=2, column=0, pady=(15, 0))

        ttk.Button(details_buttons_frame, text="➕ Ajouter Ligne",
                  command=lambda: self.add_bl_detail_row()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="✏️ Modifier Ligne",
                  command=lambda: self.edit_bl_detail_row()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="🗑️ Supprimer Ligne",
                  command=lambda: self.delete_bl_detail_row()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="📋 Importer du DEVIS",
                  command=lambda: self.import_from_devis()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="🔄 Auto-Remplir",
                  command=lambda: self.trigger_auto_populate_bl(devis_n_var, client_var, ice_var, if_var, adresse_var, n_var)).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="📊 Calculer Stock",
                  command=self.update_stock_calculations_for_all_bl_rows).pack(side=tk.LEFT)

        # Bind double-click for product selection and cell editing
        self.bl_details_tree.bind('<Double-Button-1>', self.on_bl_cell_double_click)

        # Setup cell editing for BL
        self.setup_bl_cell_editing()

        # Ajouter une ligne d'exemple
        self.add_sample_bl_row()

        # Fonction pour mettre à jour les informations client automatiquement
        def on_client_change(*args):
            client_selection = client_var.get()
            if client_selection != "automatique" and " - " in client_selection:
                client_code = client_selection.split(" - ")[0]
                # Récupérer les informations du client
                self.cursor.execute("""
                    SELECT ice, if_field, adresse FROM clients WHERE code = ?
                """, (client_code,))
                client_info = self.cursor.fetchone()
                if client_info:
                    ice_var.set(client_info[0] or "")
                    if_var.set(client_info[1] or "")
                    adresse_var.set(client_info[2] or "")

        # Lier l'événement de changement de client
        client_var.trace('w', on_client_change)

        # Boutons principaux
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row+3, column=0, columnspan=3, pady=20)

        def save_bon():
            # Validation améliorée
            if not client_var.get().strip() or client_var.get() in ["automatique", "Sélectionner un client..."]:
                messagebox.showerror("Erreur", "Veuillez sélectionner un client!")
                return

            # Validation des détails
            children = self.bl_details_tree.get_children()
            if not children:
                if not messagebox.askyesno("Confirmation", "Aucun détail n'a été ajouté. Voulez-vous continuer?"):
                    return

            # Sauvegarder
            try:
                bon_id = None

                if bon_data:  # Modification
                    # Update main record
                    self.cursor.execute("""
                        UPDATE bons_livraison SET type_marche=?, devis_n=?, n=?, client=?,
                        ice=?, if_field=?, adresse=? WHERE n_bon_livraison=?
                    """, (type_marche_var.get(), devis_n_var.get(), n_var.get(), client_var.get(),
                          ice_var.get(), if_var.get(), adresse_var.get(), n_bon_var.get()))

                    # Get bon ID
                    self.cursor.execute("SELECT id FROM bons_livraison WHERE n_bon_livraison=?", (n_bon_var.get(),))
                    bon_id = self.cursor.fetchone()[0]

                    # Delete existing details
                    self.cursor.execute("DELETE FROM details_bon_livraison WHERE bon_livraison_id=?", (bon_id,))

                else:  # Nouveau
                    # Insert new record
                    self.cursor.execute("""
                        INSERT INTO bons_livraison (n_bon_livraison, type_marche, devis_n, n, client,
                        ice, if_field, adresse) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (n_bon_var.get(), type_marche_var.get(), devis_n_var.get(), n_var.get(),
                          client_var.get(), ice_var.get(), if_var.get(), adresse_var.get()))

                    bon_id = self.cursor.lastrowid

                # Save details
                self.save_bl_details(bon_id)

                self.conn.commit()
                self.load_bons_livraison_data()
                form_window.destroy()
                messagebox.showinfo("Succès", "Bon de livraison sauvegardé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        def cancel():
            form_window.destroy()

        def delete_bon():
            if bon_data and messagebox.askyesno("Confirmation",
                "Êtes-vous sûr de vouloir supprimer ce bon de livraison?\nCette action est irréversible."):
                try:
                    # Delete details first
                    self.cursor.execute("SELECT id FROM bons_livraison WHERE n_bon_livraison=?", (n_bon_var.get(),))
                    result = self.cursor.fetchone()
                    if result:
                        bon_id = result[0]
                        self.cursor.execute("DELETE FROM details_bon_livraison WHERE bon_livraison_id=?", (bon_id,))

                    # Delete main record
                    self.cursor.execute("DELETE FROM bons_livraison WHERE n_bon_livraison=?", (n_bon_var.get(),))
                    self.conn.commit()
                    self.load_bons_livraison_data()
                    form_window.destroy()
                    messagebox.showinfo("Succès", "Bon de livraison supprimé avec succès!")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

        def print_bon():
            messagebox.showinfo("Impression", "Fonctionnalité d'impression en cours de développement")

        # Boutons avec style amélioré
        ttk.Button(buttons_frame, text="✓ Valider", command=save_bon,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))

        if bon_data:  # Show delete button only when editing
            ttk.Button(buttons_frame, text="🗑️ Supprimer", command=delete_bon,
                      style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="🖨️ Imprimer", command=print_bon).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ Annuler", command=cancel).pack(side=tk.LEFT)

        # Load existing details if editing
        if bon_data:
            form_window.after(100, lambda: self.load_bl_details(bon_data[0]))

        # Focus sur le type de marché
        type_combo.focus()

    def get_next_bon_number(self):
        """Générer le prochain numéro de bon"""
        self.cursor.execute("SELECT COUNT(*) FROM bons_livraison")
        count = self.cursor.fetchone()[0]
        return f"BL{count + 1:04d}"

    # Enhanced BL Details Management Methods
    def add_sample_bl_row(self):
        """Add a sample row to the BL details table"""
        self.bl_details_tree.insert('', tk.END, values=(
            '1', 'Cliquez pour sélectionner un produit', 'unité', '1', '1', '0', '1'
        ))

    def add_bl_detail_row(self):
        """Add a new detail row to BL"""
        # Get next row number
        children = self.bl_details_tree.get_children()
        next_num = len(children) + 1

        # Insert new row
        self.bl_details_tree.insert('', tk.END, values=(
            str(next_num), 'Cliquez pour sélectionner un produit', 'unité', '1', '1', '0', '1'
        ))

    def edit_bl_detail_row(self):
        """Edit selected BL detail row"""
        selection = self.bl_details_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à modifier!")
            return

        item = selection[0]
        self.open_bl_product_selection_dialog(item)

    def delete_bl_detail_row(self):
        """Delete selected BL detail row"""
        selection = self.bl_details_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à supprimer!")
            return

        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer cette ligne?"):
            self.bl_details_tree.delete(selection[0])
            # Renumber remaining rows
            self.renumber_bl_detail_rows()

    def renumber_bl_detail_rows(self):
        """Renumber all BL detail rows"""
        children = self.bl_details_tree.get_children()
        for i, child in enumerate(children, 1):
            values = list(self.bl_details_tree.item(child, 'values'))
            values[0] = str(i)
            self.bl_details_tree.item(child, values=values)

    def setup_bl_cell_editing(self):
        """Configurer l'édition directe des cellules du bon de livraison"""
        # Variables pour l'édition BL
        self.bl_editing_item = None
        self.bl_editing_column = None
        self.bl_edit_widget = None

        # Bind keyboard navigation for BL
        self.bl_details_tree.bind('<Tab>', self.on_bl_tab_navigation)
        self.bl_details_tree.bind('<Return>', self.on_bl_enter_key)
        self.bl_details_tree.bind('<F2>', self.on_bl_f2_edit)

    def on_bl_cell_double_click(self, event):
        """Gérer le double-clic sur une cellule du bon de livraison - TOUTES les colonnes sont éditables"""
        # Identifier l'élément et la colonne cliquée
        item = self.bl_details_tree.identify_row(event.y)
        column = self.bl_details_tree.identify_column(event.x)

        if not item or not column:
            return

        # Sélectionner l'élément
        self.bl_details_tree.selection_set(item)
        self.bl_details_tree.focus(item)

        # Obtenir le nom de la colonne
        try:
            column_name = self.bl_details_tree.heading(column)['text']
        except:
            return

        print(f"🖱️ BL Double-click sur colonne: {column_name} (#{column})")

        # Obtenir la position et taille de la cellule
        bbox = self.bl_details_tree.bbox(item, column)
        if not bbox:
            return

        # Obtenir la valeur actuelle
        values = self.bl_details_tree.item(item, 'values')
        column_index = int(column.replace('#', '')) - 1
        current_value = values[column_index] if column_index < len(values) else ""

        print(f"📝 BL Édition de la cellule: {column_name} = '{current_value}'")

        # TOUTES les colonnes sont éditables - utiliser des éditeurs simples
        if column_name == 'Désignation':
            # Pour la désignation, ouvrir la sélection de produits
            self.close_bl_current_dropdown()
            self.open_bl_product_selection_dialog(item)
        elif column_name == 'U':
            # Pour l'unité, utiliser l'éditeur avec bouton d'ajout
            self.edit_bl_unit_cell(item, column, bbox, current_value)
        else:
            # Pour tous les autres champs, utiliser l'éditeur texte simple
            self.edit_bl_text_cell(item, column, bbox, current_value, column_name)

    def close_bl_current_dropdown(self):
        """Fermer le dropdown actuel du BL"""
        if hasattr(self, 'bl_edit_widget') and self.bl_edit_widget:
            self.bl_edit_widget.destroy()
            self.bl_edit_widget = None

    def edit_bl_unit_cell(self, item, column, bbox, current_value):
        """Éditer la cellule Unité du BL avec dropdown et bouton d'ajout"""
        # Supprimer le widget précédent s'il existe
        self.close_bl_current_dropdown()

        # Créer un frame pour contenir le combobox et le bouton
        edit_frame = tk.Frame(self.bl_details_tree)
        edit_frame.place(x=bbox[0], y=bbox[1], width=bbox[2] + 30, height=bbox[3])

        # Combobox pour les unités
        unit_var = tk.StringVar(value=current_value)
        unit_combo = ttk.Combobox(edit_frame, textvariable=unit_var,
                                 values=self.get_available_units(), width=8)
        unit_combo.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Bouton d'ajout d'unité
        add_unit_btn = ttk.Button(edit_frame, text="➕", width=3,
                                 command=lambda: self.open_unit_add_dialog(
                                     callback_function=lambda new_unit: self.refresh_unit_dropdown(unit_combo, new_unit),
                                     current_widget=unit_combo
                                 ))
        add_unit_btn.pack(side=tk.RIGHT, padx=(2, 0))

        self.bl_edit_widget = edit_frame
        self.bl_editing_item = item
        self.bl_editing_column = column

        def save_bl_unit_value():
            new_value = unit_var.get()
            values = list(self.bl_details_tree.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            if column_index < len(values):
                values[column_index] = new_value
                self.bl_details_tree.item(item, values=values)

                # Trigger stock calculation if needed
                if values[1] and values[5]:  # If product and quantity delivered exist
                    product_name = values[1]
                    qte_livre = values[5]
                    remaining_stock = self.calculate_remaining_stock(product_name, qte_livre)
                    values[6] = remaining_stock
                    self.bl_details_tree.item(item, values=values)
                    print(f"📦 Stock updated after unit change: {remaining_stock}")

            edit_frame.destroy()
            self.bl_edit_widget = None

        def cancel_bl_edit():
            edit_frame.destroy()
            self.bl_edit_widget = None

        # Bind events
        unit_combo.bind('<Return>', lambda e: save_bl_unit_value())
        unit_combo.bind('<Escape>', lambda e: cancel_bl_edit())
        unit_combo.bind('<FocusOut>', lambda e: save_bl_unit_value())

        # Focus on combobox
        unit_combo.focus()

    def edit_bl_text_cell(self, item, column, bbox, current_value, column_name):
        """Éditer une cellule texte normale du BL - VERSION SIMPLIFIÉE"""
        print(f"📝 BL Creating text editor for: {column_name} = '{current_value}'")

        # Supprimer le widget précédent s'il existe
        self.close_bl_current_dropdown()

        # Créer le widget d'édition simple
        edit_var = tk.StringVar(value=current_value)
        edit_entry = tk.Entry(self.bl_details_tree, textvariable=edit_var,
                             font=('Segoe UI', 9), relief='solid', borderwidth=1)
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])

        self.bl_edit_widget = edit_entry
        self.bl_editing_item = item
        self.bl_editing_column = column

        def save_bl_text_value():
            try:
                new_value = edit_var.get()
                print(f"💾 BL Saving: {column_name} = '{new_value}'")

                values = list(self.bl_details_tree.item(item, 'values'))
                column_index = int(column.replace('#', '')) - 1

                if column_index < len(values):
                    values[column_index] = new_value
                    self.bl_details_tree.item(item, values=values)

                    # IMPORTANT: Trigger stock calculation when quantity delivered changes
                    if column_index == 5 and values[1]:  # Qté Livrée column and product exists
                        product_name = values[1]
                        qte_livre = new_value if new_value else "0"
                        remaining_stock = self.calculate_remaining_stock(product_name, qte_livre)
                        values[6] = remaining_stock  # Update Reste Stock column
                        self.bl_details_tree.item(item, values=values)  # Update again with new stock
                        print(f"📦 Auto-updated stock for {product_name}: {remaining_stock}")

                    print(f"✅ BL Cell updated successfully")

                edit_entry.destroy()
                self.bl_edit_widget = None

            except Exception as e:
                print(f"❌ BL Error saving: {e}")

        def cancel_bl_text_edit():
            print("❌ BL Edit cancelled")
            edit_entry.destroy()
            self.bl_edit_widget = None

        # Bind events
        edit_entry.bind('<Return>', lambda e: save_bl_text_value())
        edit_entry.bind('<Escape>', lambda e: cancel_bl_text_edit())
        edit_entry.bind('<FocusOut>', lambda e: save_bl_text_value())

        # Focus and select all - TRÈS IMPORTANT
        edit_entry.focus_set()
        edit_entry.select_range(0, tk.END)
        print(f"🎯 BL Focus set on text editor")

    def on_bl_tab_navigation(self, event):
        """Handle Tab key navigation in BL details"""
        try:
            current_selection = self.bl_details_tree.selection()
            if not current_selection:
                return

            current_item = current_selection[0]

            # Get all items
            all_items = self.bl_details_tree.get_children()
            current_index = all_items.index(current_item)

            # Move to next item
            if current_index < len(all_items) - 1:
                next_item = all_items[current_index + 1]
                self.bl_details_tree.selection_set(next_item)
                self.bl_details_tree.focus(next_item)

            return "break"  # Prevent default Tab behavior
        except Exception as e:
            print(f"Error in BL tab navigation: {e}")

    def on_bl_enter_key(self, event):
        """Handle Enter key in BL details - start editing current cell"""
        try:
            current_selection = self.bl_details_tree.selection()
            if current_selection:
                # Simulate double-click on first editable column (U)
                item = current_selection[0]
                bbox = self.bl_details_tree.bbox(item, '#3')  # U column
                if bbox:
                    self.edit_bl_unit_cell(item, '#3', bbox,
                                         self.bl_details_tree.item(item, 'values')[2])
            return "break"
        except Exception as e:
            print(f"Error in BL enter key: {e}")

    def on_bl_f2_edit(self, event):
        """Handle F2 key - edit current cell"""
        try:
            current_selection = self.bl_details_tree.selection()
            if current_selection:
                # Start editing the designation column
                item = current_selection[0]
                bbox = self.bl_details_tree.bbox(item, '#2')  # Designation column
                if bbox:
                    self.close_bl_current_dropdown()
                    self.open_bl_product_selection_dialog(item)
            return "break"
        except Exception as e:
            print(f"Error in BL F2 edit: {e}")

    def on_bl_cell_changed(self, item, column, new_value):
        """Handle BL cell value changes with auto-calculation and validation"""
        try:
            # Get current values
            values = list(self.bl_details_tree.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            # Update the specific cell
            if column_index < len(values):
                values[column_index] = new_value

                # Trigger auto-calculation for numeric fields
                if column_index in [3, 5]:  # Qté, Qté Livrée
                    self.auto_calculate_bl_row(item, values, column_index)

                    # Auto-update remaining stock when quantity delivered changes
                    if column_index == 5 and values[1]:  # Qté Livrée column and product exists
                        product_name = values[1]
                        qte_livre = new_value if new_value else "0"
                        remaining_stock = self.calculate_remaining_stock(product_name, qte_livre)
                        values[6] = remaining_stock  # Update Reste Stock column
                        print(f"📦 Auto-updated stock for {product_name}: {remaining_stock}")

                # Update tree display
                self.bl_details_tree.item(item, values=values)

                print(f"BL cell updated: Column {column_index} = {new_value}")

        except Exception as e:
            print(f"Error handling BL cell change: {e}")

    def save_bl_detail_to_database(self, item):
        """Save BL detail row to database"""
        try:
            values = self.bl_details_tree.item(item, 'values')
            if len(values) >= 7:
                # Get the current BL ID from the form
                current_bl = getattr(self, 'current_editing_bl', None)
                if not current_bl:
                    print("No current BL context for saving")
                    return

                # Get BL ID
                self.cursor.execute("SELECT id FROM bons_livraison WHERE n_bon_livraison=?", (current_bl,))
                result = self.cursor.fetchone()
                if not result:
                    print(f"BL {current_bl} not found in database")
                    return

                bl_id = result[0]
                n_ligne = int(values[0]) if values[0] else 1

                # Check if detail already exists
                self.cursor.execute("""
                    SELECT id FROM details_bon_livraison WHERE bon_livraison_id=? AND n_ligne=?
                """, (bl_id, n_ligne))

                detail_values = (
                    bl_id, n_ligne, values[1], values[2],
                    float(values[3]) if values[3] else 0.0,
                    values[4], values[5], values[6]
                )

                if self.cursor.fetchone():
                    # Update existing
                    self.cursor.execute("""
                        UPDATE details_bon_livraison SET designation=?, u=?, qte=?,
                        reste_bc_marche=?, qte_livre_dans_bc=?, reste_en_stock=?
                        WHERE bon_livraison_id=? AND n_ligne=?
                    """, detail_values[2:] + (bl_id, n_ligne))
                else:
                    # Insert new
                    self.cursor.execute("""
                        INSERT INTO details_bon_livraison (bon_livraison_id, n_ligne, designation, u, qte,
                        reste_bc_marche, qte_livre_dans_bc, reste_en_stock)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, detail_values)

                self.conn.commit()
                print(f"BL detail saved: Line {n_ligne}")

        except Exception as e:
            print(f"Error saving BL detail to database: {e}")

    def open_bl_product_selection_dialog(self, item):
        """Open product selection dialog for BL"""
        # Create dialog window
        dialog = tk.Toplevel(self.root)
        dialog.title("Sélectionner un produit pour le BL")
        dialog.geometry("600x450")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (450 // 2)
        dialog.geometry(f"600x450+{x}+{y}")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="📦 Sélectionner un produit pour le bon de livraison",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 15))

        # Search frame
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(search_frame, text="🔍 Recherche:", style='Heading.TLabel').pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=35)
        search_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)

        # Products list frame
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Create treeview for products
        columns = ('product', 'unit', 'stock')
        products_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        products_tree.heading('product', text='Produit')
        products_tree.heading('unit', text='Unité')
        products_tree.heading('stock', text='Stock')
        products_tree.column('product', width=350)
        products_tree.column('unit', width=100)
        products_tree.column('stock', width=100)

        products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=products_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        products_tree.configure(yscrollcommand=scrollbar.set)

        # Load products
        self.load_products_in_bl_dialog(products_tree, "")

        # Search functionality
        def filter_products(*args):
            search_term = search_var.get().lower()
            self.load_products_in_bl_dialog(products_tree, search_term)

        search_var.trace('w', filter_products)

        # Quantity frame
        qty_frame = ttk.Frame(main_frame)
        qty_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(qty_frame, text="Quantité:", style='Heading.TLabel').pack(side=tk.LEFT)
        qty_var = tk.StringVar(value="1")
        qty_entry = ttk.Entry(qty_frame, textvariable=qty_var, width=10)
        qty_entry.pack(side=tk.LEFT, padx=(10, 0))

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def select_product():
            selection = products_tree.selection()
            if selection:
                item_data = products_tree.item(selection[0], 'values')
                product_name = item_data[0]
                unit = item_data[1]
                quantity = qty_var.get()
                self.apply_selected_product_to_bl(item, product_name, unit, quantity)
                dialog.destroy()
            else:
                messagebox.showwarning("Sélection", "Veuillez sélectionner un produit!")

        # Add product button
        def add_product_and_refresh_bl():
            self.open_produit_form()
            # Refresh product list after potential addition
            self.root.after(100, lambda: self.load_products_in_bl_dialog(products_tree, search_var.get().lower()))

        ttk.Button(buttons_frame, text="➕ Nouveau Produit",
                  command=add_product_and_refresh_bl).pack(side=tk.LEFT)

        ttk.Button(buttons_frame, text="✓ Sélectionner",
                  command=select_product, style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="❌ Annuler",
                  command=dialog.destroy).pack(side=tk.RIGHT)

        # Bind double-click to select
        products_tree.bind('<Double-Button-1>', lambda e: select_product())

        # Focus on search entry
        search_entry.focus()

    # Legacy methods for compatibility
    def ajouter_ligne_detail(self):
        """Legacy method - redirects to new implementation"""
        self.add_bl_detail_row()

    def modifier_ligne_detail(self):
        """Legacy method - redirects to new implementation"""
        self.edit_bl_detail_row()

    def supprimer_ligne_detail(self):
        """Legacy method - redirects to new implementation"""
        self.delete_bl_detail_row()

    def load_products_in_bl_dialog(self, tree, search_term):
        """Load products in the BL selection dialog"""
        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Get products from database
        try:
            if search_term:
                self.cursor.execute("""
                    SELECT produit, unite, COALESCE(quantite, 0) as stock
                    FROM produits
                    WHERE LOWER(produit) LIKE ?
                    ORDER BY produit
                """, (f'%{search_term}%',))
            else:
                self.cursor.execute("""
                    SELECT produit, unite, COALESCE(quantite, 0) as stock
                    FROM produits
                    ORDER BY produit
                """)

            products = self.cursor.fetchall()

            # Add products to tree
            for product, unit, stock in products:
                unit_display = unit if unit else "unité"
                stock_display = f"{stock:.0f}" if stock else "0"
                tree.insert('', tk.END, values=(product, unit_display, stock_display))

        except Exception as e:
            print(f"Error loading products for BL: {e}")
            # Add sample products if database fails
            sample_products = [
                ("Ordinateur portable", "unité", "5"),
                ("Chaise de bureau", "unité", "12"),
                ("Papier A4", "rame", "25"),
                ("Écran 24 pouces", "unité", "8"),
                ("Table de bureau", "unité", "3")
            ]
            for product, unit, stock in sample_products:
                if not search_term or search_term in product.lower():
                    tree.insert('', tk.END, values=(product, unit, stock))

    def apply_selected_product_to_bl(self, item, product_name, unit, quantity):
        """Apply selected product to the BL row with automatic stock calculation"""
        try:
            # Get current values
            current_values = list(self.bl_details_tree.item(item, 'values'))

            if len(current_values) >= 7:
                # Update values
                current_values[1] = product_name  # Designation
                current_values[2] = unit          # Unit
                current_values[3] = quantity      # Quantity
                current_values[4] = "AUTO"        # Reste BC/MARCHÉ
                current_values[5] = "0"           # Qté Livrée (initially 0)

                # Calculate remaining stock automatically (0 delivered initially)
                remaining_stock = self.calculate_remaining_stock(product_name, 0)
                current_values[6] = str(remaining_stock)  # Reste Stock

                # Update the row
                self.bl_details_tree.item(item, values=current_values)
                print(f"📦 Product applied: {product_name} - Stock remaining: {remaining_stock}")

        except Exception as e:
            print(f"Error applying product to BL: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de l'application du produit: {e}")

    def calculate_remaining_stock(self, product_name, quantity_to_deliver):
        """Calculate remaining stock after delivery - FAST calculation"""
        try:
            # Get current stock from products table
            self.cursor.execute("""
                SELECT COALESCE(quantite, 0) as current_stock
                FROM produits
                WHERE produit = ?
            """, (product_name,))

            result = self.cursor.fetchone()
            if result:
                current_stock = float(result[0])
                quantity_to_deliver = float(quantity_to_deliver) if quantity_to_deliver else 0
                remaining = current_stock - quantity_to_deliver

                # Format the result
                if remaining == int(remaining):
                    return f"{int(remaining)}"
                else:
                    return f"{remaining:.2f}"
            else:
                return "N/A"

        except Exception as e:
            print(f"❌ Error calculating remaining stock: {e}")
            return "ERROR"

    def update_stock_calculations_for_all_bl_rows(self):
        """Update stock calculations for all rows in BL details - FAST batch update"""
        try:
            print("🔄 Updating stock calculations for all BL rows...")

            # Get all children (rows) in the BL details tree
            for child in self.bl_details_tree.get_children():
                values = list(self.bl_details_tree.item(child, 'values'))

                if len(values) >= 7 and values[1]:  # If designation exists
                    product_name = values[1]
                    qte_livre = values[5] if values[5] and values[5] != '' else "0"

                    try:
                        qte_livre_num = float(qte_livre)
                    except:
                        qte_livre_num = 0

                    # Calculate remaining stock
                    remaining_stock = self.calculate_remaining_stock(product_name, qte_livre_num)
                    values[6] = remaining_stock

                    # Update the row
                    self.bl_details_tree.item(child, values=values)

            print("✅ Stock calculations updated for all rows")

        except Exception as e:
            print(f"❌ Error updating stock calculations: {e}")

    def save_bl_details(self, bon_id):
        """Save BL details to database"""
        try:
            # Get all rows from the Details table
            children = self.bl_details_tree.get_children()

            for i, child in enumerate(children, 1):
                values = self.bl_details_tree.item(child, 'values')

                if len(values) >= 7:
                    n_ligne = i
                    designation = values[1] if values[1] else 'AUTOMATIQUE'
                    u = values[2] if values[2] else 'automatique'
                    qte = float(values[3]) if values[3] and values[3] != '' else 0.0
                    reste_bc = values[4] if values[4] else 'AUTO/manuel'
                    qte_livre = values[5] if values[5] else 'qté livré - qté dans BC'
                    reste_stock = values[6] if values[6] else 'AUTO/manuel'

                    # Insert detail row
                    self.cursor.execute("""
                        INSERT INTO details_bon_livraison (bon_livraison_id, n_ligne, designation, u, qte,
                        reste_bc_marche, qte_livre_dans_bc, reste_en_stock)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (bon_id, n_ligne, designation, u, qte, reste_bc, qte_livre, reste_stock))

        except Exception as e:
            print(f"Error saving BL details: {e}")
            raise e

    def load_bl_details(self, n_bon):
        """Load BL details from database"""
        try:
            # Clear existing rows
            for item in self.bl_details_tree.get_children():
                self.bl_details_tree.delete(item)

            # Get bon ID
            self.cursor.execute("SELECT id FROM bons_livraison WHERE n_bon_livraison=?", (n_bon,))
            result = self.cursor.fetchone()

            if result:
                bon_id = result[0]

                # Load details
                self.cursor.execute("""
                    SELECT n_ligne, designation, u, qte, reste_bc_marche, qte_livre_dans_bc, reste_en_stock
                    FROM details_bon_livraison
                    WHERE bon_livraison_id=?
                    ORDER BY n_ligne
                """, (bon_id,))

                details = self.cursor.fetchall()

                # Populate the Details table
                for detail in details:
                    n_ligne, designation, u, qte, reste_bc, qte_livre, reste_stock = detail

                    # Format values for display
                    qte_display = f"{qte:.0f}" if qte == int(qte) else f"{qte:.2f}"

                    self.bl_details_tree.insert('', tk.END, values=(
                        str(n_ligne), designation, u, qte_display, reste_bc, qte_livre, reste_stock
                    ))

        except Exception as e:
            print(f"Error loading BL details: {e}")

    def select_devis_for_bl(self, devis_var, client_var, ice_var, if_var, adresse_var, n_var=None):
        """Select a DEVIS to populate BL fields with automatic population"""
        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("📄 Sélectionner un DEVIS pour Auto-Population")
        dialog.geometry("800x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"800x500+{x}+{y}")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title with instructions
        title_label = ttk.Label(main_frame, text="📄 Sélectionner un DEVIS pour Auto-Population", style='Title.TLabel')
        title_label.pack(pady=(0, 10))

        info_label = ttk.Label(main_frame,
                              text="💡 La sélection d'un DEVIS remplira automatiquement tous les champs marqués 'AUTOMATIQUE'",
                              style='Info.TLabel')
        info_label.pack(pady=(0, 15))

        # Search frame
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="🔍 Recherche:", style='Heading.TLabel').pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)

        # DEVIS list
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        columns = ('n_devis', 'client', 'objet', 'total', 'date')
        devis_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        devis_tree.heading('n_devis', text='N° DEVIS')
        devis_tree.heading('client', text='Client')
        devis_tree.heading('objet', text='Objet')
        devis_tree.heading('total', text='Total HT')
        devis_tree.heading('date', text='Date')

        devis_tree.column('n_devis', width=100)
        devis_tree.column('client', width=200)
        devis_tree.column('objet', width=200)
        devis_tree.column('total', width=100)
        devis_tree.column('date', width=100)

        devis_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=devis_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        devis_tree.configure(yscrollcommand=scrollbar.set)

        # Load DEVIS function
        def load_devis_list(search_term=""):
            # Clear existing items
            for item in devis_tree.get_children():
                devis_tree.delete(item)

            try:
                if search_term:
                    self.cursor.execute("""
                        SELECT n_devis, client, objet, COALESCE(total_ht, 0), date_creation
                        FROM devis
                        WHERE LOWER(n_devis) LIKE ? OR LOWER(client) LIKE ? OR LOWER(objet) LIKE ?
                        ORDER BY date_creation DESC
                    """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
                else:
                    self.cursor.execute("""
                        SELECT n_devis, client, objet, COALESCE(total_ht, 0), date_creation
                        FROM devis
                        ORDER BY date_creation DESC
                    """)

                for row in self.cursor.fetchall():
                    n_devis, client, objet, total, date = row
                    total_display = f"{total:.2f} DH" if total else "0.00 DH"
                    date_display = date.split()[0] if date else ""
                    devis_tree.insert('', tk.END, values=(n_devis, client, objet, total_display, date_display))

            except Exception as e:
                print(f"Error loading DEVIS: {e}")

        # Initial load
        load_devis_list()

        # Search functionality
        def filter_devis(*args):
            search_term = search_var.get().lower()
            load_devis_list(search_term)

        search_var.trace('w', filter_devis)

        # Auto-population options
        options_frame = ttk.LabelFrame(main_frame, text="Options d'Auto-Population", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 15))

        populate_details_var = tk.BooleanVar(value=True)
        populate_client_var = tk.BooleanVar(value=True)

        ttk.Checkbutton(options_frame, text="📦 Remplir les détails produits",
                       variable=populate_details_var).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(options_frame, text="👤 Remplir les informations client",
                       variable=populate_client_var).pack(side=tk.LEFT)

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def select_devis():
            selection = devis_tree.selection()
            if selection:
                item_data = devis_tree.item(selection[0], 'values')
                n_devis = item_data[0]

                # Perform automatic population
                success = self.auto_populate_bl_from_devis(
                    n_devis, devis_var, client_var, ice_var, if_var, adresse_var, n_var,
                    populate_details_var.get(), populate_client_var.get()
                )

                if success:
                    dialog.destroy()
                    messagebox.showinfo("Succès",
                        f"✅ Auto-population réussie!\n\n"
                        f"DEVIS {n_devis} sélectionné et tous les champs automatiques ont été remplis.")
            else:
                messagebox.showwarning("Sélection", "Veuillez sélectionner un DEVIS!")

        ttk.Button(buttons_frame, text="✓ Sélectionner et Auto-Remplir",
                  command=select_devis, style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="❌ Annuler",
                  command=dialog.destroy).pack(side=tk.RIGHT)

        # Bind double-click
        devis_tree.bind('<Double-Button-1>', lambda e: select_devis())

        # Focus on search
        search_entry.focus()

    def auto_populate_bl_from_devis(self, n_devis, devis_var, client_var, ice_var, if_var, adresse_var, n_var=None,
                                   populate_details=True, populate_client=True):
        """Automatically populate BL fields from selected DEVIS with status updates"""
        try:
            print(f"🚀 Starting auto-population from DEVIS {n_devis}")

            # Update status
            if hasattr(self, 'bl_auto_status_label'):
                self.bl_auto_status_label.config(text="🔄 Auto-population en cours...")
                self.root.update()

            # Get DEVIS main data
            self.cursor.execute("""
                SELECT n_devis, nature_prestation, client, adresse, ice, objet, id
                FROM devis
                WHERE n_devis = ?
            """, (n_devis,))

            devis_data = self.cursor.fetchone()
            if not devis_data:
                if hasattr(self, 'bl_auto_status_label'):
                    self.bl_auto_status_label.config(text="❌ DEVIS introuvable")
                messagebox.showerror("Erreur", f"DEVIS {n_devis} introuvable!")
                return False

            n_devis_db, nature, client, adresse, ice, objet, devis_id = devis_data
            print(f"✅ DEVIS data loaded: {n_devis_db}")

            # Update status
            if hasattr(self, 'bl_auto_status_label'):
                self.bl_auto_status_label.config(text="📄 Remplissage des champs...")
                self.root.update()

            # 1. Populate DEVIS number
            devis_var.set(n_devis_db)
            print(f"📄 DEVIS number set: {n_devis_db}")

            # 2. Populate client information if requested
            if populate_client:
                if hasattr(self, 'bl_auto_status_label'):
                    self.bl_auto_status_label.config(text="👤 Remplissage client...")
                    self.root.update()
                self.auto_populate_client_info(client, client_var, ice_var, if_var, adresse_var, ice, adresse)

            # 3. Populate reference number if provided and empty/automatic
            if n_var and (not n_var.get() or n_var.get() in ["automatique", "AUTO", "AUTOMATIQUE"]):
                # Use DEVIS number as reference
                n_var.set(f"REF-{n_devis_db}")
                print(f"🔢 Reference number set: REF-{n_devis_db}")

            # 4. Populate details if requested
            if populate_details:
                if hasattr(self, 'bl_auto_status_label'):
                    self.bl_auto_status_label.config(text="📦 Remplissage détails...")
                    self.root.update()
                self.auto_populate_bl_details_from_devis(devis_id)

            # Final status
            if hasattr(self, 'bl_auto_status_label'):
                self.bl_auto_status_label.config(text="✅ Auto-population terminée")
                # Clear status after 3 seconds
                self.root.after(3000, lambda: self.bl_auto_status_label.config(text=""))

            print(f"✅ Auto-population completed successfully")
            return True

        except Exception as e:
            print(f"❌ Error in auto-population: {e}")
            if hasattr(self, 'bl_auto_status_label'):
                self.bl_auto_status_label.config(text="❌ Erreur auto-population")
            messagebox.showerror("Erreur", f"Erreur lors de l'auto-population: {str(e)}")
            return False

    def auto_populate_client_info(self, client, client_var, ice_var, if_var, adresse_var, devis_ice=None, devis_adresse=None):
        """Auto-populate client information"""
        try:
            print(f"👤 Populating client info for: {client}")

            # Set client if empty or automatic
            if not client_var.get() or client_var.get() in ["automatique", "AUTO", "AUTOMATIQUE", "Sélectionner un client..."]:
                client_var.set(client)
                print(f"✅ Client set: {client}")

            # Get detailed client information from database
            if " - " in client:
                client_code = client.split(" - ")[0]
                self.cursor.execute("""
                    SELECT ice, if_field, adresse FROM clients WHERE code = ?
                """, (client_code,))
                client_info = self.cursor.fetchone()

                if client_info:
                    db_ice, db_if, db_adresse = client_info

                    # Populate ICE (prefer DEVIS ICE, then client ICE)
                    if not ice_var.get() or ice_var.get() in ["", "automatique", "AUTO", "AUTOMATIQUE"]:
                        ice_to_use = devis_ice or db_ice or ""
                        ice_var.set(ice_to_use)
                        print(f"🆔 ICE set: {ice_to_use}")

                    # Populate IF
                    if not if_var.get() or if_var.get() in ["", "automatique", "AUTO", "AUTOMATIQUE"]:
                        if_var.set(db_if or "")
                        print(f"🆔 IF set: {db_if or ''}")

                    # Populate Address (prefer DEVIS address, then client address)
                    if not adresse_var.get() or adresse_var.get() in ["", "automatique", "AUTO", "AUTOMATIQUE"]:
                        adresse_to_use = devis_adresse or db_adresse or ""
                        adresse_var.set(adresse_to_use)
                        print(f"🏠 Address set: {adresse_to_use}")

        except Exception as e:
            print(f"❌ Error populating client info: {e}")

    def auto_populate_bl_details_from_devis(self, devis_id):
        """Auto-populate BL details from DEVIS details"""
        try:
            print(f"📦 Populating details from DEVIS ID: {devis_id}")

            # Get DEVIS details
            self.cursor.execute("""
                SELECT n_ligne, designation, u, qte, prix_achat_ht, marge, prix_ht, prix_total_ht
                FROM details_devis
                WHERE devis_id = ?
                ORDER BY n_ligne
            """, (devis_id,))

            devis_details = self.cursor.fetchall()
            print(f"📋 Found {len(devis_details)} detail lines")

            if not devis_details:
                print("⚠️ No details found in DEVIS")
                return

            # Clear existing automatic rows (keep manually entered ones)
            self.clear_automatic_bl_details()

            # Add DEVIS details to BL
            for detail in devis_details:
                n_ligne, designation, u, qte, prix_achat, marge, prix_ht, prix_total = detail

                # Format values
                qte_display = f"{qte:.0f}" if qte == int(qte) else f"{qte:.2f}"
                u_display = u if u else "unité"

                # Calculate remaining quantities (initially same as ordered)
                reste_bc = f"{qte:.0f}" if qte == int(qte) else f"{qte:.2f}"
                qte_livre = "0"  # Initially nothing delivered

                # Calculate actual remaining stock
                reste_stock = self.calculate_remaining_stock(designation, 0)  # 0 delivered initially

                # Insert into BL details tree
                self.bl_details_tree.insert('', tk.END, values=(
                    str(n_ligne), designation, u_display, qte_display,
                    reste_bc, qte_livre, reste_stock
                ))

                print(f"➕ Added detail: {designation} - Qty: {qte_display}")

            # Renumber rows
            self.renumber_bl_detail_rows()
            print(f"✅ Details population completed")

        except Exception as e:
            print(f"❌ Error populating details: {e}")

    def clear_automatic_bl_details(self):
        """Clear only automatic/placeholder rows from BL details"""
        try:
            children = list(self.bl_details_tree.get_children())
            for child in children:
                values = self.bl_details_tree.item(child, 'values')
                if len(values) >= 2:
                    designation = values[1]
                    # Remove rows with automatic placeholders
                    if designation in ["AUTOMATIQUE", "AUTO", "automatique", "Cliquez pour sélectionner un produit",
                                     "Cliquez pour sélectionner", "🔽 Cliquez pour sélectionner un produit"]:
                        self.bl_details_tree.delete(child)
                        print(f"🗑️ Removed automatic row: {designation}")

        except Exception as e:
            print(f"❌ Error clearing automatic details: {e}")

    def import_from_devis(self):
        """Import details from a DEVIS - Enhanced version"""
        # Create a simple DEVIS selection dialog for import
        dialog = tk.Toplevel(self.root)
        dialog.title("📋 Importer depuis un DEVIS")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"600x400+{x}+{y}")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="📋 Importer les détails depuis un DEVIS", style='Title.TLabel')
        title_label.pack(pady=(0, 15))

        # DEVIS list
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        columns = ('n_devis', 'client', 'objet')
        devis_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        devis_tree.heading('n_devis', text='N° DEVIS')
        devis_tree.heading('client', text='Client')
        devis_tree.heading('objet', text='Objet')

        devis_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=devis_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        devis_tree.configure(yscrollcommand=scrollbar.set)

        # Load DEVIS
        try:
            self.cursor.execute("SELECT n_devis, client, objet, id FROM devis ORDER BY date_creation DESC")
            for row in self.cursor.fetchall():
                devis_tree.insert('', tk.END, values=row[:3])
        except Exception as e:
            print(f"Error loading DEVIS for import: {e}")

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def import_details():
            selection = devis_tree.selection()
            if selection:
                item_data = devis_tree.item(selection[0], 'values')
                n_devis = item_data[0]

                # Get DEVIS ID
                try:
                    self.cursor.execute("SELECT id FROM devis WHERE n_devis = ?", (n_devis,))
                    result = self.cursor.fetchone()
                    if result:
                        devis_id = result[0]
                        self.auto_populate_bl_details_from_devis(devis_id)
                        dialog.destroy()
                        messagebox.showinfo("Succès", f"✅ Détails importés depuis DEVIS {n_devis}")
                    else:
                        messagebox.showerror("Erreur", "DEVIS introuvable!")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de l'import: {str(e)}")
            else:
                messagebox.showwarning("Sélection", "Veuillez sélectionner un DEVIS!")

        ttk.Button(buttons_frame, text="✓ Importer",
                  command=import_details, style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="❌ Annuler",
                  command=dialog.destroy).pack(side=tk.RIGHT)

        # Bind double-click
        devis_tree.bind('<Double-Button-1>', lambda e: import_details())

    def trigger_auto_populate_bl(self, devis_var, client_var, ice_var, if_var, adresse_var, n_var):
        """Trigger auto-population manually from current DEVIS field"""
        current_devis = devis_var.get().strip()

        if not current_devis or current_devis in ["", "automatique", "AUTO", "AUTOMATIQUE"]:
            messagebox.showwarning("DEVIS requis",
                "Veuillez d'abord sélectionner un DEVIS en utilisant le bouton 📋 ou saisir un numéro de DEVIS.")
            return

        # Confirm auto-population
        if messagebox.askyesno("Confirmation Auto-Population",
            f"Voulez-vous auto-remplir tous les champs automatiques avec les données du DEVIS {current_devis}?\n\n"
            f"⚠️ Cela remplacera les valeurs actuelles marquées comme 'AUTOMATIQUE'."):

            # Perform auto-population
            success = self.auto_populate_bl_from_devis(
                current_devis, devis_var, client_var, ice_var, if_var, adresse_var, n_var,
                populate_details=True, populate_client=True
            )

            if not success:
                messagebox.showerror("Erreur",
                    f"Impossible d'auto-remplir depuis le DEVIS {current_devis}.\n"
                    f"Vérifiez que le DEVIS existe et contient des données.")

    # ==================== MARCHÉS FUNCTIONALITY ====================

    def load_marches_data(self):
        """Charger les données des marchés"""
        try:
            # Vider le tableau
            for item in self.marches_tree.get_children():
                self.marches_tree.delete(item)

            # Charger les marchés
            self.cursor.execute("""
                SELECT n_marche, type_marche, client, objet, montant_ttc, statut, date_creation
                FROM marches
                ORDER BY date_creation DESC
            """)

            marches = self.cursor.fetchall()

            for marche in marches:
                n_marche, type_marche, client, objet, montant_ttc, statut, date_creation = marche

                # Formater les données pour l'affichage
                montant_display = f"{montant_ttc:.2f} DH" if montant_ttc else "0.00 DH"
                date_display = date_creation.split()[0] if date_creation else ""
                objet_display = objet[:50] + "..." if len(objet) > 50 else objet

                self.marches_tree.insert('', tk.END, values=(
                    n_marche, type_marche, client, objet_display, montant_display, statut, date_display
                ))

            # Mettre à jour les statistiques
            count = len(marches)
            total_montant = sum(m[4] for m in marches if m[4])
            self.marches_stats_label.config(
                text=f"Total: {count} marchés | Montant total: {total_montant:.2f} DH"
            )

        except Exception as e:
            print(f"Erreur lors du chargement des marchés: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {str(e)}")

    def nouveau_marche(self):
        """Créer un nouveau marché"""
        self.open_marche_form()

    def modifier_marche(self):
        """Modifier le marché sélectionné"""
        selection = self.marches_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un marché à modifier!")
            return

        # Récupérer les données du marché
        item = self.marches_tree.item(selection[0])
        n_marche = item['values'][0]

        try:
            self.cursor.execute("""
                SELECT n_marche, type_marche, devis_n, nature_prestation, objet, delai_execution,
                       client, montant_ttc, caution_provisoire, caution_definitif_population,
                       caution_definitif, ordre_service, caution_retenue_garantie,
                       delai_prevu_achevement, statut
                FROM marches
                WHERE n_marche = ?
            """, (n_marche,))

            marche_data = self.cursor.fetchone()
            if marche_data:
                self.open_marche_form(marche_data)
            else:
                messagebox.showerror("Erreur", "Impossible de charger les données du marché!")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {str(e)}")

    def search_marches(self):
        """Rechercher dans les marchés avec filtres"""
        search_term = self.marches_search_var.get().lower()
        status_filter = self.marches_status_filter_var.get() if hasattr(self, 'marches_status_filter_var') else "Tous"

        # Vider le tableau
        for item in self.marches_tree.get_children():
            self.marches_tree.delete(item)

        try:
            # Construire la requête avec filtres
            base_query = """
                SELECT n_marche, type_marche, client, objet, montant_ttc, statut, date_creation
                FROM marches
                WHERE 1=1
            """
            params = []

            # Filtre de recherche textuelle
            if search_term:
                base_query += " AND (LOWER(n_marche) LIKE ? OR LOWER(client) LIKE ? OR LOWER(objet) LIKE ?)"
                params.extend([f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'])

            # Filtre par statut
            if status_filter and status_filter != "Tous":
                base_query += " AND statut = ?"
                params.append(status_filter)

            base_query += " ORDER BY date_creation DESC"

            self.cursor.execute(base_query, params)
            marches = self.cursor.fetchall()

            for marche in marches:
                n_marche, type_marche, client, objet, montant_ttc, statut, date_creation = marche

                montant_display = f"{montant_ttc:.2f} DH" if montant_ttc else "0.00 DH"
                date_display = date_creation.split()[0] if date_creation else ""
                objet_display = objet[:50] + "..." if len(objet) > 50 else objet

                self.marches_tree.insert('', tk.END, values=(
                    n_marche, type_marche, client, objet_display, montant_display, statut, date_display
                ))

            # Mettre à jour les statistiques
            count = len(marches)
            total_montant = sum(m[4] for m in marches if m[4])
            filter_text = f" (Filtrés: {status_filter})" if status_filter != "Tous" else ""
            self.marches_stats_label.config(
                text=f"Total: {count} marchés{filter_text} | Montant total: {total_montant:.2f} DH"
            )

        except Exception as e:
            print(f"Erreur lors de la recherche: {e}")

    def refresh_marches(self):
        """Actualiser la liste des marchés"""
        self.marches_search_var.set("")
        self.load_marches_data()

    def sort_marches_column(self, column):
        """Trier les marchés par colonne"""
        try:
            # Toggle sort direction
            if not hasattr(self, 'marches_sort_reverse'):
                self.marches_sort_reverse = {}

            reverse = self.marches_sort_reverse.get(column, False)
            self.marches_sort_reverse[column] = not reverse

            # Get current data
            items = []
            for item in self.marches_tree.get_children():
                values = self.marches_tree.item(item, 'values')
                items.append(values)

            # Sort based on column
            if column == 'montant_ttc':
                # Sort by numeric value
                items.sort(key=lambda x: float(x[4].replace(' DH', '').replace(',', '')) if x[4] != '0.00 DH' else 0, reverse=reverse)
            elif column == 'date_creation':
                # Sort by date
                items.sort(key=lambda x: x[6] if x[6] else '', reverse=reverse)
            else:
                # Sort alphabetically
                col_index = ['n_marche', 'type_marche', 'client', 'objet', 'montant_ttc', 'statut', 'date_creation'].index(column)
                items.sort(key=lambda x: x[col_index].lower() if x[col_index] else '', reverse=reverse)

            # Clear and repopulate tree
            for item in self.marches_tree.get_children():
                self.marches_tree.delete(item)

            for values in items:
                self.marches_tree.insert('', tk.END, values=values)

            # Update header to show sort direction
            direction = "🔽" if reverse else "🔼"
            headers = {
                'n_marche': f'N° Marché {direction}',
                'type_marche': f'Type {direction}',
                'client': f'Client {direction}',
                'objet': f'Objet {direction}',
                'montant_ttc': f'Montant TTC {direction}',
                'statut': f'Statut {direction}',
                'date_creation': f'Date Création {direction}'
            }

            # Reset all headers
            for col in ['n_marche', 'type_marche', 'client', 'objet', 'montant_ttc', 'statut', 'date_creation']:
                if col == column:
                    self.marches_tree.heading(col, text=headers[col])
                else:
                    self.marches_tree.heading(col, text=headers[col].replace('🔼', '↕️').replace('🔽', '↕️'))

        except Exception as e:
            print(f"Error sorting MARCHÉS: {e}")

    def on_marche_selection(self, event):
        """Gestion de la sélection d'un marché"""
        selection = self.marches_tree.selection()
        if selection:
            item = self.marches_tree.item(selection[0])
            n_marche = item['values'][0]
            self.marches_selection_label.config(text=f"Sélectionné: {n_marche}")
        else:
            self.marches_selection_label.config(text="")

    def on_marche_double_click(self, event):
        """Gestion du double-clic sur un marché"""
        self.modifier_marche()

    def show_marche_context_menu(self, event):
        """Afficher le menu contextuel pour les marchés"""
        selection = self.marches_tree.selection()
        if selection:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="✏️ Modifier", command=self.modifier_marche)
            menu.add_command(label="👁️ Voir détails", command=self.voir_marche_details)
            menu.add_command(label="🗑️ Supprimer", command=self.supprimer_marche)
            menu.add_separator()
            menu.add_command(label="🖨️ Imprimer", command=self.imprimer_marche)
            menu.tk_popup(event.x_root, event.y_root)

    def voir_marche_details(self):
        """Voir les détails d'un marché"""
        messagebox.showinfo("Info", "Affichage des détails en cours de développement")

    def supprimer_marche(self):
        """Supprimer un marché"""
        selection = self.marches_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un marché à supprimer!")
            return

        item = self.marches_tree.item(selection[0])
        n_marche = item['values'][0]
        client = item['values'][2]

        if messagebox.askyesno("Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le marché?\n\n"
            f"N° Marché: {n_marche}\n"
            f"Client: {client}\n\n"
            f"Cette action supprimera également tous les détails et paiements associés."):

            try:
                # Supprimer les détails et paiements d'abord
                self.cursor.execute("SELECT id FROM marches WHERE n_marche=?", (n_marche,))
                result = self.cursor.fetchone()
                if result:
                    marche_id = result[0]
                    self.cursor.execute("DELETE FROM details_marches WHERE marche_id=?", (marche_id,))
                    self.cursor.execute("DELETE FROM paiements_marches WHERE marche_id=?", (marche_id,))

                # Supprimer le marché principal
                self.cursor.execute("DELETE FROM marches WHERE n_marche=?", (n_marche,))
                self.conn.commit()

                self.load_marches_data()
                messagebox.showinfo("Succès", f"Marché {n_marche} supprimé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

    def imprimer_marche(self):
        """Imprimer un marché"""
        messagebox.showinfo("Impression", "Fonctionnalité d'impression en cours de développement")

    def open_marche_form(self, marche_data=None):
        """Ouvrir le formulaire de marché - exactement comme dans le screenshot"""
        # Créer la fenêtre
        form_window = tk.Toplevel(self.root)
        form_window.title("MARCHÉS")

        # Taille adaptative basée sur l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = min(1200, int(screen_width * 0.9))
        window_height = min(900, int(screen_height * 0.9))

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        form_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Permettre le redimensionnement
        form_window.resizable(True, True)
        form_window.minsize(1000, 700)

        form_window.transient(self.root)
        form_window.grab_set()

        # Créer un canvas avec scrollbar pour le contenu scrollable
        canvas = tk.Canvas(form_window, highlightthickness=0, bg='white')
        scrollbar = ttk.Scrollbar(form_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configuration du scrolling
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)

        # Créer la fenêtre dans le canvas
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Placement du canvas et scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Configuration pour le redimensionnement
        scrollable_frame.columnconfigure(0, weight=1)

        # Ajuster la largeur du canvas au redimensionnement
        def on_canvas_configure(event):
            canvas.itemconfig(canvas_window, width=event.width)
        canvas.bind('<Configure>', on_canvas_configure)

        # Créer le formulaire exactement comme dans le screenshot
        self.create_marche_form_exact(scrollable_frame, marche_data, form_window)

        # Bind mouse wheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)



        # Focus sur le type de marché
        form_window.focus()

    def create_marche_form_exact(self, parent, marche_data, form_window):
        """Créer le formulaire MARCHÉS exactement comme dans le screenshot"""
        # Titre MARCHÉS
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(10, 10))
        title_frame.columnconfigure(1, weight=1)

        title_label = ttk.Label(title_frame, text="MARCHÉS", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)

        selection_label = ttk.Label(title_frame, text="sélectionné", style='Info.TLabel')
        selection_label.grid(row=0, column=1, sticky=tk.E)

        # Variables pour les champs - البيانات الفعلية من الصورة
        type_marche_var = tk.StringVar(value="sélectionné")  # من الصورة: sélectionné
        devis_n_var = tk.StringVar(value="SÉLECTIONNÉ OU TAPER")
        nature_var = tk.StringVar(value="AUTOMATIQUE")
        objet_var = tk.StringVar(value="AUTOMATIQUE")
        delai_var = tk.StringVar(value="3 mois")
        client_var = tk.StringVar(value="AUTOMATIQUE")
        montant_ttc_var = tk.StringVar(value="AUTOMATIQUE AU MANUEL")
        caution_prov_var = tk.StringVar(value="manuel")
        caution_def_pop_var = tk.StringVar(value="5%")
        caution_def_var = tk.StringVar(value="du montant de marché arrondi")
        ordre_service_var = tk.StringVar(value="paré")
        caution_retenue_var = tk.StringVar(value="7% si nature de prestation et travaux 0% si fourniture")
        delai_achevement_var = tk.StringVar(value="3 mois à compter de date ordre de service")

        # Tableau principal avec les champs
        main_frame = ttk.Frame(parent)
        main_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        main_frame.columnconfigure(1, weight=1)

        # Créer le tableau des champs comme dans le screenshot
        fields = [
            ("type de marché", type_marche_var),
            ("DEVIS N°", devis_n_var),
            ("nature de prestation", nature_var),
            ("objet", objet_var),
            ("délai d'exécution", delai_var),
            ("Client", client_var),
            ("Montant TTC", montant_ttc_var),
            ("caution provisoire", caution_prov_var),
            ("caution définitive population", caution_def_pop_var),
            ("caution définitive", caution_def_var),
            ("ordre de service", ordre_service_var),
            ("caution retenue de garantie", caution_retenue_var),
            ("délai prévu d'achèvement", delai_achevement_var)
        ]

        for i, (label_text, var) in enumerate(fields):
            # Label
            label = ttk.Label(main_frame, text=label_text)
            label.grid(row=i, column=0, sticky=tk.W, pady=2, padx=(0, 10))

            # Entry
            entry = ttk.Entry(main_frame, textvariable=var, width=50)
            entry.grid(row=i, column=1, sticky=(tk.W, tk.E), pady=2)

        # Section bordereau de prix
        self.create_bordereau_section_exact(parent, 2)

        # Section paiements
        self.create_paiements_section_exact(parent, 3)

        # Boutons
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=4, column=0, pady=20)

        ttk.Button(button_frame, text="Enregistrer",
                  command=lambda: self.save_marche_exact(form_window, type_marche_var, devis_n_var,
                                                        nature_var, objet_var, delai_var, client_var,
                                                        montant_ttc_var, caution_prov_var, caution_def_pop_var,
                                                        caution_def_var, ordre_service_var, caution_retenue_var,
                                                        delai_achevement_var, marche_data)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Annuler", command=form_window.destroy).pack(side=tk.LEFT, padx=5)

    def create_bordereau_section_exact(self, parent, row):
        """Créer la section bordereau de prix exactement comme dans le screenshot"""
        # Titre bordereau de prix
        bordereau_frame = ttk.LabelFrame(parent, text="bordereau de prix", padding="10")
        bordereau_frame.grid(row=row, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        bordereau_frame.columnconfigure(1, weight=1)
        bordereau_frame.columnconfigure(2, weight=1)
        bordereau_frame.columnconfigure(3, weight=1)
        bordereau_frame.columnconfigure(4, weight=1)
        bordereau_frame.columnconfigure(5, weight=1)
        bordereau_frame.columnconfigure(6, weight=1)
        bordereau_frame.columnconfigure(7, weight=1)
        bordereau_frame.columnconfigure(8, weight=1)

        # Headers
        headers = ["N°", "désignation", "U", "qte", "prix ACHAT HT", "TOTAL ACHAT HT",
                  "prix HT", "prix U HT", "MARGE"]

        for i, header in enumerate(headers):
            label = ttk.Label(bordereau_frame, text=header, style='Heading.TLabel')
            label.grid(row=0, column=i, sticky=(tk.W, tk.E), padx=2, pady=5)

        # البيانات الفعلية من الصورة
        data_rows = [
            ("1", "AUTOMATIQUE", "automatique", "AUTO", "AUTO / MANUEL", "ACHAT X QTE",
             "AUTO / MANUEL", "AUTO / MANUEL", "VENT - ACHAT"),
            ("2", "AUTOMATIQUE", "automatique", "AUTO", "AUTO / MANUEL", "ACHAT X QTE",
             "AUTO / MANUEL", "AUTO / MANUEL", "VENT - ACHAT")
        ]

        for row_idx, row_data in enumerate(data_rows, 1):
            for col_idx, cell_data in enumerate(row_data):
                entry = ttk.Entry(bordereau_frame, width=12)
                entry.insert(0, cell_data)
                entry.grid(row=row_idx, column=col_idx, sticky=(tk.W, tk.E), padx=2, pady=2)

        # Totaux
        total_frame = ttk.Frame(bordereau_frame)
        total_frame.grid(row=3, column=0, columnspan=9, sticky=(tk.W, tk.E), pady=10)
        total_frame.columnconfigure(1, weight=1)
        total_frame.columnconfigure(3, weight=1)
        total_frame.columnconfigure(5, weight=1)
        total_frame.columnconfigure(7, weight=1)

        # Labels et champs totaux
        ttk.Label(total_frame, text="TOTAL", style='Heading.TLabel').grid(row=0, column=0, padx=5)
        ttk.Entry(total_frame, width=15).grid(row=0, column=1, padx=5)

        ttk.Label(total_frame, text="TOTAL ACHAT HT", style='Heading.TLabel').grid(row=0, column=2, padx=5)
        ttk.Entry(total_frame, width=15).grid(row=0, column=3, padx=5)

        ttk.Label(total_frame, text="TOTAL VENTE HT", style='Heading.TLabel').grid(row=0, column=4, padx=5)
        ttk.Entry(total_frame, width=15).grid(row=0, column=5, padx=5)

        ttk.Label(total_frame, text="TOTAL MARGE HT", style='Heading.TLabel').grid(row=0, column=6, padx=5)
        ttk.Entry(total_frame, width=15).grid(row=0, column=7, padx=5)

        # Ligne TVA
        tva_frame = ttk.Frame(bordereau_frame)
        tva_frame.grid(row=4, column=0, columnspan=9, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(tva_frame, text="TVA", style='Heading.TLabel').grid(row=0, column=0, padx=5)
        ttk.Entry(tva_frame, width=15).grid(row=0, column=1, padx=5)
        ttk.Label(tva_frame, text="TVA 20%", style='Heading.TLabel').grid(row=0, column=2, padx=5)

        # Ligne TTC
        ttc_frame = ttk.Frame(bordereau_frame)
        ttc_frame.grid(row=5, column=0, columnspan=9, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(ttc_frame, text="TTC", style='Heading.TLabel').grid(row=0, column=0, padx=5)
        ttc_entry = ttk.Entry(ttc_frame, width=15)
        ttc_entry.grid(row=0, column=1, padx=5)

    def create_paiements_section_exact(self, parent, row):
        """Créer la section paiements exactement comme dans le screenshot"""
        # Section paiements
        paiements_frame = ttk.Frame(parent)
        paiements_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=10)
        paiements_frame.columnconfigure(1, weight=1)

        # البيانات الفعلية من الصورة - أرقام المدفوعات
        payment_modes = [
            ("TOTAL MARGE HT", ""),
            ("TRANCHE HT", "500 ( sélectionner mode de paiement )"),
            ("COUPON", "501 ( sélectionner mode de paiement ) si espace ajouter au caisse"),
            ("autres", "502 ( sélectionner mode de paiement )"),
            ("autres", "503 ( sélectionner mode de paiement )"),
            ("autres", "504 ( sélectionner mode de paiement )"),
            ("autres", "505 ( sélectionner mode de paiement )"),
            ("autres", "506 ( sélectionner mode de paiement )"),
            ("autres", "507 ( sélectionner mode de paiement )")
        ]

        for i, (label_text, entry_text) in enumerate(payment_modes):
            ttk.Label(paiements_frame, text=label_text).grid(row=i, column=0, sticky=tk.W, pady=2, padx=(0, 10))
            entry = ttk.Entry(paiements_frame, width=60)
            if entry_text:
                entry.insert(0, entry_text)
            entry.grid(row=i, column=1, sticky=(tk.W, tk.E), pady=2)

        # Reste bénéficiaire
        ttk.Label(paiements_frame, text="reste bénéficiaire").grid(row=len(payment_modes), column=0, sticky=tk.W, pady=10)

    def save_marche_exact(self, form_window, type_marche_var, devis_n_var, nature_var, objet_var,
                         delai_var, client_var, montant_ttc_var, caution_prov_var, caution_def_pop_var,
                         caution_def_var, ordre_service_var, caution_retenue_var, delai_achevement_var, marche_data):
        """Sauvegarder le marché"""
        try:
            if marche_data:
                # Modification
                self.cursor.execute("""
                    UPDATE marches SET type_marche=?, devis_n=?, nature_prestation=?, objet=?, delai_execution=?,
                    client=?, montant_ttc=?, caution_provisoire=?, caution_definitif_population=?,
                    caution_definitif=?, ordre_service=?, caution_retenue_garantie=?, delai_prevu_achevement=?
                    WHERE id=?
                """, (type_marche_var.get(), devis_n_var.get(), nature_var.get(), objet_var.get(),
                         delai_var.get(), client_var.get(), montant_ttc_var.get(), caution_prov_var.get(),
                         caution_def_pop_var.get(), caution_def_var.get(), ordre_service_var.get(),
                         caution_retenue_var.get(), delai_achevement_var.get(), marche_data[0]))
            else:
                # Nouveau marché - générer n_marche
                n_marche = self.get_next_marche_number()
                self.cursor.execute("""
                    INSERT INTO marches (n_marche, type_marche, devis_n, nature_prestation, objet, delai_execution, client,
                    montant_ttc, caution_provisoire, caution_definitif_population, caution_definitif,
                    ordre_service, caution_retenue_garantie, delai_prevu_achevement, date_creation)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (n_marche, type_marche_var.get(), devis_n_var.get(), nature_var.get(), objet_var.get(),
                     delai_var.get(), client_var.get(), montant_ttc_var.get(), caution_prov_var.get(),
                     caution_def_pop_var.get(), caution_def_var.get(), ordre_service_var.get(),
                     caution_retenue_var.get(), delai_achevement_var.get(), datetime.now().strftime('%Y-%m-%d')))

            self.conn.commit()
            messagebox.showinfo("Succès", "Marché enregistré avec succès!")
            form_window.destroy()
            self.load_marches_data()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def get_next_marche_number(self):
        """Générer le prochain numéro de marché"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM marches")
            count = self.cursor.fetchone()[0]
            return f"M{count + 1:04d}"
        except:
            return "M0001"

    def create_marche_general_section(self, parent, row, n_marche_var, type_marche_var,
                                    devis_n_var, nature_var, objet_var, delai_var, client_var, montant_var):
        """Créer la section informations générales du marché"""
        general_frame = ttk.LabelFrame(parent, text="📄 Informations Générales", padding="8")
        general_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        general_frame.columnconfigure(1, weight=1)
        general_frame.columnconfigure(3, weight=1)

        # Type de marché et DEVIS N°
        ttk.Label(general_frame, text="Type de marché:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)

        # Type selection with add button
        type_selection_frame = ttk.Frame(general_frame)
        type_selection_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))
        type_selection_frame.columnconfigure(0, weight=1)

        type_combo = ttk.Combobox(type_selection_frame, textvariable=type_marche_var,
                                 values=["BC", "MARCHÉ"], state="readonly", width=10)
        type_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add type button
        def add_type_marche_and_refresh():
            self.add_new_type_option(type_combo, type_marche_var)

        add_type_marche_btn = ttk.Button(type_selection_frame, text="➕", width=3,
                                        command=add_type_marche_and_refresh)
        add_type_marche_btn.grid(row=0, column=1)

        ttk.Label(general_frame, text="DEVIS N°:", style='Heading.TLabel').grid(row=0, column=2, sticky=tk.W, pady=5)
        devis_frame = ttk.Frame(general_frame)
        devis_frame.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        devis_frame.columnconfigure(0, weight=1)

        devis_entry = ttk.Entry(devis_frame, textvariable=devis_n_var, width=20)
        devis_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        devis_select_btn = ttk.Button(devis_frame, text="📋", width=3,
                                     command=lambda: self.select_devis_for_marche(devis_n_var, client_var, nature_var, objet_var, montant_var))
        devis_select_btn.grid(row=0, column=1)

        # Nature de prestation
        ttk.Label(general_frame, text="Nature de prestation:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)

        # Nature selection with add button
        nature_marche_selection_frame = ttk.Frame(general_frame)
        nature_marche_selection_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))
        nature_marche_selection_frame.columnconfigure(0, weight=1)

        nature_combo = ttk.Combobox(nature_marche_selection_frame, textvariable=nature_var,
                                   values=["Travaux", "Fourniture", "Services", "Maintenance", "Études"], width=15)
        nature_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add nature button
        def add_nature_marche_and_refresh():
            self.add_new_nature_option(nature_combo, nature_var)

        add_nature_marche_btn = ttk.Button(nature_marche_selection_frame, text="➕", width=3,
                                          command=add_nature_marche_and_refresh)
        add_nature_marche_btn.grid(row=0, column=1)

        # Objet
        ttk.Label(general_frame, text="Objet:", style='Heading.TLabel').grid(row=1, column=2, sticky=tk.W, pady=5)
        objet_entry = ttk.Entry(general_frame, textvariable=objet_var, width=30)
        objet_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Délai d'exécution
        ttk.Label(general_frame, text="Délai d'exécution:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W, pady=5)
        delai_entry = ttk.Entry(general_frame, textvariable=delai_var, width=20)
        delai_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        # Client
        ttk.Label(general_frame, text="Client:", style='Heading.TLabel').grid(row=2, column=2, sticky=tk.W, pady=5)
        client_frame = ttk.Frame(general_frame)
        client_frame.grid(row=2, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        client_frame.columnconfigure(0, weight=1)

        # Récupérer la liste des clients
        try:
            self.cursor.execute("SELECT code, nom FROM clients ORDER BY nom")
            clients_list = [f"{code} - {nom}" for code, nom in self.cursor.fetchall()]
        except Exception as e:
            print(f"Error loading clients: {e}")
            clients_list = []

        client_combo = ttk.Combobox(client_frame, textvariable=client_var,
                                   values=["Sélectionner un client..."] + clients_list, width=25)
        client_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add client button with refresh
        def add_client_and_refresh_marche():
            self.open_client_form()
            # Refresh client list after potential addition
            self.root.after(100, lambda: self.refresh_client_dropdown(client_combo, client_var))

        new_client_btn = ttk.Button(client_frame, text="➕", width=3,
                                   command=add_client_and_refresh_marche)
        new_client_btn.grid(row=0, column=1)

    def create_marche_montants_section(self, parent, row, montant_var, caution_prov_var,
                                     caution_def_pop_var, caution_def_var, ordre_service_var,
                                     caution_retenue_var, delai_achevement_var):
        """Créer la section montants et cautions du marché"""
        montants_frame = ttk.LabelFrame(parent, text="💰 Montants et Cautions", padding="8")
        montants_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        montants_frame.columnconfigure(1, weight=1)
        montants_frame.columnconfigure(3, weight=1)

        # Montant TTC
        ttk.Label(montants_frame, text="Montant TTC:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)
        montant_entry = ttk.Entry(montants_frame, textvariable=montant_var, width=25)
        montant_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        # Caution provisoire
        ttk.Label(montants_frame, text="Caution provisoire:", style='Heading.TLabel').grid(row=0, column=2, sticky=tk.W, pady=5)
        caution_prov_entry = ttk.Entry(montants_frame, textvariable=caution_prov_var, width=25)
        caution_prov_entry.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Caution définitif population
        ttk.Label(montants_frame, text="Caution définitif population:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)
        caution_def_pop_entry = ttk.Entry(montants_frame, textvariable=caution_def_pop_var, width=25)
        caution_def_pop_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        # Caution définitif
        ttk.Label(montants_frame, text="Caution définitif:", style='Heading.TLabel').grid(row=1, column=2, sticky=tk.W, pady=5)
        caution_def_entry = ttk.Entry(montants_frame, textvariable=caution_def_var, width=25)
        caution_def_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Ordre de service
        ttk.Label(montants_frame, text="Ordre de service:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W, pady=5)
        ordre_service_entry = ttk.Entry(montants_frame, textvariable=ordre_service_var, width=25)
        ordre_service_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        # Caution retenue de garantie
        ttk.Label(montants_frame, text="Caution retenue de garantie:", style='Heading.TLabel').grid(row=2, column=2, sticky=tk.W, pady=5)
        caution_retenue_entry = ttk.Entry(montants_frame, textvariable=caution_retenue_var, width=25)
        caution_retenue_entry.grid(row=2, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Délai prévu d'achèvement
        ttk.Label(montants_frame, text="Délai prévu d'achèvement:", style='Heading.TLabel').grid(row=3, column=0, sticky=tk.W, pady=5)
        delai_achevement_entry = ttk.Entry(montants_frame, textvariable=delai_achevement_var, width=50)
        delai_achevement_entry.grid(row=3, column=1, columnspan=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

    def create_marche_bordereau_section(self, parent, row, marche_data, devis_n_var, client_var, nature_var, objet_var, montant_var):
        """Créer la section bordereau de prix du marché"""
        bordereau_frame = ttk.LabelFrame(parent, text="📋 Bordereau de Prix", padding="8")
        bordereau_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 8))
        bordereau_frame.columnconfigure(0, weight=1)
        bordereau_frame.rowconfigure(1, weight=1)

        # Instructions
        instruction_label = ttk.Label(bordereau_frame,
                                    text="💡 Double-cliquez sur 'Désignation' pour sélectionner un produit",
                                    style='Info.TLabel')
        instruction_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Tableau des détails
        details_table_frame = ttk.Frame(bordereau_frame)
        details_table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        details_table_frame.columnconfigure(0, weight=1)
        details_table_frame.rowconfigure(0, weight=1)

        # Treeview pour les détails
        details_columns = ('n', 'designation', 'u', 'qte', 'prix_achat_ht', 'total_achat_ht', 'prix_ht', 'prix_ttc', 'marge')
        self.marche_details_tree = ttk.Treeview(details_table_frame, columns=details_columns, show='headings', height=8)

        # En-têtes
        self.marche_details_tree.heading('n', text='N°')
        self.marche_details_tree.heading('designation', text='Désignation')
        self.marche_details_tree.heading('u', text='U')
        self.marche_details_tree.heading('qte', text='Qté')
        self.marche_details_tree.heading('prix_achat_ht', text='Prix Achat HT')
        self.marche_details_tree.heading('total_achat_ht', text='Total Achat HT')
        self.marche_details_tree.heading('prix_ht', text='Prix HT')
        self.marche_details_tree.heading('prix_ttc', text='Prix TTC')
        self.marche_details_tree.heading('marge', text='Marge')

        # Largeurs des colonnes
        self.marche_details_tree.column('n', width=40, minwidth=30)
        self.marche_details_tree.column('designation', width=200, minwidth=150)
        self.marche_details_tree.column('u', width=60, minwidth=50)
        self.marche_details_tree.column('qte', width=60, minwidth=50)
        self.marche_details_tree.column('prix_achat_ht', width=100, minwidth=80)
        self.marche_details_tree.column('total_achat_ht', width=100, minwidth=80)
        self.marche_details_tree.column('prix_ht', width=80, minwidth=70)
        self.marche_details_tree.column('prix_ttc', width=80, minwidth=70)
        self.marche_details_tree.column('marge', width=80, minwidth=70)

        self.marche_details_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbars pour le tableau des détails
        details_v_scrollbar = ttk.Scrollbar(details_table_frame, orient=tk.VERTICAL, command=self.marche_details_tree.yview)
        details_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.marche_details_tree.configure(yscrollcommand=details_v_scrollbar.set)

        details_h_scrollbar = ttk.Scrollbar(details_table_frame, orient=tk.HORIZONTAL, command=self.marche_details_tree.xview)
        details_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.marche_details_tree.configure(xscrollcommand=details_h_scrollbar.set)

        # Boutons pour gérer les détails
        details_buttons_frame = ttk.Frame(bordereau_frame)
        details_buttons_frame.grid(row=2, column=0, pady=(15, 0))

        ttk.Button(details_buttons_frame, text="➕ Ajouter Ligne",
                  command=lambda: self.add_marche_detail_row()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="✏️ Modifier Ligne",
                  command=lambda: self.edit_marche_detail_row()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="🗑️ Supprimer Ligne",
                  command=lambda: self.delete_marche_detail_row()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="📋 Importer du DEVIS",
                  command=lambda: self.import_marche_from_devis()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(details_buttons_frame, text="🔄 Auto-Remplir",
                  command=lambda: self.trigger_auto_populate_marche(devis_n_var, client_var, nature_var, objet_var, montant_var)).pack(side=tk.LEFT)

        # Bind double-click for product selection and cell editing
        self.marche_details_tree.bind('<Double-Button-1>', self.on_marche_cell_double_click)

        # Setup cell editing for MARCHÉS
        self.setup_marche_cell_editing()

        # Totaux
        totaux_frame = ttk.Frame(bordereau_frame)
        totaux_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        totaux_frame.columnconfigure(1, weight=1)

        self.marche_totaux_label = ttk.Label(totaux_frame, text="TOTAL: 0.00 DH HT | TVA 20%: 0.00 DH | TTC: 0.00 DH",
                                           style='Info.TLabel')
        self.marche_totaux_label.grid(row=0, column=0, sticky=tk.W)

        # Ajouter une ligne d'exemple
        self.add_sample_marche_row()

    def create_marche_paiements_section(self, parent, row, marche_data):
        """Créer la section paiements du marché"""
        paiements_frame = ttk.LabelFrame(parent, text="💳 Paiements", padding="8")
        paiements_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        paiements_frame.columnconfigure(0, weight=1)

        # Tableau des paiements
        paiements_table_frame = ttk.Frame(paiements_frame)
        paiements_table_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        paiements_table_frame.columnconfigure(0, weight=1)

        # Treeview pour les paiements
        paiements_columns = ('mode_paiement', 'montant', 'date_paiement', 'reference', 'statut')
        self.marche_paiements_tree = ttk.Treeview(paiements_table_frame, columns=paiements_columns, show='headings', height=6)

        # En-têtes
        self.marche_paiements_tree.heading('mode_paiement', text='Mode de Paiement')
        self.marche_paiements_tree.heading('montant', text='Montant')
        self.marche_paiements_tree.heading('date_paiement', text='Date Paiement')
        self.marche_paiements_tree.heading('reference', text='Référence')
        self.marche_paiements_tree.heading('statut', text='Statut')

        # Largeurs des colonnes
        self.marche_paiements_tree.column('mode_paiement', width=200, minwidth=150)
        self.marche_paiements_tree.column('montant', width=100, minwidth=80)
        self.marche_paiements_tree.column('date_paiement', width=120, minwidth=100)
        self.marche_paiements_tree.column('reference', width=150, minwidth=120)
        self.marche_paiements_tree.column('statut', width=100, minwidth=80)

        self.marche_paiements_tree.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Scrollbar pour les paiements
        paiements_scrollbar = ttk.Scrollbar(paiements_table_frame, orient=tk.VERTICAL, command=self.marche_paiements_tree.yview)
        paiements_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.marche_paiements_tree.configure(yscrollcommand=paiements_scrollbar.set)

        # Boutons pour gérer les paiements
        paiements_buttons_frame = ttk.Frame(paiements_frame)
        paiements_buttons_frame.grid(row=1, column=0, pady=(15, 0))

        ttk.Button(paiements_buttons_frame, text="➕ Ajouter Paiement",
                  command=lambda: self.add_marche_paiement()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(paiements_buttons_frame, text="✏️ Modifier Paiement",
                  command=lambda: self.edit_marche_paiement()).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(paiements_buttons_frame, text="🗑️ Supprimer Paiement",
                  command=lambda: self.delete_marche_paiement()).pack(side=tk.LEFT)

        # Ajouter des paiements d'exemple
        self.add_sample_marche_paiements()

    def create_marche_buttons_section(self, parent, row, form_window, marche_data, *vars):
        """Créer la section boutons du marché"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=row, column=0, columnspan=2, pady=15)

        def save_marche():
            # Validation
            n_marche_var, type_marche_var, devis_n_var, nature_var, objet_var, delai_var, client_var, montant_var, caution_prov_var, caution_def_pop_var, caution_def_var, ordre_service_var, caution_retenue_var, delai_achevement_var, statut_var = vars

            if not client_var.get().strip() or client_var.get() == "Sélectionner un client...":
                messagebox.showerror("Erreur", "Veuillez sélectionner un client!")
                return

            # Sauvegarder
            try:
                marche_id = None

                if marche_data:  # Modification
                    self.cursor.execute("""
                        UPDATE marches SET type_marche=?, devis_n=?, nature_prestation=?, objet=?,
                        delai_execution=?, client=?, montant_ttc=?, caution_provisoire=?,
                        caution_definitif_population=?, caution_definitif=?, ordre_service=?,
                        caution_retenue_garantie=?, delai_prevu_achevement=?, statut=?
                        WHERE n_marche=?
                    """, (type_marche_var.get(), devis_n_var.get(), nature_var.get(), objet_var.get(),
                          delai_var.get(), client_var.get(), float(montant_var.get().replace('AUTOMATIQUE AU MANUEL', '0')),
                          caution_prov_var.get(), caution_def_pop_var.get(), caution_def_var.get(),
                          ordre_service_var.get(), caution_retenue_var.get(), delai_achevement_var.get(),
                          statut_var.get(), n_marche_var.get()))

                    self.cursor.execute("SELECT id FROM marches WHERE n_marche=?", (n_marche_var.get(),))
                    marche_id = self.cursor.fetchone()[0]

                    # Delete existing details and payments
                    self.cursor.execute("DELETE FROM details_marches WHERE marche_id=?", (marche_id,))
                    self.cursor.execute("DELETE FROM paiements_marches WHERE marche_id=?", (marche_id,))

                else:  # Nouveau
                    self.cursor.execute("""
                        INSERT INTO marches (n_marche, type_marche, devis_n, nature_prestation, objet,
                        delai_execution, client, montant_ttc, caution_provisoire, caution_definitif_population,
                        caution_definitif, ordre_service, caution_retenue_garantie, delai_prevu_achevement, statut)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (n_marche_var.get(), type_marche_var.get(), devis_n_var.get(), nature_var.get(),
                          objet_var.get(), delai_var.get(), client_var.get(),
                          float(montant_var.get()) if montant_var.get() and montant_var.get().replace('.', '').replace(',', '').isdigit() else 0.0,
                          caution_prov_var.get(), caution_def_pop_var.get(), caution_def_var.get(),
                          ordre_service_var.get(), caution_retenue_var.get(), delai_achevement_var.get(),
                          statut_var.get()))

                    marche_id = self.cursor.lastrowid

                # Save details and payments
                self.save_marche_details(marche_id)
                self.save_marche_paiements(marche_id)

                self.conn.commit()
                self.load_marches_data()
                form_window.destroy()
                messagebox.showinfo("Succès", "Marché sauvegardé avec succès!")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        def cancel():
            form_window.destroy()

        def delete_marche():
            if marche_data and messagebox.askyesno("Confirmation",
                "Êtes-vous sûr de vouloir supprimer ce marché?\nCette action est irréversible."):
                try:
                    n_marche_var = vars[0]
                    self.cursor.execute("SELECT id FROM marches WHERE n_marche=?", (n_marche_var.get(),))
                    result = self.cursor.fetchone()
                    if result:
                        marche_id = result[0]
                        self.cursor.execute("DELETE FROM details_marches WHERE marche_id=?", (marche_id,))
                        self.cursor.execute("DELETE FROM paiements_marches WHERE marche_id=?", (marche_id,))

                    self.cursor.execute("DELETE FROM marches WHERE n_marche=?", (n_marche_var.get(),))
                    self.conn.commit()
                    self.load_marches_data()
                    form_window.destroy()
                    messagebox.showinfo("Succès", "Marché supprimé avec succès!")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

        def print_marche():
            messagebox.showinfo("Impression", "Fonctionnalité d'impression en cours de développement")

        # Boutons avec style amélioré
        ttk.Button(buttons_frame, text="✓ Valider", command=save_marche,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))

        if marche_data:  # Show delete button only when editing
            ttk.Button(buttons_frame, text="🗑️ Supprimer", command=delete_marche,
                      style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="🖨️ Imprimer", command=print_marche).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="❌ Annuler", command=cancel).pack(side=tk.LEFT)

    # Supporting methods for MARCHÉS functionality
    def add_sample_marche_row(self):
        """Add a sample row to the marché details table"""
        self.marche_details_tree.insert('', tk.END, values=(
            '1', 'Cliquez pour sélectionner un produit', 'unité', '1', '0.00', '0.00', '0.00', '0.00', '0.00'
        ))

    def add_sample_marche_paiements(self):
        """Add sample payments to the marché payments table"""
        sample_payments = [
            ('500 ( sélectionner mode de paiement )', '', '', '', ''),
            ('501 ( sélectionner mode de paiement ) si espace ajouter au caisse', '', '', '', ''),
            ('502 ( sélectionner mode de paiement )', '', '', '', ''),
            ('503 ( sélectionner mode de paiement )', '', '', '', ''),
            ('504 ( sélectionner mode de paiement )', '', '', '', ''),
            ('505 ( sélectionner mode de paiement )', '', '', '', ''),
            ('506 ( sélectionner mode de paiement )', '', '', '', ''),
            ('507 ( sélectionner mode de paiement )', '', '', '', '')
        ]

        for payment in sample_payments:
            self.marche_paiements_tree.insert('', tk.END, values=payment)

    def add_marche_detail_row(self):
        """Add a new detail row to marché"""
        children = self.marche_details_tree.get_children()
        next_num = len(children) + 1

        self.marche_details_tree.insert('', tk.END, values=(
            str(next_num), 'Cliquez pour sélectionner un produit', 'unité', '1', '0.00', '0.00', '0.00', '0.00', '0.00'
        ))

    def edit_marche_detail_row(self):
        """Edit selected marché detail row"""
        selection = self.marche_details_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à modifier!")
            return

        messagebox.showinfo("Info", "Modification de ligne en cours de développement")

    def delete_marche_detail_row(self):
        """Delete selected marché detail row"""
        selection = self.marche_details_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à supprimer!")
            return

        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer cette ligne?"):
            self.marche_details_tree.delete(selection[0])
            self.renumber_marche_detail_rows()

    def renumber_marche_detail_rows(self):
        """Renumber all marché detail rows"""
        children = self.marche_details_tree.get_children()
        for i, child in enumerate(children, 1):
            values = list(self.marche_details_tree.item(child, 'values'))
            values[0] = str(i)
            self.marche_details_tree.item(child, values=values)

    def on_marche_designation_double_click(self, event):
        """Handle double-click on designation column in marché"""
        item = self.marche_details_tree.identify_row(event.y)
        column = self.marche_details_tree.identify_column(event.x)

        if item and column == '#2':  # Designation column
            messagebox.showinfo("Info", "Sélection de produit en cours de développement")

    def import_marche_from_devis(self):
        """Import details from a DEVIS to marché"""
        messagebox.showinfo("Import DEVIS", "Fonctionnalité d'import depuis DEVIS en cours de développement")

    def add_marche_paiement(self):
        """Add a new payment to marché"""
        self.marche_paiements_tree.insert('', tk.END, values=(
            'sélectionner mode de paiement', '', '', '', 'En attente'
        ))

    def edit_marche_paiement(self):
        """Edit selected marché payment"""
        selection = self.marche_paiements_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un paiement à modifier!")
            return

        messagebox.showinfo("Info", "Modification de paiement en cours de développement")

    def delete_marche_paiement(self):
        """Delete selected marché payment"""
        selection = self.marche_paiements_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un paiement à supprimer!")
            return

        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer ce paiement?"):
            self.marche_paiements_tree.delete(selection[0])

    def save_marche_details(self, marche_id):
        """Save marché details to database"""
        try:
            children = self.marche_details_tree.get_children()

            for i, child in enumerate(children, 1):
                values = self.marche_details_tree.item(child, 'values')

                if len(values) >= 9:
                    n_ligne = i
                    designation = values[1] if values[1] else 'AUTOMATIQUE'
                    u = values[2] if values[2] else 'automatique'
                    qte = float(values[3]) if values[3] and values[3] != 'AUTO' else 0.0
                    prix_achat_ht = float(values[4].replace('AUTO / MANUEL', '0')) if values[4] and values[4] != 'AUTO / MANUEL' else 0.0
                    total_achat_ht = float(values[5].replace('ACHAT X QTE', '0')) if values[5] and values[5] != 'ACHAT X QTE' else 0.0
                    prix_ht = float(values[6].replace('AUTO / MANUEL', '0')) if values[6] and values[6] != 'AUTO / MANUEL' else 0.0
                    prix_ttc = float(values[7].replace('AUTO / MANUEL', '0')) if values[7] and values[7] != 'AUTO / MANUEL' else 0.0
                    marge = float(values[8].replace('VENT - ACHAT', '0')) if values[8] and values[8] != 'VENT - ACHAT' else 0.0

                    self.cursor.execute("""
                        INSERT INTO details_marches (marche_id, n_ligne, designation, u, qte,
                        prix_achat_ht, total_achat_ht, prix_ht, prix_ttc, marge)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (marche_id, n_ligne, designation, u, qte, prix_achat_ht, total_achat_ht, prix_ht, prix_ttc, marge))

        except Exception as e:
            print(f"Error saving marché details: {e}")
            raise e

    def save_marche_paiements(self, marche_id):
        """Save marché payments to database"""
        try:
            children = self.marche_paiements_tree.get_children()

            for child in children:
                values = self.marche_paiements_tree.item(child, 'values')

                if len(values) >= 5 and values[0]:  # Only save non-empty payments
                    mode_paiement = values[0] if values[0] else 'sélectionner mode de paiement'
                    montant = float(values[1]) if values[1] and values[1] != '' else 0.0
                    date_paiement = values[2] if values[2] else None
                    reference = values[3] if values[3] else ''
                    statut = values[4] if values[4] else 'En attente'

                    if montant > 0 or mode_paiement != 'sélectionner mode de paiement':
                        self.cursor.execute("""
                            INSERT INTO paiements_marches (marche_id, mode_paiement, montant, date_paiement, reference, statut)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (marche_id, mode_paiement, montant, date_paiement, reference, statut))

        except Exception as e:
            print(f"Error saving marché payments: {e}")
            raise e

    def select_devis_for_marche(self, devis_var, client_var, nature_var, objet_var, montant_var):
        """Select a DEVIS to populate marché fields with automatic population"""
        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("📄 Sélectionner un DEVIS pour Auto-Population MARCHÉS")
        dialog.geometry("900x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (900 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"900x600+{x}+{y}")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🏛️ Sélectionner un DEVIS pour Auto-Population MARCHÉS",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 15))

        # Search frame
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(search_frame, text="🔍 Rechercher:", style='Heading.TLabel').pack(side=tk.LEFT, padx=(0, 10))
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=40)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # DEVIS table
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Columns for DEVIS selection
        columns = ('n_devis', 'client', 'nature_prestation', 'objet', 'montant_ttc', 'date_creation')
        devis_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configure columns
        devis_tree.heading('n_devis', text='N° DEVIS')
        devis_tree.heading('client', text='Client')
        devis_tree.heading('nature_prestation', text='Nature')
        devis_tree.heading('objet', text='Objet')
        devis_tree.heading('montant_ttc', text='Montant TTC')
        devis_tree.heading('date_creation', text='Date')

        devis_tree.column('n_devis', width=100)
        devis_tree.column('client', width=150)
        devis_tree.column('nature_prestation', width=120)
        devis_tree.column('objet', width=200)
        devis_tree.column('montant_ttc', width=100)
        devis_tree.column('date_creation', width=100)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=devis_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=devis_tree.xview)
        devis_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack table and scrollbars
        devis_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Load DEVIS data
        def load_devis_list(search_term=""):
            try:
                # Clear existing items
                for item in devis_tree.get_children():
                    devis_tree.delete(item)

                # Query DEVIS with search filter
                if search_term:
                    self.cursor.execute("""
                        SELECT n_devis, client, nature_prestation, objet,
                               (SELECT SUM(prix_total_ht * 1.2) FROM details_devis WHERE devis_id = devis.id) as montant_ttc,
                               date_creation
                        FROM devis
                        WHERE LOWER(n_devis) LIKE ? OR LOWER(client) LIKE ? OR LOWER(objet) LIKE ?
                        ORDER BY date_creation DESC
                    """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
                else:
                    self.cursor.execute("""
                        SELECT n_devis, client, nature_prestation, objet,
                               (SELECT SUM(prix_total_ht * 1.2) FROM details_devis WHERE devis_id = devis.id) as montant_ttc,
                               date_creation
                        FROM devis
                        ORDER BY date_creation DESC
                    """)

                devis_list = self.cursor.fetchall()

                for devis in devis_list:
                    n_devis, client, nature, objet, montant_ttc, date_creation = devis

                    # Format data for display
                    montant_display = f"{montant_ttc:.2f} DH" if montant_ttc else "0.00 DH"
                    date_display = date_creation.split()[0] if date_creation else ""
                    objet_display = objet[:40] + "..." if len(objet) > 40 else objet
                    nature_display = nature[:15] + "..." if len(nature) > 15 else nature

                    devis_tree.insert('', tk.END, values=(
                        n_devis, client, nature_display, objet_display, montant_display, date_display
                    ))

            except Exception as e:
                print(f"Error loading DEVIS: {e}")

        # Initial load
        load_devis_list()

        # Search functionality
        def filter_devis(*args):
            search_term = search_var.get().lower()
            load_devis_list(search_term)

        search_var.trace('w', filter_devis)

        # Auto-population options
        options_frame = ttk.LabelFrame(main_frame, text="Options d'Auto-Population", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 15))

        populate_details_var = tk.BooleanVar(value=True)
        populate_client_var = tk.BooleanVar(value=True)
        populate_montant_var = tk.BooleanVar(value=True)

        ttk.Checkbutton(options_frame, text="📦 Remplir le bordereau de prix",
                       variable=populate_details_var).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(options_frame, text="👤 Remplir les informations client",
                       variable=populate_client_var).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(options_frame, text="💰 Remplir le montant TTC",
                       variable=populate_montant_var).pack(side=tk.LEFT)

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        def select_devis():
            selection = devis_tree.selection()
            if selection:
                item_data = devis_tree.item(selection[0], 'values')
                n_devis = item_data[0]

                # Perform automatic population
                success = self.auto_populate_marche_from_devis(
                    n_devis, devis_var, client_var, nature_var, objet_var, montant_var,
                    populate_details_var.get(), populate_client_var.get(), populate_montant_var.get()
                )

                if success:
                    dialog.destroy()
                    messagebox.showinfo("Succès",
                        f"✅ Auto-population réussie!\n\n"
                        f"DEVIS {n_devis} sélectionné et tous les champs automatiques ont été remplis.")
            else:
                messagebox.showwarning("Sélection", "Veuillez sélectionner un DEVIS!")

        ttk.Button(buttons_frame, text="✓ Sélectionner et Auto-Remplir",
                  command=select_devis, style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="❌ Annuler",
                  command=dialog.destroy).pack(side=tk.RIGHT)

        # Bind double-click
        devis_tree.bind('<Double-Button-1>', lambda e: select_devis())

        # Focus on search
        search_entry.focus()

    def auto_populate_marche_from_devis(self, n_devis, devis_var, client_var, nature_var, objet_var, montant_var,
                                       populate_details=True, populate_client=True, populate_montant=True):
        """Automatically populate MARCHÉS fields from selected DEVIS with status updates"""
        try:
            print(f"🚀 Starting MARCHÉS auto-population from DEVIS {n_devis}")

            # Update status
            if hasattr(self, 'marche_auto_status_label'):
                self.marche_auto_status_label.config(text="🔄 Auto-population en cours...")
                self.root.update()

            # Get DEVIS main data
            self.cursor.execute("""
                SELECT n_devis, nature_prestation, client, adresse, ice, objet, id,
                       (SELECT SUM(prix_total_ht * 1.2) FROM details_devis WHERE devis_id = devis.id) as montant_ttc
                FROM devis
                WHERE n_devis = ?
            """, (n_devis,))

            devis_data = self.cursor.fetchone()
            if not devis_data:
                messagebox.showerror("Erreur", f"DEVIS {n_devis} introuvable!")
                return False

            n_devis_db, nature_prestation, client, adresse, ice, objet, devis_id, montant_ttc = devis_data
            print(f"📄 Found DEVIS: {n_devis_db} - Client: {client}")

            # 1. Populate DEVIS number
            devis_var.set(n_devis_db)
            print(f"📄 DEVIS number set: {n_devis_db}")

            # 2. Populate client information if requested
            if populate_client and client:
                if hasattr(self, 'marche_auto_status_label'):
                    self.marche_auto_status_label.config(text="👤 Remplissage client...")
                    self.root.update()
                if not client_var.get() or client_var.get() in ["automatique", "AUTO", "AUTOMATIQUE", ""]:
                    client_var.set(client)
                    print(f"👤 Client set: {client}")

            # 3. Populate nature de prestation if requested
            if nature_prestation and (not nature_var.get() or nature_var.get().strip() == ""):
                if hasattr(self, 'marche_auto_status_label'):
                    self.marche_auto_status_label.config(text="🏷️ Remplissage nature...")
                    self.root.update()
                nature_var.set(nature_prestation)
                print(f"🏷️ Nature set: {nature_prestation}")

            # 4. Populate objet if requested
            if objet and (not objet_var.get() or objet_var.get().strip() == ""):
                if hasattr(self, 'marche_auto_status_label'):
                    self.marche_auto_status_label.config(text="📝 Remplissage objet...")
                    self.root.update()
                objet_var.set(objet)
                print(f"📝 Objet set: {objet}")

            # 5. Populate montant if requested and available
            if populate_montant and montant_ttc and montant_var:
                if hasattr(self, 'marche_auto_status_label'):
                    self.marche_auto_status_label.config(text="💰 Remplissage montant...")
                    self.root.update()
                if not montant_var.get() or montant_var.get().strip() == "":
                    montant_var.set(f"{montant_ttc:.2f}")
                    print(f"💰 Montant set: {montant_ttc:.2f} DH")

            # 6. Populate bordereau de prix details if requested
            if populate_details:
                if hasattr(self, 'marche_auto_status_label'):
                    self.marche_auto_status_label.config(text="📦 Remplissage détails...")
                    self.root.update()
                self.auto_populate_marche_details_from_devis(devis_id)

            # Final status
            if hasattr(self, 'marche_auto_status_label'):
                self.marche_auto_status_label.config(text="✅ Auto-population terminée")
                # Clear status after 3 seconds
                self.root.after(3000, lambda: self.marche_auto_status_label.config(text=""))

            print(f"✅ MARCHÉS auto-population completed successfully")
            return True

        except Exception as e:
            print(f"❌ Error in MARCHÉS auto-population: {e}")
            if hasattr(self, 'marche_auto_status_label'):
                self.marche_auto_status_label.config(text="❌ Erreur auto-population")
            messagebox.showerror("Erreur", f"Erreur lors de l'auto-population: {str(e)}")
            return False

    def auto_populate_marche_details_from_devis(self, devis_id):
        """Auto-populate MARCHÉS bordereau de prix from DEVIS details"""
        try:
            print(f"📦 Populating MARCHÉS details from DEVIS ID: {devis_id}")

            # Get DEVIS details
            self.cursor.execute("""
                SELECT n_ligne, designation, u, qte, prix_achat_ht, marge, prix_ht, prix_total_ht
                FROM details_devis
                WHERE devis_id = ?
                ORDER BY n_ligne
            """, (devis_id,))

            devis_details = self.cursor.fetchall()
            print(f"📋 Found {len(devis_details)} detail lines")

            if not devis_details:
                print("⚠️ No details found in DEVIS")
                return

            # Clear existing automatic rows in bordereau de prix if tree exists
            if hasattr(self, 'marche_details_tree'):
                self.clear_automatic_marche_details()

                # Add DEVIS details to MARCHÉS bordereau de prix
                for detail in devis_details:
                    n_ligne, designation, u, qte, prix_achat, marge, prix_ht, prix_total = detail

                    # Format values
                    qte_display = f"{qte:.0f}" if qte == int(qte) else f"{qte:.2f}"
                    u_display = u if u else "unité"
                    prix_achat_display = f"{prix_achat:.2f}" if prix_achat else "0.00"
                    total_achat_display = f"{prix_achat * qte:.2f}" if prix_achat else "0.00"
                    prix_ht_display = f"{prix_ht:.2f}" if prix_ht else "0.00"
                    prix_ttc_display = f"{prix_ht * 1.2:.2f}" if prix_ht else "0.00"  # Assuming 20% VAT
                    marge_display = f"{marge:.2f}" if marge else "0.00"

                    # Insert into MARCHÉS bordereau de prix tree
                    self.marche_details_tree.insert('', tk.END, values=(
                        str(n_ligne), designation, u_display, qte_display,
                        prix_achat_display, total_achat_display, prix_ht_display,
                        prix_ttc_display, marge_display
                    ))

                    print(f"➕ Added detail: {designation} - Qty: {qte_display}")

            print(f"✅ MARCHÉS details populated successfully")

        except Exception as e:
            print(f"❌ Error populating MARCHÉS details: {e}")

    def clear_automatic_marche_details(self):
        """Clear automatic entries from MARCHÉS bordereau de prix"""
        try:
            if hasattr(self, 'marche_details_tree'):
                # For now, clear all entries - in future could be more selective
                for item in self.marche_details_tree.get_children():
                    self.marche_details_tree.delete(item)
                print("🧹 Cleared existing MARCHÉS details")
        except Exception as e:
            print(f"Error clearing MARCHÉS details: {e}")

    def add_marche_detail_row(self):
        """Ajouter une ligne de détail au marché"""
        try:
            if hasattr(self, 'marche_details_tree'):
                # Get next line number
                existing_items = self.marche_details_tree.get_children()
                next_line = len(existing_items) + 1

                # Add new row with default values
                self.marche_details_tree.insert('', tk.END, values=(
                    str(next_line), "AUTOMATIQUE", "automatique", "AUTO",
                    "AUTO / MANUEL", "ACHAT X QTE", "AUTO / MANUEL", "AUTO / MANUEL", "VENT - ACHAT"
                ))
                print(f"➕ Added new MARCHÉS detail row: {next_line}")
        except Exception as e:
            print(f"Error adding MARCHÉS detail row: {e}")

    def edit_marche_detail_row(self):
        """Modifier une ligne de détail du marché"""
        try:
            if hasattr(self, 'marche_details_tree'):
                selection = self.marche_details_tree.selection()
                if selection:
                    messagebox.showinfo("Modification", "Fonctionnalité de modification en cours de développement")
                else:
                    messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à modifier!")
        except Exception as e:
            print(f"Error editing MARCHÉS detail row: {e}")

    def delete_marche_detail_row(self):
        """Supprimer une ligne de détail du marché"""
        try:
            if hasattr(self, 'marche_details_tree'):
                selection = self.marche_details_tree.selection()
                if selection:
                    if messagebox.askyesno("Confirmation", "Supprimer cette ligne?"):
                        self.marche_details_tree.delete(selection[0])
                        print("🗑️ Deleted MARCHÉS detail row")
                else:
                    messagebox.showwarning("Sélection", "Veuillez sélectionner une ligne à supprimer!")
        except Exception as e:
            print(f"Error deleting MARCHÉS detail row: {e}")

    def import_marche_from_devis(self):
        """Importer les détails depuis un DEVIS"""
        try:
            # Create a simple DEVIS selection dialog for import
            dialog = tk.Toplevel(self.root)
            dialog.title("📋 Importer depuis DEVIS")
            dialog.geometry("600x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # Center the dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
            y = (dialog.winfo_screenheight() // 2) - (400 // 2)
            dialog.geometry(f"600x400+{x}+{y}")

            main_frame = ttk.Frame(dialog, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(main_frame, text="📋 Sélectionner un DEVIS pour importer les détails",
                     style='Title.TLabel').pack(pady=(0, 15))

            # DEVIS list
            columns = ('n_devis', 'client', 'montant')
            devis_tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=12)

            devis_tree.heading('n_devis', text='N° DEVIS')
            devis_tree.heading('client', text='Client')
            devis_tree.heading('montant', text='Montant')

            devis_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # Load DEVIS
            self.cursor.execute("""
                SELECT n_devis, client,
                       (SELECT SUM(prix_total_ht * 1.2) FROM details_devis WHERE devis_id = devis.id) as montant_ttc
                FROM devis ORDER BY date_creation DESC LIMIT 20
            """)

            for devis in self.cursor.fetchall():
                n_devis, client, montant = devis
                montant_display = f"{montant:.2f} DH" if montant else "0.00 DH"
                devis_tree.insert('', tk.END, values=(n_devis, client, montant_display))

            # Buttons
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X)

            def import_details():
                selection = devis_tree.selection()
                if selection:
                    item_data = devis_tree.item(selection[0], 'values')
                    n_devis = item_data[0]

                    # Get DEVIS ID and import
                    self.cursor.execute("SELECT id FROM devis WHERE n_devis = ?", (n_devis,))
                    result = self.cursor.fetchone()
                    if result:
                        devis_id = result[0]
                        self.auto_populate_marche_details_from_devis(devis_id)
                        dialog.destroy()
                        messagebox.showinfo("Succès", f"✅ Détails importés depuis DEVIS {n_devis}")
                else:
                    messagebox.showwarning("Sélection", "Veuillez sélectionner un DEVIS!")

            ttk.Button(buttons_frame, text="✓ Importer", command=import_details,
                      style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(buttons_frame, text="❌ Annuler", command=dialog.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            print(f"Error in import MARCHÉS from DEVIS: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de l'import: {str(e)}")

    def setup_marche_cell_editing(self):
        """Configurer l'édition directe des cellules du marché"""
        # Variables pour l'édition MARCHÉS
        self.marche_editing_item = None
        self.marche_editing_column = None
        self.marche_edit_widget = None

        # Bind keyboard navigation for MARCHÉS
        self.marche_details_tree.bind('<Tab>', self.on_marche_tab_navigation)
        self.marche_details_tree.bind('<Return>', self.on_marche_enter_key)
        self.marche_details_tree.bind('<F2>', self.on_marche_f2_edit)

    def on_marche_cell_double_click(self, event):
        """Gérer le double-clic sur une cellule du marché - TOUTES les colonnes sont éditables"""
        # Identifier l'élément et la colonne cliquée
        item = self.marche_details_tree.identify_row(event.y)
        column = self.marche_details_tree.identify_column(event.x)

        if not item or not column:
            return

        # Sélectionner l'élément
        self.marche_details_tree.selection_set(item)
        self.marche_details_tree.focus(item)

        # Obtenir le nom de la colonne
        try:
            column_name = self.marche_details_tree.heading(column)['text']
        except:
            return

        print(f"🖱️ MARCHÉS Double-click sur colonne: {column_name} (#{column})")

        # Obtenir la position et taille de la cellule
        bbox = self.marche_details_tree.bbox(item, column)
        if not bbox:
            return

        # Obtenir la valeur actuelle
        values = self.marche_details_tree.item(item, 'values')
        column_index = int(column.replace('#', '')) - 1
        current_value = values[column_index] if column_index < len(values) else ""

        print(f"📝 MARCHÉS Édition de la cellule: {column_name} = '{current_value}'")

        # TOUTES les colonnes sont éditables - utiliser des éditeurs simples
        if column_name == 'Désignation':
            # Pour la désignation, utiliser l'éditeur texte simple
            self.edit_marche_text_cell(item, column, bbox, current_value, column_name)
        elif column_name == 'U':
            # Pour l'unité, utiliser l'éditeur avec bouton d'ajout
            self.edit_marche_unit_cell(item, column, bbox, current_value)
        else:
            # Pour tous les autres champs, utiliser l'éditeur texte simple
            self.edit_marche_text_cell(item, column, bbox, current_value, column_name)

        # Obtenir la position et taille de la cellule
        bbox = self.marche_details_tree.bbox(item, column)
        if not bbox:
            return

        # Obtenir la valeur actuelle
        values = self.marche_details_tree.item(item, 'values')
        column_index = int(column.replace('#', '')) - 1
        current_value = values[column_index] if column_index < len(values) else ""

        # Créer le widget d'édition selon la colonne avec le framework universel
        if column_name == 'Désignation':
            self.create_universal_cell_editor(self.marche_details_tree, item, column, bbox, current_value,
                                             column_name, "designation", callback=self.on_marche_cell_changed)
        elif column_name == 'U':
            self.create_universal_cell_editor(self.marche_details_tree, item, column, bbox, current_value,
                                             column_name, "unit", callback=self.on_marche_cell_changed)
        elif column_name in ['N° Ligne']:
            self.create_universal_cell_editor(self.marche_details_tree, item, column, bbox, current_value,
                                             column_name, "number", callback=self.on_marche_cell_changed)
        elif column_name in ['Qté', 'Prix achat HT', 'Total achat HT', 'Prix HT', 'Prix TTC', 'Marge']:
            self.create_universal_cell_editor(self.marche_details_tree, item, column, bbox, current_value,
                                             column_name, "number", callback=self.on_marche_cell_changed)
        else:
            self.create_universal_cell_editor(self.marche_details_tree, item, column, bbox, current_value,
                                             column_name, "text", callback=self.on_marche_cell_changed)

    def close_marche_current_dropdown(self):
        """Fermer le dropdown actuel du marché"""
        if hasattr(self, 'marche_edit_widget') and self.marche_edit_widget:
            self.marche_edit_widget.destroy()
            self.marche_edit_widget = None

    def edit_marche_unit_cell(self, item, column, bbox, current_value):
        """Éditer la cellule Unité du marché avec dropdown et bouton d'ajout"""
        # Supprimer le widget précédent s'il existe
        self.close_marche_current_dropdown()

        # Créer un frame pour contenir le combobox et le bouton
        edit_frame = tk.Frame(self.marche_details_tree)
        edit_frame.place(x=bbox[0], y=bbox[1], width=bbox[2] + 30, height=bbox[3])

        # Combobox pour les unités
        unit_var = tk.StringVar(value=current_value)
        unit_combo = ttk.Combobox(edit_frame, textvariable=unit_var,
                                 values=self.get_available_units(), width=8)
        unit_combo.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Bouton d'ajout d'unité
        add_unit_btn = ttk.Button(edit_frame, text="➕", width=3,
                                 command=lambda: self.open_unit_add_dialog(
                                     callback_function=lambda new_unit: self.refresh_unit_dropdown(unit_combo, new_unit),
                                     current_widget=unit_combo
                                 ))
        add_unit_btn.pack(side=tk.RIGHT, padx=(2, 0))

        self.marche_edit_widget = edit_frame
        self.marche_editing_item = item
        self.marche_editing_column = column

        def save_marche_unit_value():
            new_value = unit_var.get()
            values = list(self.marche_details_tree.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            if column_index < len(values):
                values[column_index] = new_value
                self.marche_details_tree.item(item, values=values)

            edit_frame.destroy()
            self.marche_edit_widget = None

        def cancel_marche_edit():
            edit_frame.destroy()
            self.marche_edit_widget = None

        # Bind events
        unit_combo.bind('<Return>', lambda e: save_marche_unit_value())
        unit_combo.bind('<Escape>', lambda e: cancel_marche_edit())
        unit_combo.bind('<FocusOut>', lambda e: save_marche_unit_value())

        # Focus on combobox
        unit_combo.focus()

    def edit_marche_text_cell(self, item, column, bbox, current_value, column_name):
        """Éditer une cellule texte normale du marché - VERSION SIMPLIFIÉE"""
        print(f"📝 MARCHÉS Creating text editor for: {column_name} = '{current_value}'")

        # Supprimer le widget précédent s'il existe
        self.close_marche_current_dropdown()

        # Créer le widget d'édition simple
        edit_var = tk.StringVar(value=current_value)
        edit_entry = tk.Entry(self.marche_details_tree, textvariable=edit_var,
                             font=('Segoe UI', 9), relief='solid', borderwidth=1)
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])

        self.marche_edit_widget = edit_entry
        self.marche_editing_item = item
        self.marche_editing_column = column

        def save_marche_text_value():
            try:
                new_value = edit_var.get()
                print(f"💾 MARCHÉS Saving: {column_name} = '{new_value}'")

                values = list(self.marche_details_tree.item(item, 'values'))
                column_index = int(column.replace('#', '')) - 1

                if column_index < len(values):
                    values[column_index] = new_value
                    self.marche_details_tree.item(item, values=values)
                    print(f"✅ MARCHÉS Cell updated successfully")

                    # Auto-calculate if it's a numeric field
                    if column_name in ['Qté', 'Prix achat HT', 'Prix HT', 'Marge']:
                        self.auto_calculate_marche_row(item, values)

                edit_entry.destroy()
                self.marche_edit_widget = None

            except Exception as e:
                print(f"❌ MARCHÉS Error saving: {e}")

        def cancel_marche_text_edit():
            print("❌ MARCHÉS Edit cancelled")
            edit_entry.destroy()
            self.marche_edit_widget = None

        # Bind events
        edit_entry.bind('<Return>', lambda e: save_marche_text_value())
        edit_entry.bind('<Escape>', lambda e: cancel_marche_text_edit())
        edit_entry.bind('<FocusOut>', lambda e: save_marche_text_value())

        # Focus and select all - TRÈS IMPORTANT
        edit_entry.focus_set()
        edit_entry.select_range(0, tk.END)
        print(f"🎯 MARCHÉS Focus set on text editor")

    def auto_calculate_marche_row(self, item, values):
        """Auto-calculate totals for MARCHÉS row"""
        try:
            # Get numeric values
            qte = float(values[3]) if values[3] and values[3] not in ['AUTO', ''] else 0.0
            prix_achat = float(values[4]) if values[4] and values[4] not in ['AUTO / MANUEL', ''] else 0.0
            prix_ht = float(values[6]) if values[6] and values[6] not in ['AUTO / MANUEL', ''] else 0.0

            # Calculate totals
            total_achat = prix_achat * qte
            prix_ttc = prix_ht * 1.2  # Assuming 20% VAT
            marge = prix_ht - prix_achat if prix_achat > 0 else 0.0

            # Update calculated fields
            values[5] = f"{total_achat:.2f}"  # Total achat HT
            values[7] = f"{prix_ttc:.2f}"     # Prix TTC
            values[8] = f"{marge:.2f}"        # Marge

            # Update the tree item
            self.marche_details_tree.item(item, values=values)

        except (ValueError, TypeError):
            pass  # Ignore calculation errors

    def on_marche_tab_navigation(self, event):
        """Handle Tab key navigation in MARCHÉS details"""
        try:
            current_selection = self.marche_details_tree.selection()
            if not current_selection:
                return

            current_item = current_selection[0]

            # Get all items
            all_items = self.marche_details_tree.get_children()
            current_index = all_items.index(current_item)

            # Move to next item
            if current_index < len(all_items) - 1:
                next_item = all_items[current_index + 1]
                self.marche_details_tree.selection_set(next_item)
                self.marche_details_tree.focus(next_item)

            return "break"  # Prevent default Tab behavior
        except Exception as e:
            print(f"Error in MARCHÉS tab navigation: {e}")

    def on_marche_enter_key(self, event):
        """Handle Enter key in MARCHÉS details - start editing current cell"""
        try:
            current_selection = self.marche_details_tree.selection()
            if current_selection:
                # Simulate double-click on first editable column (U)
                item = current_selection[0]
                bbox = self.marche_details_tree.bbox(item, '#3')  # U column
                if bbox:
                    self.edit_marche_unit_cell(item, '#3', bbox,
                                             self.marche_details_tree.item(item, 'values')[2])
            return "break"
        except Exception as e:
            print(f"Error in MARCHÉS enter key: {e}")

    def on_marche_f2_edit(self, event):
        """Handle F2 key - edit current cell"""
        try:
            current_selection = self.marche_details_tree.selection()
            if current_selection:
                # Start editing the designation column
                item = current_selection[0]
                bbox = self.marche_details_tree.bbox(item, '#2')  # Designation column
                if bbox:
                    self.close_marche_current_dropdown()
                    messagebox.showinfo("Sélection Produit", "Double-cliquez sur 'Désignation' pour sélectionner un produit.\nFonctionnalité complète en cours de développement.")
            return "break"
        except Exception as e:
            print(f"Error in MARCHÉS F2 edit: {e}")

    def on_marche_cell_changed(self, item, column, new_value):
        """Handle MARCHÉS cell value changes with auto-calculation and validation"""
        try:
            # Get current values
            values = list(self.marche_details_tree.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            # Update the specific cell
            if column_index < len(values):
                values[column_index] = new_value

                # Trigger auto-calculation for numeric fields
                if column_index in [3, 4, 6]:  # Qté, Prix achat HT, Prix HT
                    self.auto_calculate_marche_row(item, values)

                # Update tree display
                self.marche_details_tree.item(item, values=values)

                print(f"MARCHÉS cell updated: Column {column_index} = {new_value}")

        except Exception as e:
            print(f"Error handling MARCHÉS cell change: {e}")

    def save_marche_detail_to_database(self, item):
        """Save MARCHÉS detail row to database"""
        try:
            values = self.marche_details_tree.item(item, 'values')
            if len(values) >= 9:
                # Get the current MARCHÉS ID from the form
                current_marche = getattr(self, 'current_editing_marche', None)
                if not current_marche:
                    print("No current MARCHÉS context for saving")
                    return

                # Get MARCHÉS ID
                self.cursor.execute("SELECT id FROM marches WHERE n_marche=?", (current_marche,))
                result = self.cursor.fetchone()
                if not result:
                    print(f"MARCHÉS {current_marche} not found in database")
                    return

                marche_id = result[0]
                n_ligne = int(values[0]) if values[0] else 1

                # Check if detail already exists
                self.cursor.execute("""
                    SELECT id FROM details_marches WHERE marche_id=? AND n_ligne=?
                """, (marche_id, n_ligne))

                detail_values = (
                    marche_id, n_ligne, values[1], values[2],
                    float(values[3]) if values[3] else 0.0,
                    float(values[4]) if values[4] else 0.0,
                    float(values[5]) if values[5] else 0.0,
                    float(values[6]) if values[6] else 0.0,
                    float(values[7]) if values[7] else 0.0,
                    float(values[8]) if values[8] else 0.0
                )

                if self.cursor.fetchone():
                    # Update existing
                    self.cursor.execute("""
                        UPDATE details_marches SET designation=?, u=?, qte=?, prix_achat_ht=?,
                        total_achat_ht=?, prix_ht=?, prix_ttc=?, marge=?
                        WHERE marche_id=? AND n_ligne=?
                    """, detail_values[2:] + (marche_id, n_ligne))
                else:
                    # Insert new
                    self.cursor.execute("""
                        INSERT INTO details_marches (marche_id, n_ligne, designation, u, qte,
                        prix_achat_ht, total_achat_ht, prix_ht, prix_ttc, marge)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, detail_values)

                self.conn.commit()
                print(f"MARCHÉS detail saved: Line {n_ligne}")

        except Exception as e:
            print(f"Error saving MARCHÉS detail to database: {e}")

    def add_sample_marche_row(self):
        """Ajouter une ligne d'exemple au bordereau de prix"""
        try:
            if hasattr(self, 'marche_details_tree'):
                self.marche_details_tree.insert('', tk.END, values=(
                    "1", "AUTOMATIQUE", "automatique", "AUTO",
                    "AUTO / MANUEL", "ACHAT X QTE", "AUTO / MANUEL", "AUTO / MANUEL", "VENT - ACHAT"
                ))
        except Exception as e:
            print(f"Error adding sample MARCHÉS row: {e}")

    def trigger_auto_populate_marche(self, devis_var, client_var, nature_var, objet_var, montant_var):
        """Trigger auto-population manually from current DEVIS field"""
        current_devis = devis_var.get().strip()

        if not current_devis or current_devis.strip() == "" or current_devis in ["SÉLECTIONNÉ OU TAPER"]:
            messagebox.showwarning("DEVIS requis",
                "Veuillez d'abord sélectionner un DEVIS en utilisant le bouton 📋 ou saisir un numéro de DEVIS.")
            return

        # Confirm auto-population
        if messagebox.askyesno("Confirmation Auto-Population",
            f"Voulez-vous auto-remplir tous les champs automatiques avec les données du DEVIS {current_devis}?\n\n"
            f"⚠️ Cela remplacera les valeurs actuelles marquées comme 'AUTOMATIQUE'."):

            # Perform auto-population
            success = self.auto_populate_marche_from_devis(
                current_devis, devis_var, client_var, nature_var, objet_var, montant_var,
                populate_details=True, populate_client=True, populate_montant=True
            )

            if not success:
                messagebox.showerror("Erreur",
                    f"Impossible d'auto-remplir depuis le DEVIS {current_devis}.\n"
                    f"Vérifiez que le DEVIS existe et contient des données.")

    # ==========================================
    # UNIVERSAL CELL EDITING FRAMEWORK
    # ==========================================

    def create_universal_cell_editor(self, tree_widget, item, column, bbox, current_value, column_name, field_type="text", options=None, callback=None):
        """Universal cell editor that handles all field types with validation and persistence"""
        try:
            # Close any existing editor
            self.close_universal_editor(tree_widget)

            # Determine field type and create appropriate editor
            if field_type == "unit":
                return self.create_unit_editor(tree_widget, item, column, bbox, current_value, callback)
            elif field_type == "number":
                return self.create_number_editor(tree_widget, item, column, bbox, current_value, column_name, callback)
            elif field_type == "dropdown":
                return self.create_dropdown_editor(tree_widget, item, column, bbox, current_value, options, callback)
            elif field_type == "designation":
                return self.create_designation_editor(tree_widget, item, column, bbox, current_value, callback)
            else:  # text
                return self.create_text_editor(tree_widget, item, column, bbox, current_value, callback)

        except Exception as e:
            print(f"Error creating universal cell editor: {e}")
            return None

    def close_universal_editor(self, tree_widget):
        """Close any active editor for the given tree widget"""
        editor_attr = f"{tree_widget.winfo_name()}_edit_widget"
        if hasattr(self, editor_attr):
            editor = getattr(self, editor_attr)
            if editor:
                editor.destroy()
                setattr(self, editor_attr, None)

    def create_text_editor(self, tree_widget, item, column, bbox, current_value, callback=None):
        """Create a text editor for general text fields"""
        edit_var = tk.StringVar(value=current_value)
        edit_entry = tk.Entry(tree_widget, textvariable=edit_var, font=('Segoe UI', 9))
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])

        # Store editor reference
        editor_attr = f"{tree_widget.winfo_name()}_edit_widget"
        setattr(self, editor_attr, edit_entry)

        def save_value():
            new_value = edit_var.get()
            self.update_cell_value(tree_widget, item, column, new_value)
            if callback:
                callback(item, column, new_value)
            edit_entry.destroy()
            setattr(self, editor_attr, None)

        def cancel_edit():
            edit_entry.destroy()
            setattr(self, editor_attr, None)

        # Bind events
        edit_entry.bind('<Return>', lambda e: save_value())
        edit_entry.bind('<Escape>', lambda e: cancel_edit())
        edit_entry.bind('<FocusOut>', lambda e: save_value())

        # Focus and select all
        edit_entry.focus()
        edit_entry.select_range(0, tk.END)

        return edit_entry

    def create_number_editor(self, tree_widget, item, column, bbox, current_value, column_name, callback=None):
        """Create a number editor with validation for numeric fields"""
        # Clean current value for editing
        clean_value = str(current_value).replace(' DH', '').replace(',', '.').replace(' ', '')
        if clean_value in ['AUTO', 'MANUEL', 'AUTOMATIQUE', '']:
            clean_value = '0'

        edit_var = tk.StringVar(value=clean_value)
        edit_entry = tk.Entry(tree_widget, textvariable=edit_var, font=('Segoe UI', 9))
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])

        # Store editor reference
        editor_attr = f"{tree_widget.winfo_name()}_edit_widget"
        setattr(self, editor_attr, edit_entry)

        def validate_and_save():
            try:
                new_value = edit_var.get().replace(',', '.')
                # Validate numeric input
                if new_value.strip() == '':
                    new_value = '0'

                float_value = float(new_value)

                # Format based on column type
                if 'prix' in column_name.lower() or 'montant' in column_name.lower():
                    formatted_value = f"{float_value:.2f}"
                elif 'qte' in column_name.lower() or 'quantite' in column_name.lower():
                    formatted_value = f"{float_value:.0f}" if float_value == int(float_value) else f"{float_value:.2f}"
                elif 'marge' in column_name.lower():
                    formatted_value = f"{float_value:.2f}"
                else:
                    formatted_value = f"{float_value:.2f}"

                self.update_cell_value(tree_widget, item, column, formatted_value)
                if callback:
                    callback(item, column, formatted_value)

            except ValueError:
                messagebox.showerror("Erreur", f"Valeur numérique invalide: {edit_var.get()}")
                return False

            edit_entry.destroy()
            setattr(self, editor_attr, None)
            return True

        def cancel_edit():
            edit_entry.destroy()
            setattr(self, editor_attr, None)

        # Bind events
        edit_entry.bind('<Return>', lambda e: validate_and_save())
        edit_entry.bind('<Escape>', lambda e: cancel_edit())
        edit_entry.bind('<FocusOut>', lambda e: validate_and_save())

        # Focus and select all
        edit_entry.focus()
        edit_entry.select_range(0, tk.END)

        return edit_entry

    def create_unit_editor(self, tree_widget, item, column, bbox, current_value, callback=None):
        """Create a unit editor with dropdown and add button"""
        # Create frame for combobox and button
        edit_frame = tk.Frame(tree_widget)
        edit_frame.place(x=bbox[0], y=bbox[1], width=bbox[2] + 30, height=bbox[3])

        # Unit combobox
        unit_var = tk.StringVar(value=current_value)
        unit_combo = ttk.Combobox(edit_frame, textvariable=unit_var,
                                 values=self.get_available_units(), width=8)
        unit_combo.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add button
        add_unit_btn = ttk.Button(edit_frame, text="➕", width=3,
                                 command=lambda: self.open_unit_add_dialog(
                                     callback_function=lambda new_unit: self.refresh_unit_dropdown(unit_combo, new_unit),
                                     current_widget=unit_combo
                                 ))
        add_unit_btn.pack(side=tk.RIGHT, padx=(2, 0))

        # Store editor reference
        editor_attr = f"{tree_widget.winfo_name()}_edit_widget"
        setattr(self, editor_attr, edit_frame)

        def save_unit_value():
            new_value = unit_var.get()
            self.update_cell_value(tree_widget, item, column, new_value)
            if callback:
                callback(item, column, new_value)
            edit_frame.destroy()
            setattr(self, editor_attr, None)

        def cancel_edit():
            edit_frame.destroy()
            setattr(self, editor_attr, None)

        # Bind events
        unit_combo.bind('<Return>', lambda e: save_unit_value())
        unit_combo.bind('<Escape>', lambda e: cancel_edit())
        unit_combo.bind('<FocusOut>', lambda e: save_unit_value())

        # Focus on combobox
        unit_combo.focus()

        return edit_frame

    def update_cell_value(self, tree_widget, item, column, new_value):
        """Update cell value in tree widget"""
        try:
            values = list(tree_widget.item(item, 'values'))
            column_index = int(column.replace('#', '')) - 1

            if column_index < len(values):
                values[column_index] = new_value
                tree_widget.item(item, values=values)

                # Trigger auto-calculation if needed
                self.trigger_auto_calculation(tree_widget, item, values, column_index)

        except Exception as e:
            print(f"Error updating cell value: {e}")

    def trigger_auto_calculation(self, tree_widget, item, values, changed_column_index):
        """Trigger automatic calculations when numeric fields change"""
        try:
            tree_name = tree_widget.winfo_name()

            if 'devis' in tree_name:
                self.auto_calculate_devis_row(item, values, changed_column_index)
            elif 'bl' in tree_name or 'livraison' in tree_name:
                self.auto_calculate_bl_row(item, values, changed_column_index)
            elif 'marche' in tree_name:
                self.auto_calculate_marche_row(item, values)

        except Exception as e:
            print(f"Error in auto-calculation: {e}")

    def auto_calculate_devis_row(self, item, values, changed_column_index):
        """Auto-calculate DEVIS row totals when values change"""
        try:
            # DEVIS columns: N° Ligne, Désignation, U, Qté, Prix achat HT, Marge, Prix HT, Prix total HT
            if len(values) >= 8:
                qte = float(values[3]) if values[3] and values[3] not in ['AUTO', ''] else 0.0
                prix_achat = float(values[4]) if values[4] and values[4] not in ['AUTO', ''] else 0.0
                marge = float(values[5]) if values[5] and values[5] not in ['AUTO', ''] else 0.0

                # Calculate Prix HT (Prix achat + Marge)
                prix_ht = prix_achat + marge
                values[6] = f"{prix_ht:.2f}"

                # Calculate Prix total HT (Prix HT * Qté)
                prix_total = prix_ht * qte
                values[7] = f"{prix_total:.2f}"

                # Update the tree item
                self.devis_details_tree.item(item, values=values)

        except (ValueError, TypeError, IndexError):
            pass  # Ignore calculation errors

    def auto_calculate_bl_row(self, item, values, changed_column_index):
        """Auto-calculate BL row totals when values change"""
        try:
            # BL columns: N° Ligne, Désignation, U, Qté, Reste BC/MARCHÉ, Qté Livrée, Reste Stock
            if len(values) >= 7:
                qte = float(values[3]) if values[3] and values[3] not in ['AUTO', ''] else 0.0
                qte_livree = float(values[5]) if values[5] and values[5] not in ['AUTO', ''] else 0.0

                # Calculate Reste Stock (Qté - Qté Livrée)
                reste_stock = qte - qte_livree
                values[6] = f"{reste_stock:.0f}" if reste_stock == int(reste_stock) else f"{reste_stock:.2f}"

                # Update the tree item
                self.bl_details_tree.item(item, values=values)

        except (ValueError, TypeError, IndexError):
            pass  # Ignore calculation errors

    # ==========================================
    # UNIT MANAGEMENT SYSTEM
    # ==========================================

    def get_available_units(self):
        """Get list of available units for dropdowns"""
        return [
            "unité", "kg", "g", "t", "m", "cm", "mm", "m²", "cm²", "m³", "cm³",
            "litre", "ml", "pièce", "boîte", "paquet", "rouleau", "sac", "carton",
            "palette", "lot", "ensemble", "kit", "mètre linéaire", "heure", "jour"
        ]

    def open_unit_add_dialog(self, callback_function=None, current_widget=None):
        """Open dialog to add a new unit type"""
        dialog = tk.Toplevel(self.root)
        dialog.title("➕ Ajouter une Nouvelle Unité")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="📏 Ajouter une Nouvelle Unité", style='Title.TLabel')
        title_label.pack(pady=(0, 20))

        # New unit entry
        ttk.Label(main_frame, text="Nouvelle unité:", style='Heading.TLabel').pack(anchor=tk.W, pady=(0, 5))
        unit_var = tk.StringVar()
        unit_entry = ttk.Entry(main_frame, textvariable=unit_var, width=30, font=('Segoe UI', 11))
        unit_entry.pack(fill=tk.X, pady=(0, 15))

        # Examples
        examples_frame = ttk.LabelFrame(main_frame, text="💡 Exemples d'unités", padding="10")
        examples_frame.pack(fill=tk.X, pady=(0, 20))

        examples_text = """• Poids: kg, g, t
• Longueur: m, cm, mm, mètre linéaire
• Surface: m², cm²
• Volume: m³, cm³, litre, ml
• Quantité: pièce, boîte, paquet, lot
• Conditionnement: rouleau, sac, carton, palette
• Temps: heure, jour"""

        ttk.Label(examples_frame, text=examples_text, style='Info.TLabel').pack(anchor=tk.W)

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        def add_unit():
            new_unit = unit_var.get().strip()
            if not new_unit:
                messagebox.showerror("Erreur", "Veuillez saisir une unité!")
                return

            # Check if unit already exists
            current_units = self.get_available_units()
            if new_unit.lower() in [u.lower() for u in current_units]:
                messagebox.showwarning("Attention", f"L'unité '{new_unit}' existe déjà!")
                return

            # Add to the list (in a real app, this would be saved to database)
            current_units.append(new_unit)

            # Update the calling widget if provided
            if current_widget and hasattr(current_widget, 'configure'):
                try:
                    current_widget.configure(values=self.get_available_units())
                    current_widget.set(new_unit)  # Select the new unit
                except:
                    pass

            # Call callback function if provided
            if callback_function:
                callback_function(new_unit)

            dialog.destroy()
            messagebox.showinfo("Succès", f"✅ Unité '{new_unit}' ajoutée avec succès!")

        ttk.Button(buttons_frame, text="✓ Ajouter", command=add_unit,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="❌ Annuler", command=dialog.destroy).pack(side=tk.RIGHT)

        # Focus on entry
        unit_entry.focus()

        # Bind Enter key
        unit_entry.bind('<Return>', lambda e: add_unit())

    def create_unit_field_with_add_button(self, parent, unit_var, row=None, column=None, width=15):
        """Create a unit combobox with add button"""
        frame = ttk.Frame(parent)
        if row is not None and column is not None:
            frame.grid(row=row, column=column, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        else:
            frame.pack(fill=tk.X, pady=5)

        frame.columnconfigure(0, weight=1)

        # Unit combobox
        unit_combo = ttk.Combobox(frame, textvariable=unit_var, values=self.get_available_units(),
                                 width=width, state="normal")
        unit_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add button
        add_btn = ttk.Button(frame, text="➕", width=3,
                           command=lambda: self.open_unit_add_dialog(
                               callback_function=lambda new_unit: self.refresh_unit_dropdown(unit_combo, new_unit),
                               current_widget=unit_combo
                           ))
        add_btn.grid(row=0, column=1)

        return frame, unit_combo, add_btn

    def refresh_unit_dropdown(self, combo_widget, selected_unit=None):
        """Refresh unit dropdown with updated values"""
        try:
            combo_widget.configure(values=self.get_available_units())
            if selected_unit:
                combo_widget.set(selected_unit)
        except Exception as e:
            print(f"Error refreshing unit dropdown: {e}")

    def __del__(self):
        """Fermeture de la base de données"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    """Point d'entrée principal"""
    app = IsolocApp()
    app.run()

if __name__ == "__main__":
    main()
