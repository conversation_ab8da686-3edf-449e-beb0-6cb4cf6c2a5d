#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de mise à jour de la base de données Isoloc
"""

import sqlite3
import os
from pathlib import Path

def update_database():
    """Mettre à jour la structure de la base de données"""
    
    # C<PERSON>er le répertoire data s'il n'existe pas
    Path("data").mkdir(exist_ok=True)
    
    # Sauvegarder l'ancienne base si elle existe
    if os.path.exists("data/isoloc.db"):
        print("📁 Sauvegarde de l'ancienne base de données...")
        os.rename("data/isoloc.db", "data/isoloc_backup.db")
        print("✅ Sauvegarde créée: data/isoloc_backup.db")
    
    # Créer la nouvelle base de données
    print("🔧 Création de la nouvelle base de données...")
    conn = sqlite3.connect("data/isoloc.db")
    cursor = conn.cursor()
    
    # Créer la nouvelle table clients
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            nom TEXT NOT NULL,
            categorie TEXT NOT NULL DEFAULT 'Public',
            ice TEXT,
            if_field TEXT,
            adresse TEXT,
            personne_contact TEXT,
            contact TEXT,
            n_fix TEXT,
            n_fax TEXT,
            adresse_electronique TEXT,
            solde REAL DEFAULT 0.0,
            date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Table factures
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS factures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT UNIQUE NOT NULL,
            client_id INTEGER,
            date_facture DATE NOT NULL,
            date_echeance DATE,
            montant_ht REAL NOT NULL,
            tva REAL DEFAULT 20.0,
            montant_ttc REAL NOT NULL,
            statut TEXT DEFAULT 'En attente',
            notes TEXT,
            date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
    """)
    
    # Table dépenses
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS depenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            description TEXT NOT NULL,
            montant REAL NOT NULL,
            categorie TEXT,
            date_depense DATE NOT NULL,
            fournisseur TEXT,
            notes TEXT,
            date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Insérer des données d'exemple
    print("📊 Insertion des données d'exemple...")
    
    # Clients d'exemple
    clients_data = [
        ("C1", "Dupont Jean", "Public", "ICE123456789", "IF987654321", "123 Rue de la République, 75001 Paris", "Jean Dupont", "01 23 45 67 89", "01 23 45 67 90", "01 23 45 67 91", "<EMAIL>", 1250.50),
        ("C2", "Martin Marie", "Public", "ICE234567890", "IF876543210", "456 Avenue des Champs, 69000 Lyon", "Marie Martin", "01 98 76 54 32", "01 98 76 54 33", "01 98 76 54 34", "<EMAIL>", 890.00),
        ("C3", "Société ABC", "Privé", "", "", "", "Ahmed Benali", "01 11 22 33 44", "01 11 22 33 45", "01 11 22 33 46", "<EMAIL>", 2100.75),
        ("C4", "Entreprise XYZ", "Privé", "", "", "", "Fatima Alami", "01 55 66 77 88", "01 55 66 77 89", "01 55 66 77 90", "<EMAIL>", 0.00),
        ("C5", "Restaurant Le Bon Goût", "Public", "ICE555666777", "IF111222333", "15 Place de la Gastronomie, 33000 Bordeaux", "Chef Pierre", "05 56 78 90 12", "05 56 78 90 13", "05 56 78 90 14", "contact@bongoût.fr", 750.25)
    ]
    
    cursor.executemany("""
        INSERT INTO clients (code, nom, categorie, ice, if_field, adresse, personne_contact, contact, n_fix, n_fax, adresse_electronique, solde)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, clients_data)
    
    # Factures d'exemple
    factures_data = [
        ("FAC-2024-001", 1, "2024-01-15", "2024-02-15", 1041.67, 20.0, 1250.00, "Payée"),
        ("FAC-2024-002", 2, "2024-01-20", "2024-02-20", 741.67, 20.0, 890.00, "En attente"),
        ("FAC-2024-003", 3, "2024-01-25", "2024-02-25", 1750.00, 20.0, 2100.00, "En attente"),
        ("FAC-2024-004", 1, "2024-02-01", "2024-03-01", 625.00, 20.0, 750.00, "Brouillon"),
        ("FAC-2024-005", 5, "2024-02-05", "2024-03-05", 625.21, 20.0, 750.25, "En attente")
    ]
    
    cursor.executemany("""
        INSERT INTO factures (numero, client_id, date_facture, date_echeance, montant_ht, tva, montant_ttc, statut)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, factures_data)
    
    # Dépenses d'exemple
    depenses_data = [
        ("Fournitures de bureau", 150.00, "Bureau", "2024-01-10", "Bureau Plus"),
        ("Électricité", 280.50, "Utilities", "2024-01-15", "EDF"),
        ("Logiciel comptable", 99.00, "Logiciels", "2024-01-20", "SoftwareStore"),
        ("Déplacement client", 45.80, "Transport", "2024-01-25", "SNCF"),
        ("Matériel informatique", 1200.00, "Équipement", "2024-02-01", "TechStore"),
        ("Formation comptabilité", 350.00, "Formation", "2024-02-05", "Centre Formation")
    ]
    
    cursor.executemany("""
        INSERT INTO depenses (description, montant, categorie, date_depense, fournisseur)
        VALUES (?, ?, ?, ?, ?)
    """, depenses_data)
    
    conn.commit()
    conn.close()
    
    print("✅ Base de données mise à jour avec succès!")
    print("📊 Données d'exemple ajoutées:")
    print("   - 5 clients (3 publics avec ICE/IF/Adresse, 2 privés sans ICE/IF/Adresse)")
    print("   - 5 factures avec différents statuts")
    print("   - 6 dépenses dans diverses catégories")
    print()
    print("📋 Règles des catégories:")
    print("   - Public: ICE, IF, Adresse sont OBLIGATOIRES")
    print("   - Privé: ICE, IF, Adresse sont DÉSACTIVÉS")
    print()
    print("🚀 Vous pouvez maintenant lancer l'application:")
    print("   python run_app.py")

if __name__ == "__main__":
    print("=" * 60)
    print("    ISOLOC - Mise à jour de la Base de Données")
    print("=" * 60)
    print()
    
    try:
        update_database()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("💡 Vérifiez que l'application n'est pas en cours d'exécution.")
