#include "../../include/isoloc.h"

void create_sidebar(IsolocApp *app) {
    // Titre de la sidebar
    GtkWidget *sidebar_title = gtk_label_new("Navigation");
    gtk_widget_set_margin_top(sidebar_title, 10);
    gtk_widget_set_margin_bottom(sidebar_title, 10);
    gtk_style_context_add_class(gtk_widget_get_style_context(sidebar_title), "sidebar-title");
    gtk_box_pack_start(GTK_BOX(app->sidebar), sidebar_title, FALSE, FALSE, 0);
    
    // Créer la liste de navigation
    GtkWidget *nav_listbox = gtk_list_box_new();
    gtk_list_box_set_selection_mode(GTK_LIST_BOX(nav_listbox), GTK_SELECTION_SINGLE);
    gtk_box_pack_start(GTK_BOX(app->sidebar), nav_listbox, FALSE, FALSE, 0);
    
    // Connecter le signal de sélection
    g_signal_connect(nav_listbox, "row-selected", G_CALLBACK(on_sidebar_selection_changed), app);
    
    // Créer les éléments de navigation
    struct {
        const char *icon;
        const char *label;
        const char *page_name;
    } nav_items[] = {
        {"view-dashboard", "Tableau de Bord", "dashboard"},
        {"system-users", "Clients", "clients"},
        {"document-properties", "Factures", "factures"},
        {"folder-documents", "Dépenses", "depenses"},
        {"document-print", "Rapports", "rapports"},
        {NULL, NULL, NULL}
    };
    
    for (int i = 0; nav_items[i].icon != NULL; i++) {
        // Créer la ligne
        GtkWidget *row = gtk_list_box_row_new();
        g_object_set_data_full(G_OBJECT(row), "page-name", 
                              g_strdup(nav_items[i].page_name), g_free);
        
        // Créer le conteneur horizontal
        GtkWidget *box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 10);
        gtk_widget_set_margin_start(box, 15);
        gtk_widget_set_margin_end(box, 15);
        gtk_widget_set_margin_top(box, 8);
        gtk_widget_set_margin_bottom(box, 8);
        
        // Ajouter l'icône
        GtkWidget *icon = gtk_image_new_from_icon_name(nav_items[i].icon, GTK_ICON_SIZE_MENU);
        gtk_box_pack_start(GTK_BOX(box), icon, FALSE, FALSE, 0);
        
        // Ajouter le label
        GtkWidget *label = gtk_label_new(nav_items[i].label);
        gtk_widget_set_halign(label, GTK_ALIGN_START);
        gtk_box_pack_start(GTK_BOX(box), label, TRUE, TRUE, 0);
        
        // Ajouter le box à la ligne
        gtk_container_add(GTK_CONTAINER(row), box);
        
        // Ajouter la ligne à la listbox
        gtk_list_box_insert(GTK_LIST_BOX(nav_listbox), row, -1);
    }
    
    // Sélectionner le premier élément par défaut
    gtk_list_box_select_row(GTK_LIST_BOX(nav_listbox), 
                           gtk_list_box_get_row_at_index(GTK_LIST_BOX(nav_listbox), 0));
    
    // Ajouter un séparateur
    GtkWidget *separator = gtk_separator_new(GTK_ORIENTATION_HORIZONTAL);
    gtk_widget_set_margin_top(separator, 20);
    gtk_widget_set_margin_bottom(separator, 10);
    gtk_box_pack_start(GTK_BOX(app->sidebar), separator, FALSE, FALSE, 0);
    
    // Section Actions rapides
    GtkWidget *actions_title = gtk_label_new("Actions Rapides");
    gtk_widget_set_margin_bottom(actions_title, 10);
    gtk_style_context_add_class(gtk_widget_get_style_context(actions_title), "sidebar-title");
    gtk_box_pack_start(GTK_BOX(app->sidebar), actions_title, FALSE, FALSE, 0);
    
    // Boutons d'actions rapides
    GtkWidget *btn_nouveau_client = gtk_button_new_with_label("Nouveau Client");
    gtk_widget_set_margin_start(btn_nouveau_client, 10);
    gtk_widget_set_margin_end(btn_nouveau_client, 10);
    gtk_widget_set_margin_bottom(btn_nouveau_client, 5);
    g_signal_connect(btn_nouveau_client, "clicked", G_CALLBACK(on_nouveau_client_clicked), app);
    gtk_box_pack_start(GTK_BOX(app->sidebar), btn_nouveau_client, FALSE, FALSE, 0);
    
    GtkWidget *btn_nouvelle_facture = gtk_button_new_with_label("Nouvelle Facture");
    gtk_widget_set_margin_start(btn_nouvelle_facture, 10);
    gtk_widget_set_margin_end(btn_nouvelle_facture, 10);
    gtk_widget_set_margin_bottom(btn_nouvelle_facture, 5);
    g_signal_connect(btn_nouvelle_facture, "clicked", G_CALLBACK(on_nouvelle_facture_clicked), app);
    gtk_box_pack_start(GTK_BOX(app->sidebar), btn_nouvelle_facture, FALSE, FALSE, 0);
}
