// Application Isoloc - Script principal
class IsolocApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupChart();
        this.setupEventListeners();
        this.loadData();
    }

    // Configuration de la navigation
    setupNavigation() {
        const navButtons = document.querySelectorAll('.nav-btn');
        
        navButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetPage = e.currentTarget.dataset.page;
                this.navigateToPage(targetPage);
            });
        });
    }

    // Navigation entre les pages
    navigateToPage(pageName) {
        // Masquer toutes les pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // Désactiver tous les boutons de navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Afficher la page cible
        const targetPage = document.getElementById(pageName);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Activer le bouton de navigation correspondant
        const targetButton = document.querySelector(`[data-page="${pageName}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }

        this.currentPage = pageName;
        
        // Charger les données spécifiques à la page
        this.loadPageData(pageName);
    }

    // Configuration du graphique
    setupChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        // Données d'exemple pour le graphique
        const chartData = {
            labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            datasets: [{
                label: 'Revenus (€)',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: '#4F46E5',
                backgroundColor: 'rgba(79, 70, 229, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        };

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#E5E7EB'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('fr-FR') + ' €';
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        };

        new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: chartOptions
        });
    }

    // Configuration des écouteurs d'événements
    setupEventListeners() {
        // Bouton nouvelle facture
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-primary')) {
                const buttonText = e.target.closest('.btn-primary').textContent.trim();
                if (buttonText.includes('Nouvelle Facture')) {
                    this.showNewInvoiceModal();
                } else if (buttonText.includes('Nouveau Client')) {
                    this.showNewClientModal();
                }
            }
        });

        // Barre de recherche
        const searchInput = document.querySelector('.search-bar input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterClients(e.target.value);
            });
        }

        // Boutons d'action dans les tableaux
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-icon')) {
                const icon = e.target.closest('.btn-icon').querySelector('i');
                if (icon.classList.contains('fa-eye')) {
                    this.viewClient(e.target.closest('tr'));
                } else if (icon.classList.contains('fa-edit')) {
                    this.editClient(e.target.closest('tr'));
                } else if (icon.classList.contains('fa-trash')) {
                    this.deleteClient(e.target.closest('tr'));
                }
            }
        });
    }

    // Chargement des données
    loadData() {
        this.updateDashboardStats();
        this.loadRecentInvoices();
        this.loadClients();
    }

    // Chargement des données spécifiques à une page
    loadPageData(pageName) {
        switch (pageName) {
            case 'dashboard':
                this.updateDashboardStats();
                break;
            case 'clients':
                this.loadClients();
                break;
            case 'factures':
                this.loadInvoices();
                break;
            case 'depenses':
                this.loadExpenses();
                break;
            case 'rapports':
                this.loadReports();
                break;
        }
    }

    // Mise à jour des statistiques du tableau de bord
    updateDashboardStats() {
        // Simulation de données en temps réel
        const stats = {
            revenue: this.formatCurrency(45230.50 + Math.random() * 1000),
            pendingInvoices: Math.floor(8 + Math.random() * 5),
            activeClients: Math.floor(24 + Math.random() * 10),
            expenses: this.formatCurrency(8450.00 + Math.random() * 500)
        };

        // Mise à jour des valeurs dans l'interface
        const statValues = document.querySelectorAll('.stat-value');
        if (statValues.length >= 4) {
            statValues[0].textContent = stats.revenue;
            statValues[1].textContent = stats.pendingInvoices;
            statValues[2].textContent = stats.activeClients;
            statValues[3].textContent = stats.expenses;
        }
    }

    // Chargement des dernières factures
    loadRecentInvoices() {
        // Données d'exemple - en production, cela viendrait d'une API
        const invoices = [
            { number: 'FAC-2024-001', client: 'Client ABC', amount: 1250.00, status: 'paid' },
            { number: 'FAC-2024-002', client: 'Société XYZ', amount: 890.50, status: 'pending' },
            { number: 'FAC-2024-003', client: 'Entreprise 123', amount: 2100.00, status: 'pending' },
            { number: 'FAC-2024-004', client: 'Client DEF', amount: 750.25, status: 'draft' }
        ];

        this.renderInvoiceList(invoices);
    }

    // Rendu de la liste des factures
    renderInvoiceList(invoices) {
        const container = document.querySelector('.invoice-list');
        if (!container) return;

        container.innerHTML = invoices.map(invoice => `
            <div class="invoice-item">
                <div class="invoice-info">
                    <div class="invoice-number">${invoice.number}</div>
                    <div class="invoice-client">${invoice.client}</div>
                </div>
                <div class="invoice-amount">${this.formatCurrency(invoice.amount)}</div>
                <div class="invoice-status status-${invoice.status}">
                    ${this.getStatusText(invoice.status)}
                </div>
            </div>
        `).join('');
    }

    // Chargement des clients
    loadClients() {
        // Données d'exemple
        const clients = [
            {
                id: 1,
                name: 'Jean Dupont',
                company: 'Entreprise ABC',
                email: '<EMAIL>',
                phone: '01 23 45 67 89',
                balance: 1250.50,
                lastInvoice: '15/01/2024'
            },
            {
                id: 2,
                name: 'Marie Martin',
                company: 'Société XYZ',
                email: '<EMAIL>',
                phone: '01 98 76 54 32',
                balance: 890.00,
                lastInvoice: '20/01/2024'
            }
        ];

        this.renderClientsTable(clients);
    }

    // Rendu du tableau des clients
    renderClientsTable(clients) {
        const tbody = document.querySelector('.data-table tbody');
        if (!tbody) return;

        tbody.innerHTML = clients.map(client => `
            <tr data-client-id="${client.id}">
                <td>
                    <div class="client-info">
                        <div class="client-avatar">${this.getInitials(client.name)}</div>
                        <div>
                            <div class="client-name">${client.name}</div>
                            <div class="client-company">${client.company}</div>
                        </div>
                    </div>
                </td>
                <td>${client.email}</td>
                <td>${client.phone}</td>
                <td class="amount ${client.balance > 0 ? 'positive' : ''}">${this.formatCurrency(client.balance)}</td>
                <td>${client.lastInvoice}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" title="Voir">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Filtrage des clients
    filterClients(searchTerm) {
        const rows = document.querySelectorAll('.data-table tbody tr');
        
        rows.forEach(row => {
            const clientName = row.querySelector('.client-name')?.textContent.toLowerCase() || '';
            const clientEmail = row.cells[1]?.textContent.toLowerCase() || '';
            
            if (clientName.includes(searchTerm.toLowerCase()) || 
                clientEmail.includes(searchTerm.toLowerCase())) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // Modales et actions
    showNewInvoiceModal() {
        alert('Fonctionnalité "Nouvelle Facture" en cours de développement');
    }

    showNewClientModal() {
        alert('Fonctionnalité "Nouveau Client" en cours de développement');
    }

    viewClient(row) {
        const clientId = row.dataset.clientId;
        alert(`Voir le client ID: ${clientId}`);
    }

    editClient(row) {
        const clientId = row.dataset.clientId;
        alert(`Modifier le client ID: ${clientId}`);
    }

    deleteClient(row) {
        const clientId = row.dataset.clientId;
        if (confirm('Êtes-vous sûr de vouloir supprimer ce client ?')) {
            row.remove();
        }
    }

    // Fonctions utilitaires
    formatCurrency(amount) {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'EUR'
        }).format(amount);
    }

    getStatusText(status) {
        const statusMap = {
            'paid': 'Payée',
            'pending': 'En attente',
            'draft': 'Brouillon',
            'overdue': 'En retard'
        };
        return statusMap[status] || status;
    }

    getInitials(name) {
        return name.split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .substring(0, 2);
    }

    // Méthodes pour les autres pages (placeholders)
    loadInvoices() {
        console.log('Chargement des factures...');
    }

    loadExpenses() {
        console.log('Chargement des dépenses...');
    }

    loadReports() {
        console.log('Chargement des rapports...');
    }
}

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', () => {
    new IsolocApp();
});

// Animation de chargement
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});
