# Isoloc - Application de Comptabilité Professionnelle

## Description
Isoloc est une application de comptabilité professionnelle développée en C avec GTK+ pour l'interface graphique et SQLite pour le stockage des données. L'application fonctionne entièrement hors ligne et est conçue pour les petites et moyennes entreprises.

## Fonctionnalités
- 📊 Tableau de bord avec indicateurs financiers
- 👥 Gestion des clients et fournisseurs
- 🧾 Création et gestion des factures
- 💰 Suivi des dépenses et recettes
- 📈 Rapports financiers (Bilan, Compte de résultat)
- 💾 Sauvegarde automatique des données
- 🇫🇷 Interface entièrement en français

## Structure du Projet
```
isoloc/
├── src/
│   ├── main.c              # Point d'entrée principal
│   ├── ui/                 # Interface utilisateur GTK+
│   │   ├── main_window.c   # Fenêtre principale
│   │   ├── dashboard.c     # Tableau de bord
│   │   ├── clients.c       # Gestion des clients
│   │   ├── factures.c      # Gestion des factures
│   │   └── rapports.c      # Génération de rapports
│   ├── db/                 # Base de données
│   │   ├── database.c      # Connexion SQLite
│   │   └── models.c        # Modèles de données
│   └── utils/              # Utilitaires
│       ├── config.c        # Configuration
│       └── helpers.c       # Fonctions d'aide
├── include/                # Fichiers d'en-tête
├── data/                   # Base de données SQLite
├── resources/              # Ressources (icônes, CSS)
├── Makefile               # Compilation
└── README.md
```

## Prérequis
- GCC (GNU Compiler Collection)
- GTK+ 3.0 ou supérieur
- SQLite3
- pkg-config

### Installation des dépendances (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install build-essential
sudo apt-get install libgtk-3-dev
sudo apt-get install libsqlite3-dev
sudo apt-get install pkg-config
```

### Installation des dépendances (Windows avec MSYS2)
```bash
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-gtk3
pacman -S mingw-w64-x86_64-sqlite3
pacman -S mingw-w64-x86_64-pkg-config
```

## Compilation
```bash
make
```

## Exécution
```bash
./isoloc
```

## Licence
Propriétaire - Tous droits réservés

## Auteur
Développé pour votre entreprise
