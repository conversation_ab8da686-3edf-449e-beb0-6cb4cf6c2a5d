#include "../../include/isoloc.h"

void create_main_window(IsolocApp *app) {
    // Créer la fenêtre principale
    app->window = gtk_window_new(GTK_WINDOW_TOPLEVEL);
    gtk_window_set_title(GTK_WINDOW(app->window), "Isoloc - Comptabilité Professionnelle");
    gtk_window_set_default_size(GTK_WINDOW(app->window), 1200, 800);
    gtk_window_set_position(GTK_WINDOW(app->window), GTK_WIN_POS_CENTER);
    
    // Connecter le signal de fermeture
    g_signal_connect(app->window, "destroy", G_CALLBACK(gtk_main_quit), NULL);
    
    // Créer le conteneur principal
    GtkWidget *main_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 0);
    gtk_container_add(GTK_CONTAINER(app->window), main_box);
    
    // Ajouter la barre d'en-tête (sera créée dans create_header_bar)
    app->header_bar = gtk_header_bar_new();
    gtk_box_pack_start(GTK_BOX(main_box), app->header_bar, FALSE, FALSE, 0);
    
    // Créer le conteneur horizontal pour sidebar + contenu
    GtkWidget *content_box = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 0);
    gtk_box_pack_start(GTK_BOX(main_box), content_box, TRUE, TRUE, 0);
    
    // Créer la sidebar (sera remplie dans create_sidebar)
    app->sidebar = gtk_box_new(GTK_ORIENTATION_VERTICAL, 0);
    gtk_widget_set_size_request(app->sidebar, 250, -1);
    gtk_style_context_add_class(gtk_widget_get_style_context(app->sidebar), "sidebar");
    gtk_box_pack_start(GTK_BOX(content_box), app->sidebar, FALSE, FALSE, 0);
    
    // Créer le séparateur
    GtkWidget *separator = gtk_separator_new(GTK_ORIENTATION_VERTICAL);
    gtk_box_pack_start(GTK_BOX(content_box), separator, FALSE, FALSE, 0);
    
    // Créer la zone de contenu principal
    app->content_area = gtk_box_new(GTK_ORIENTATION_VERTICAL, 0);
    gtk_box_pack_start(GTK_BOX(content_box), app->content_area, TRUE, TRUE, 0);
    
    // Créer le stack pour les différentes pages
    app->main_stack = gtk_stack_new();
    gtk_stack_set_transition_type(GTK_STACK(app->main_stack), GTK_STACK_TRANSITION_TYPE_SLIDE_LEFT_RIGHT);
    gtk_box_pack_start(GTK_BOX(app->content_area), app->main_stack, TRUE, TRUE, 0);
}

void create_header_bar(IsolocApp *app) {
    gtk_header_bar_set_show_close_button(GTK_HEADER_BAR(app->header_bar), TRUE);
    gtk_header_bar_set_title(GTK_HEADER_BAR(app->header_bar), "Isoloc");
    gtk_header_bar_set_subtitle(GTK_HEADER_BAR(app->header_bar), "Comptabilité Professionnelle");
    
    // Bouton Nouveau (à gauche)
    GtkWidget *new_button = gtk_button_new_from_icon_name("document-new", GTK_ICON_SIZE_BUTTON);
    gtk_widget_set_tooltip_text(new_button, "Nouveau document");
    gtk_header_bar_pack_start(GTK_HEADER_BAR(app->header_bar), new_button);
    
    // Bouton Sauvegarder
    GtkWidget *save_button = gtk_button_new_from_icon_name("document-save", GTK_ICON_SIZE_BUTTON);
    gtk_widget_set_tooltip_text(save_button, "Sauvegarder");
    gtk_header_bar_pack_start(GTK_HEADER_BAR(app->header_bar), save_button);
    
    // Bouton Menu (à droite)
    GtkWidget *menu_button = gtk_button_new_from_icon_name("open-menu", GTK_ICON_SIZE_BUTTON);
    gtk_widget_set_tooltip_text(menu_button, "Menu principal");
    gtk_header_bar_pack_end(GTK_HEADER_BAR(app->header_bar), menu_button);
    
    // Bouton Paramètres
    GtkWidget *settings_button = gtk_button_new_from_icon_name("preferences-system", GTK_ICON_SIZE_BUTTON);
    gtk_widget_set_tooltip_text(settings_button, "Paramètres");
    gtk_header_bar_pack_end(GTK_HEADER_BAR(app->header_bar), settings_button);
}
