# Isoloc - Interface Web

Interface utilisateur moderne pour l'application de comptabilité Isoloc.

## 🎨 Aperçu du Design

Cette interface web présente un design moderne et professionnel avec :

- **Navigation intuitive** avec onglets dans la barre supérieure
- **Tableau de bord** avec statistiques en temps réel
- **Cartes de données** colorées et informatives
- **Graphiques interactifs** pour visualiser les revenus
- **Tables responsives** pour la gestion des clients
- **Design adaptatif** pour tous les écrans

## 🚀 Démarrage Rapide

### Méthode 1: Serveur Python (Recommandé)

```bash
cd web_ui
python server.py
```

L'interface s'ouvrira automatiquement dans votre navigateur à l'adresse `http://localhost:8080`

### Méthode 2: Serveur personnalisé

```bash
cd web_ui
python server.py 3000  # Port personnalisé
```

### Méthode 3: Ouverture directe

Ouvrez simplement le fichier `index.html` dans votre navigateur.

## 📱 Fonctionnalités

### ✅ Implémentées

- **Tableau de Bord**
  - Statistiques financières en temps réel
  - Graphique d'évolution des revenus
  - Liste des dernières factures
  - Cartes de données colorées

- **Gestion des Clients**
  - Liste complète des clients
  - Recherche et filtrage
  - Actions (voir, modifier, supprimer)
  - Interface responsive

- **Navigation**
  - Menu principal intuitif
  - Transitions fluides entre pages
  - Indicateurs visuels de la page active

### 🚧 En Développement

- **Gestion des Factures**
  - Création de nouvelles factures
  - Modification et suppression
  - Gestion des statuts

- **Gestion des Dépenses**
  - Enregistrement des dépenses
  - Catégorisation
  - Rapports de dépenses

- **Rapports Financiers**
  - Génération de rapports PDF
  - Graphiques avancés
  - Export de données

## 🎨 Palette de Couleurs

```css
--primary-color: #4F46E5    /* Bleu principal */
--success-color: #10B981    /* Vert (revenus) */
--warning-color: #F59E0B    /* Orange (alertes) */
--danger-color: #EF4444     /* Rouge (dépenses) */
--background-color: #F9FAFB /* Arrière-plan */
--surface-color: #FFFFFF    /* Cartes */
```

## 📊 Composants Principaux

### 1. Header (Barre de Navigation)
- Logo Isoloc
- Menu de navigation principal
- Notifications et profil utilisateur

### 2. Dashboard (Tableau de Bord)
- Cartes de statistiques
- Graphique des revenus (Chart.js)
- Liste des dernières factures

### 3. Pages de Gestion
- Interface clients avec recherche
- Tables responsives
- Actions contextuelles

## 🛠️ Technologies Utilisées

- **HTML5** - Structure sémantique
- **CSS3** - Design moderne avec variables CSS
- **JavaScript ES6+** - Interactivité et logique métier
- **Chart.js** - Graphiques interactifs
- **Font Awesome** - Icônes vectorielles
- **Google Fonts** - Typographie (Inter)

## 📱 Responsive Design

L'interface s'adapte automatiquement à tous les types d'écrans :

- **Desktop** (1200px+) - Interface complète
- **Tablet** (768px-1199px) - Navigation adaptée
- **Mobile** (< 768px) - Interface simplifiée

## 🎯 Structure des Fichiers

```
web_ui/
├── index.html          # Page principale
├── styles.css          # Styles CSS
├── script.js           # Logique JavaScript
├── server.py           # Serveur de développement
└── README.md           # Documentation
```

## 🔧 Personnalisation

### Modifier les Couleurs

Éditez les variables CSS dans `styles.css` :

```css
:root {
    --primary-color: #votre-couleur;
    --success-color: #votre-couleur;
    /* ... */
}
```

### Ajouter des Pages

1. Ajoutez un bouton dans la navigation (HTML)
2. Créez la section de page correspondante
3. Implémentez la logique dans `script.js`

### Modifier les Données

Les données sont actuellement simulées dans `script.js`. Pour connecter à une vraie base de données :

1. Remplacez les données d'exemple par des appels API
2. Implémentez la logique de sauvegarde
3. Ajoutez la gestion d'erreurs

## 🚀 Prochaines Étapes

1. **Intégration Backend** - Connexion avec l'API
2. **Authentification** - Système de connexion
3. **Notifications** - Alertes en temps réel
4. **Export PDF** - Génération de rapports
5. **Mode Sombre** - Thème alternatif
6. **PWA** - Application web progressive

## 📞 Support

Pour toute question ou suggestion concernant l'interface :

- Consultez la documentation du projet principal
- Vérifiez les issues GitHub
- Contactez l'équipe de développement

---

**Isoloc** - Comptabilité Professionnelle Moderne
