#include "../../include/isoloc.h"

int init_database(IsolocApp *app) {
    int rc;
    char *err_msg = 0;
    
    // Ouvrir la base de données
    rc = sqlite3_open("data/isoloc.db", &app->db);
    
    if (rc != SQLITE_OK) {
        g_print("Erreur d'ouverture de la base de données: %s\n", sqlite3_errmsg(app->db));
        sqlite3_close(app->db);
        return 1;
    }
    
    // Créer les tables si elles n'existent pas
    const char *sql_create_tables = 
        "CREATE TABLE IF NOT EXISTS clients ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "nom TEXT NOT NULL,"
        "prenom TEXT,"
        "email TEXT,"
        "telephone TEXT,"
        "adresse TEXT,"
        "solde REAL DEFAULT 0.0,"
        "date_creation DATETIME DEFAULT CURRENT_TIMESTAMP"
        ");"
        
        "CREATE TABLE IF NOT EXISTS factures ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "numero TEXT UNIQUE NOT NULL,"
        "client_id INTEGER,"
        "date_facture DATE NOT NULL,"
        "date_echeance DATE,"
        "montant_ht REAL NOT NULL,"
        "tva REAL DEFAULT 20.0,"
        "montant_ttc REAL NOT NULL,"
        "statut TEXT DEFAULT 'En attente',"
        "notes TEXT,"
        "date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,"
        "FOREIGN KEY (client_id) REFERENCES clients (id)"
        ");"
        
        "CREATE TABLE IF NOT EXISTS lignes_facture ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "facture_id INTEGER,"
        "description TEXT NOT NULL,"
        "quantite REAL DEFAULT 1.0,"
        "prix_unitaire REAL NOT NULL,"
        "montant REAL NOT NULL,"
        "FOREIGN KEY (facture_id) REFERENCES factures (id)"
        ");"
        
        "CREATE TABLE IF NOT EXISTS depenses ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "description TEXT NOT NULL,"
        "montant REAL NOT NULL,"
        "categorie TEXT,"
        "date_depense DATE NOT NULL,"
        "fournisseur TEXT,"
        "numero_facture TEXT,"
        "notes TEXT,"
        "date_creation DATETIME DEFAULT CURRENT_TIMESTAMP"
        ");"
        
        "CREATE TABLE IF NOT EXISTS parametres ("
        "cle TEXT PRIMARY KEY,"
        "valeur TEXT"
        ");";
    
    rc = sqlite3_exec(app->db, sql_create_tables, 0, 0, &err_msg);
    
    if (rc != SQLITE_OK) {
        g_print("Erreur de création des tables: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(app->db);
        return 1;
    }
    
    // Insérer des paramètres par défaut si nécessaire
    const char *sql_insert_defaults = 
        "INSERT OR IGNORE INTO parametres (cle, valeur) VALUES "
        "('nom_entreprise', 'Mon Entreprise'),"
        "('adresse_entreprise', '123 Rue de la Paix, 75001 Paris'),"
        "('telephone_entreprise', '01 23 45 67 89'),"
        "('email_entreprise', '<EMAIL>'),"
        "('siret', '12345678901234'),"
        "('tva_defaut', '20.0'),"
        "('devise', 'EUR'),"
        "('format_facture', 'FAC-{YYYY}-{NNN}');";
    
    rc = sqlite3_exec(app->db, sql_insert_defaults, 0, 0, &err_msg);
    
    if (rc != SQLITE_OK) {
        g_print("Erreur d'insertion des paramètres par défaut: %s\n", err_msg);
        sqlite3_free(err_msg);
        // Ne pas fermer la base pour cette erreur, continuer
    }
    
    // Insérer quelques données de test
    insert_sample_data(app);
    
    g_print("Base de données initialisée avec succès\n");
    return 0;
}

void close_database(IsolocApp *app) {
    if (app->db) {
        sqlite3_close(app->db);
        app->db = NULL;
        g_print("Base de données fermée\n");
    }
}

int insert_sample_data(IsolocApp *app) {
    char *err_msg = 0;
    int rc;
    
    // Vérifier si des données existent déjà
    const char *sql_check = "SELECT COUNT(*) FROM clients;";
    sqlite3_stmt *stmt;
    
    rc = sqlite3_prepare_v2(app->db, sql_check, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return 1;
    }
    
    rc = sqlite3_step(stmt);
    int count = 0;
    if (rc == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }
    sqlite3_finalize(stmt);
    
    // Si des clients existent déjà, ne pas insérer de données de test
    if (count > 0) {
        return 0;
    }
    
    // Insérer des clients de test
    const char *sql_clients = 
        "INSERT INTO clients (nom, prenom, email, telephone, adresse, solde) VALUES "
        "('Dupont', 'Jean', '<EMAIL>', '01 23 45 67 89', '123 Rue de la République, 75001 Paris', 1250.50),"
        "('Martin', 'Marie', '<EMAIL>', '01 98 76 54 32', '456 Avenue des Champs, 69000 Lyon', 890.00),"
        "('Société ABC', '', '<EMAIL>', '01 11 22 33 44', '789 Boulevard Central, 13000 Marseille', 2100.75),"
        "('Entreprise XYZ', '', '<EMAIL>', '01 55 66 77 88', '321 Place du Marché, 31000 Toulouse', 0.00);";
    
    rc = sqlite3_exec(app->db, sql_clients, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        g_print("Erreur d'insertion des clients de test: %s\n", err_msg);
        sqlite3_free(err_msg);
        return 1;
    }
    
    // Insérer des factures de test
    const char *sql_factures = 
        "INSERT INTO factures (numero, client_id, date_facture, date_echeance, montant_ht, tva, montant_ttc, statut) VALUES "
        "('FAC-2024-001', 1, '2024-01-15', '2024-02-15', 1041.67, 20.0, 1250.00, 'Payée'),"
        "('FAC-2024-002', 2, '2024-01-20', '2024-02-20', 741.67, 20.0, 890.00, 'En attente'),"
        "('FAC-2024-003', 3, '2024-01-25', '2024-02-25', 1750.00, 20.0, 2100.00, 'En attente'),"
        "('FAC-2024-004', 1, '2024-02-01', '2024-03-01', 625.00, 20.0, 750.00, 'Brouillon');";
    
    rc = sqlite3_exec(app->db, sql_factures, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        g_print("Erreur d'insertion des factures de test: %s\n", err_msg);
        sqlite3_free(err_msg);
        return 1;
    }
    
    g_print("Données de test insérées avec succès\n");
    return 0;
}
