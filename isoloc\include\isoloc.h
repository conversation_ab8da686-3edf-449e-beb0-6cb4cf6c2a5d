#ifndef ISOLOC_H
#define ISOLOC_H

#include <gtk/gtk.h>
#include <sqlite3.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Structures principales
typedef struct {
    GtkWidget *window;
    GtkWidget *header_bar;
    GtkWidget *main_stack;
    GtkWidget *sidebar;
    GtkWidget *content_area;
    sqlite3 *db;
} IsolocApp;

typedef struct {
    int id;
    char nom[100];
    char prenom[100];
    char email[100];
    char telephone[20];
    char adresse[200];
    double solde;
} Client;

typedef struct {
    int id;
    int client_id;
    char numero[50];
    char date[20];
    double montant_ht;
    double tva;
    double montant_ttc;
    char statut[20];
} Facture;

// Fonctions principales
IsolocApp* isoloc_app_new(void);
void isoloc_app_init(IsolocApp *app);
void isoloc_app_destroy(IsolocApp *app);

// Fonctions UI
void create_main_window(IsolocApp *app);
void create_header_bar(IsolocApp *app);
void create_sidebar(IsolocApp *app);
void setup_main_content(IsolocApp *app);

// Pages de l'application
GtkWidget* create_dashboard_page(IsolocApp *app);
GtkWidget* create_clients_page(IsolocApp *app);
GtkWidget* create_factures_page(IsolocApp *app);
GtkWidget* create_depenses_page(IsolocApp *app);
GtkWidget* create_rapports_page(IsolocApp *app);

// Callbacks
void on_sidebar_selection_changed(GtkListBox *listbox, GtkListBoxRow *row, gpointer user_data);
void on_nouveau_client_clicked(GtkButton *button, gpointer user_data);
void on_nouvelle_facture_clicked(GtkButton *button, gpointer user_data);
void on_quit_clicked(GtkButton *button, gpointer user_data);

// Base de données
int init_database(IsolocApp *app);
void close_database(IsolocApp *app);
int insert_sample_data(IsolocApp *app);

// Fonctions utilitaires pour les cartes du dashboard
GtkWidget* create_stat_card(const char *title, const char *value, const char *change, const char *color);
GtkWidget* create_recent_invoices_list(IsolocApp *app);

#endif // ISOLOC_H
