#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Structure pour les clients (en mémoire)
typedef struct {
    int id;
    char nom[100];
    char prenom[100];
    char email[100];
    char telephone[20];
    double solde;
} Client;

// Structure pour les factures (en mémoire)
typedef struct {
    int id;
    int client_id;
    char numero[50];
    char date[20];
    double montant_ht;
    double tva;
    double montant_ttc;
    char statut[20];
} Facture;

// Variables globales pour stocker les données
Client clients[100];
Facture factures[100];
int nb_clients = 0;
int nb_factures = 0;

// Fonctions
void initialiser_donnees_test();
void afficher_menu_principal();
void afficher_clients();
void afficher_factures();
void afficher_statistiques();
void ajouter_client();
void ajouter_facture();

int main() {
    int choix;
    
    printf("=================================================\n");
    printf("    ISOLOC - Application de Comptabilite\n");
    printf("         Version Demo Simplifiee\n");
    printf("=================================================\n\n");
    
    // Initialiser avec des données de test
    initialiser_donnees_test();
    
    printf("Application initialisee avec des donnees de test!\n\n");
    
    // Boucle principale
    while (1) {
        afficher_menu_principal();
        printf("Votre choix: ");
        scanf("%d", &choix);
        
        switch (choix) {
            case 1:
                afficher_statistiques();
                break;
            case 2:
                afficher_clients();
                break;
            case 3:
                afficher_factures();
                break;
            case 4:
                ajouter_client();
                break;
            case 5:
                ajouter_facture();
                break;
            case 0:
                printf("\nMerci d'avoir utilise Isoloc!\n");
                printf("Note: Cette version demo ne sauvegarde pas les donnees.\n");
                return 0;
            default:
                printf("\nChoix invalide! Veuillez reessayer.\n\n");
        }
        
        printf("\nAppuyez sur Entree pour continuer...");
        getchar();
        getchar(); // Pour capturer le \n restant
        system("cls"); // Nettoyer l'écran (Windows)
    }
}

void initialiser_donnees_test() {
    // Clients de test
    strcpy(clients[0].nom, "Dupont");
    strcpy(clients[0].prenom, "Jean");
    strcpy(clients[0].email, "<EMAIL>");
    strcpy(clients[0].telephone, "01 23 45 67 89");
    clients[0].id = 1;
    clients[0].solde = 1250.50;
    
    strcpy(clients[1].nom, "Martin");
    strcpy(clients[1].prenom, "Marie");
    strcpy(clients[1].email, "<EMAIL>");
    strcpy(clients[1].telephone, "01 98 76 54 32");
    clients[1].id = 2;
    clients[1].solde = 890.00;
    
    strcpy(clients[2].nom, "Societe ABC");
    strcpy(clients[2].prenom, "");
    strcpy(clients[2].email, "<EMAIL>");
    strcpy(clients[2].telephone, "01 11 22 33 44");
    clients[2].id = 3;
    clients[2].solde = 2100.75;
    
    nb_clients = 3;
    
    // Factures de test
    strcpy(factures[0].numero, "FAC-2024-001");
    factures[0].id = 1;
    factures[0].client_id = 1;
    strcpy(factures[0].date, "2024-01-15");
    factures[0].montant_ht = 1041.67;
    factures[0].tva = 20.0;
    factures[0].montant_ttc = 1250.00;
    strcpy(factures[0].statut, "Payee");
    
    strcpy(factures[1].numero, "FAC-2024-002");
    factures[1].id = 2;
    factures[1].client_id = 2;
    strcpy(factures[1].date, "2024-01-20");
    factures[1].montant_ht = 741.67;
    factures[1].tva = 20.0;
    factures[1].montant_ttc = 890.00;
    strcpy(factures[1].statut, "En attente");
    
    strcpy(factures[2].numero, "FAC-2024-003");
    factures[2].id = 3;
    factures[2].client_id = 3;
    strcpy(factures[2].date, "2024-01-25");
    factures[2].montant_ht = 1750.00;
    factures[2].tva = 20.0;
    factures[2].montant_ttc = 2100.00;
    strcpy(factures[2].statut, "En attente");
    
    nb_factures = 3;
}

void afficher_menu_principal() {
    printf("=== MENU PRINCIPAL ===\n");
    printf("1. Tableau de bord\n");
    printf("2. Gestion des clients\n");
    printf("3. Gestion des factures\n");
    printf("4. Ajouter un client\n");
    printf("5. Ajouter une facture\n");
    printf("0. Quitter\n");
    printf("======================\n");
}

void afficher_statistiques() {
    double ca_total = 0;
    int factures_attente = 0;
    
    printf("\n=== TABLEAU DE BORD ===\n");
    printf("Nombre de clients: %d\n", nb_clients);
    
    // Calculer le chiffre d'affaires et les factures en attente
    for (int i = 0; i < nb_factures; i++) {
        if (strcmp(factures[i].statut, "Payee") == 0) {
            ca_total += factures[i].montant_ttc;
        }
        if (strcmp(factures[i].statut, "En attente") == 0) {
            factures_attente++;
        }
    }
    
    printf("Chiffre d'affaires: %.2f EUR\n", ca_total);
    printf("Factures en attente: %d\n", factures_attente);
    printf("Nombre total de factures: %d\n", nb_factures);
    printf("=======================\n");
}

void afficher_clients() {
    printf("\n=== LISTE DES CLIENTS ===\n");
    printf("ID | Nom                | Email                  | Solde\n");
    printf("---|--------------------|-----------------------|----------\n");
    
    for (int i = 0; i < nb_clients; i++) {
        printf("%-3d| %-18s | %-21s | %8.2f\n",
            clients[i].id,
            clients[i].nom,
            clients[i].email,
            clients[i].solde);
    }
    printf("=========================\n");
}

void afficher_factures() {
    printf("\n=== LISTE DES FACTURES ===\n");
    printf("Numero        | Client     | Date       | Montant TTC | Statut\n");
    printf("--------------|------------|------------|-------------|----------\n");
    
    for (int i = 0; i < nb_factures; i++) {
        // Trouver le nom du client
        char nom_client[100] = "Inconnu";
        for (int j = 0; j < nb_clients; j++) {
            if (clients[j].id == factures[i].client_id) {
                strcpy(nom_client, clients[j].nom);
                break;
            }
        }
        
        printf("%-13s | %-10s | %-10s | %11.2f | %s\n",
            factures[i].numero,
            nom_client,
            factures[i].date,
            factures[i].montant_ttc,
            factures[i].statut);
    }
    printf("===========================\n");
}

void ajouter_client() {
    if (nb_clients >= 100) {
        printf("Erreur: Nombre maximum de clients atteint!\n");
        return;
    }
    
    printf("\n=== AJOUTER UN CLIENT ===\n");
    clients[nb_clients].id = nb_clients + 1;
    
    printf("Nom: ");
    scanf("%99s", clients[nb_clients].nom);
    printf("Prenom: ");
    scanf("%99s", clients[nb_clients].prenom);
    printf("Email: ");
    scanf("%99s", clients[nb_clients].email);
    printf("Telephone: ");
    scanf("%19s", clients[nb_clients].telephone);
    clients[nb_clients].solde = 0.0;
    
    nb_clients++;
    printf("Client ajoute avec succes! (ID: %d)\n", nb_clients);
}

void ajouter_facture() {
    if (nb_factures >= 100) {
        printf("Erreur: Nombre maximum de factures atteint!\n");
        return;
    }
    
    printf("\n=== AJOUTER UNE FACTURE ===\n");
    factures[nb_factures].id = nb_factures + 1;
    
    printf("Numero de facture: ");
    scanf("%49s", factures[nb_factures].numero);
    
    // Afficher les clients disponibles
    printf("\nClients disponibles:\n");
    for (int i = 0; i < nb_clients; i++) {
        printf("%d. %s %s\n", clients[i].id, clients[i].nom, clients[i].prenom);
    }
    
    printf("ID du client: ");
    scanf("%d", &factures[nb_factures].client_id);
    printf("Date (YYYY-MM-DD): ");
    scanf("%19s", factures[nb_factures].date);
    printf("Montant HT: ");
    scanf("%lf", &factures[nb_factures].montant_ht);
    
    factures[nb_factures].tva = 20.0;
    factures[nb_factures].montant_ttc = factures[nb_factures].montant_ht * (1 + factures[nb_factures].tva / 100);
    strcpy(factures[nb_factures].statut, "En attente");
    
    nb_factures++;
    printf("Facture ajoutee avec succes! Montant TTC: %.2f EUR\n", 
           factures[nb_factures-1].montant_ttc);
}
