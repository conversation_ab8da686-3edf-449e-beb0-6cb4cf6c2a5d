#include "../../include/isoloc.h"

void setup_main_content(IsolocApp *app) {
    // Ajouter toutes les pages au stack
    
    // Page Tableau de bord
    GtkWidget *dashboard_page = create_dashboard_page(app);
    gtk_stack_add_named(GTK_STACK(app->main_stack), dashboard_page, "dashboard");
    
    // Page Clients
    GtkWidget *clients_page = create_clients_page(app);
    gtk_stack_add_named(GTK_STACK(app->main_stack), clients_page, "clients");
    
    // Page Factures
    GtkWidget *factures_page = create_factures_page(app);
    gtk_stack_add_named(GTK_STACK(app->main_stack), factures_page, "factures");
    
    // Page Dépenses
    GtkWidget *depenses_page = create_depenses_page(app);
    gtk_stack_add_named(GTK_STACK(app->main_stack), depenses_page, "depenses");
    
    // Page Rapports
    GtkWidget *rapports_page = create_rapports_page(app);
    gtk_stack_add_named(GTK_STACK(app->main_stack), rapports_page, "rapports");
    
    // Afficher la page tableau de bord par défaut
    gtk_stack_set_visible_child_name(GTK_STACK(app->main_stack), "dashboard");
}

// Callback pour la sélection dans la sidebar
void on_sidebar_selection_changed(GtkListBox *listbox, GtkListBoxRow *row, gpointer user_data) {
    IsolocApp *app = (IsolocApp*)user_data;
    
    if (row == NULL) return;
    
    // Récupérer le nom de la page
    const char *page_name = (const char*)g_object_get_data(G_OBJECT(row), "page-name");
    
    if (page_name) {
        // Changer la page visible dans le stack
        gtk_stack_set_visible_child_name(GTK_STACK(app->main_stack), page_name);
    }
}

// Callbacks pour les boutons d'actions rapides
void on_nouveau_client_clicked(GtkButton *button, gpointer user_data) {
    IsolocApp *app = (IsolocApp*)user_data;
    
    // Changer vers la page clients
    gtk_stack_set_visible_child_name(GTK_STACK(app->main_stack), "clients");
    
    // TODO: Ouvrir le dialogue de nouveau client
    g_print("Nouveau client cliqué\n");
}

void on_nouvelle_facture_clicked(GtkButton *button, gpointer user_data) {
    IsolocApp *app = (IsolocApp*)user_data;
    
    // Changer vers la page factures
    gtk_stack_set_visible_child_name(GTK_STACK(app->main_stack), "factures");
    
    // TODO: Ouvrir le dialogue de nouvelle facture
    g_print("Nouvelle facture cliquée\n");
}

void on_quit_clicked(GtkButton *button, gpointer user_data) {
    gtk_main_quit();
}

// Pages simples (placeholders pour l'instant)
GtkWidget* create_clients_page(IsolocApp *app) {
    GtkWidget *page_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 20);
    gtk_widget_set_margin_start(page_box, 20);
    gtk_widget_set_margin_end(page_box, 20);
    gtk_widget_set_margin_top(page_box, 20);
    gtk_widget_set_margin_bottom(page_box, 20);
    
    GtkWidget *title = gtk_label_new("Gestion des Clients");
    gtk_style_context_add_class(gtk_widget_get_style_context(title), "title-1");
    gtk_widget_set_halign(title, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(page_box), title, FALSE, FALSE, 0);
    
    GtkWidget *content = gtk_label_new("Interface de gestion des clients\n(À développer)");
    gtk_widget_set_halign(content, GTK_ALIGN_CENTER);
    gtk_widget_set_valign(content, GTK_ALIGN_CENTER);
    gtk_box_pack_start(GTK_BOX(page_box), content, TRUE, TRUE, 0);
    
    return page_box;
}

GtkWidget* create_factures_page(IsolocApp *app) {
    GtkWidget *page_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 20);
    gtk_widget_set_margin_start(page_box, 20);
    gtk_widget_set_margin_end(page_box, 20);
    gtk_widget_set_margin_top(page_box, 20);
    gtk_widget_set_margin_bottom(page_box, 20);
    
    GtkWidget *title = gtk_label_new("Gestion des Factures");
    gtk_style_context_add_class(gtk_widget_get_style_context(title), "title-1");
    gtk_widget_set_halign(title, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(page_box), title, FALSE, FALSE, 0);
    
    GtkWidget *content = gtk_label_new("Interface de gestion des factures\n(À développer)");
    gtk_widget_set_halign(content, GTK_ALIGN_CENTER);
    gtk_widget_set_valign(content, GTK_ALIGN_CENTER);
    gtk_box_pack_start(GTK_BOX(page_box), content, TRUE, TRUE, 0);
    
    return page_box;
}

GtkWidget* create_depenses_page(IsolocApp *app) {
    GtkWidget *page_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 20);
    gtk_widget_set_margin_start(page_box, 20);
    gtk_widget_set_margin_end(page_box, 20);
    gtk_widget_set_margin_top(page_box, 20);
    gtk_widget_set_margin_bottom(page_box, 20);
    
    GtkWidget *title = gtk_label_new("Gestion des Dépenses");
    gtk_style_context_add_class(gtk_widget_get_style_context(title), "title-1");
    gtk_widget_set_halign(title, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(page_box), title, FALSE, FALSE, 0);
    
    GtkWidget *content = gtk_label_new("Interface de gestion des dépenses\n(À développer)");
    gtk_widget_set_halign(content, GTK_ALIGN_CENTER);
    gtk_widget_set_valign(content, GTK_ALIGN_CENTER);
    gtk_box_pack_start(GTK_BOX(page_box), content, TRUE, TRUE, 0);
    
    return page_box;
}

GtkWidget* create_rapports_page(IsolocApp *app) {
    GtkWidget *page_box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 20);
    gtk_widget_set_margin_start(page_box, 20);
    gtk_widget_set_margin_end(page_box, 20);
    gtk_widget_set_margin_top(page_box, 20);
    gtk_widget_set_margin_bottom(page_box, 20);
    
    GtkWidget *title = gtk_label_new("Rapports Financiers");
    gtk_style_context_add_class(gtk_widget_get_style_context(title), "title-1");
    gtk_widget_set_halign(title, GTK_ALIGN_START);
    gtk_box_pack_start(GTK_BOX(page_box), title, FALSE, FALSE, 0);
    
    GtkWidget *content = gtk_label_new("Interface de génération de rapports\n(À développer)");
    gtk_widget_set_halign(content, GTK_ALIGN_CENTER);
    gtk_widget_set_valign(content, GTK_ALIGN_CENTER);
    gtk_box_pack_start(GTK_BOX(page_box), content, TRUE, TRUE, 0);
    
    return page_box;
}
